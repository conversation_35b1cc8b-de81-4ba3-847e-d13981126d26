/**
 * Loading and Error Handling Type Definitions
 * Comprehensive TypeScript interfaces for the loading and error handling system
 */

/**
 * Loading state interface for tracking individual loading operations
 */
export interface LoadingState {
  isLoading: boolean;
  message?: string;
  operation?: string;
  startTime?: number;
}

/**
 * Global loading manager state
 */
export interface GlobalLoadingState {
  activeOperations: Map<string, LoadingState>;
  isGlobalLoading: boolean;
  defaultMessage: string;
}

/**
 * Loading service configuration options
 */
export interface LoadingServiceConfig {
  defaultMessage: string;
  minDisplayTime: number; // Minimum time to show loading (prevents flashing)
  maxDisplayTime: number; // Maximum time before auto-hide
  enableDebugLogging: boolean;
}

/**
 * HTTP Error types following Capacitor HTTP patterns
 */
export interface HttpError {
  status: number;
  statusText?: string;
  message: string;
  data?: any;
  url?: string;
  timestamp: number;
  operation?: string;
}

/**
 * Error severity levels for different handling strategies
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Error handling configuration
 */
export interface ErrorHandlingConfig {
  showToast: boolean;
  showAlert: boolean;
  logError: boolean;
  severity: ErrorSeverity;
  retryable: boolean;
  customMessage?: string;
}

/**
 * Enhanced HTTP response interface extending Capacitor's HttpResponse
 */
export interface EnhancedHttpResponse<T = any> {
  data: T;
  status: number;
  headers: { [key: string]: string };
  ok: boolean;
  url?: string;
  operation?: string;
  duration?: number;
}

/**
 * HTTP request options with loading integration
 */
export interface HttpRequestOptions {
  headers?: { [key: string]: string };
  timeout?: number;
  showLoading?: boolean;
  loadingMessage?: string;
  operation?: string;
  errorHandling?: Partial<ErrorHandlingConfig>;
}

/**
 * Skeleton loader component props
 */
export interface SkeletonLoaderProps {
  // Layout configuration
  lines?: number;
  width?: string | number;
  height?: string | number;
  animated?: boolean;
  
  // Styling options
  className?: string;
  style?: React.CSSProperties;
  
  // Layout patterns
  variant?: 'text' | 'card' | 'list' | 'grid' | 'custom';
  
  // Card-specific options (when variant='card')
  cardConfig?: {
    hasHeader?: boolean;
    hasImage?: boolean;
    hasContent?: boolean;
    hasActions?: boolean;
  };
  
  // List-specific options (when variant='list')
  listConfig?: {
    items?: number;
    hasAvatar?: boolean;
    hasSecondaryText?: boolean;
  };
  
  // Grid-specific options (when variant='grid')
  gridConfig?: {
    columns?: number;
    rows?: number;
    gap?: string;
  };
  
  // Custom pattern (when variant='custom')
  customPattern?: SkeletonElement[];
}

/**
 * Individual skeleton element configuration
 */
export interface SkeletonElement {
  type: 'text' | 'circle' | 'rect';
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Loading context interface for React context
 */
export interface LoadingContextType {
  // Global loading state
  isLoading: boolean;
  loadingMessage: string;
  
  // Loading control methods
  showLoading: (operation?: string, message?: string) => string;
  hideLoading: (operationId: string) => void;
  hideAllLoading: () => void;
  
  // Operation tracking
  getActiveOperations: () => string[];
  isOperationLoading: (operation: string) => boolean;
}

/**
 * Error context interface for React context
 */
export interface ErrorContextType {
  // Error state
  lastError: HttpError | null;
  errorHistory: HttpError[];
  
  // Error handling methods
  handleError: (error: any, config?: Partial<ErrorHandlingConfig>) => void;
  clearError: () => void;
  clearErrorHistory: () => void;
  
  // Error utilities
  getErrorMessage: (error: any) => string;
  isRetryableError: (error: HttpError) => boolean;
}

/**
 * Toast notification configuration
 */
export interface ToastConfig {
  message: string;
  duration?: number;
  position?: 'top' | 'middle' | 'bottom';
  color?: 'primary' | 'secondary' | 'tertiary' | 'success' | 'warning' | 'danger' | 'light' | 'medium' | 'dark';
  buttons?: Array<{
    text: string;
    role?: 'cancel' | 'destructive';
    handler?: () => void;
  }>;
}

/**
 * Alert configuration for critical errors
 */
export interface AlertConfig {
  header: string;
  message: string;
  buttons?: Array<{
    text: string;
    role?: 'cancel' | 'destructive' | 'confirm';
    handler?: () => void;
  }>;
}

/**
 * HTTP interceptor configuration
 */
export interface HttpInterceptorConfig {
  enableLoading: boolean;
  enableErrorHandling: boolean;
  enableRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  enableDebugLogging: boolean;
}

/**
 * Network status interface
 */
export interface NetworkStatus {
  isOnline: boolean;
  connectionType?: 'wifi' | 'cellular' | 'none' | 'unknown';
  isSlowConnection?: boolean;
}

/**
 * Loading animation types for different scenarios
 */
export type LoadingAnimation = 'crescent' | 'dots' | 'bubbles' | 'circles' | 'circular' | 'lines' | 'lines-small';

/**
 * Loading overlay configuration
 */
export interface LoadingOverlayConfig {
  message?: string;
  spinner?: LoadingAnimation;
  duration?: number;
  showBackdrop?: boolean;
  backdropDismiss?: boolean;
  cssClass?: string;
  keyboardClose?: boolean;
  id?: string;
}
