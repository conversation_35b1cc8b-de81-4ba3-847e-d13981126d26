/**
 * Types Index
 * Export all TypeScript type definitions
 */

// Loading and Error Handling Types
export type {
  LoadingState,
  GlobalLoadingState,
  LoadingServiceConfig,
  HttpError,
  ErrorSeverity,
  ErrorHandlingConfig,
  EnhancedHttpResponse,
  HttpRequestOptions,
  SkeletonLoaderProps,
  SkeletonElement,
  LoadingContextType,
  ErrorContextType,
  ToastConfig,
  AlertConfig,
  HttpInterceptorConfig,
  NetworkStatus,
  LoadingAnimation,
  LoadingOverlayConfig,
} from "./loading.types";

// API Types
export type {
  LoginContext,
  EncodedLoginContext,
  Tenant,
  AvailableTenantsResponse,
  SchoolLevelSession,
  RelatedUser,
  RelatedUsersResponse,
  RelatedUsersParams,
  ApiError,
  ApiResponse,
  CachedUserProfile,
  CacheConfig,
  ApiEndpoint,
  ApiEndpoints,
} from "./api.types";

export { API_ENDPOINTS, DEFAULT_CACHE_CONFIG, CACHE_KEYS } from "./api.types";
