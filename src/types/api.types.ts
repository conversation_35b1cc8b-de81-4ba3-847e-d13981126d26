/**
 * API Types and Interfaces for Santillana API Integration
 * Defines TypeScript interfaces for API requests and responses
 */

// ============================================================================
// LOGIN CONTEXT TYPES
// ============================================================================

/**
 * Decoded login context structure
 * Contains user session information from the availabletenants endpoint
 */
export interface LoginContext {
  userId: string;
  roleId: string;
  schoolLevelSessionId: string;
  tenantId?: string;
  sessionData?: Record<string, any>;
}

/**
 * Encoded login context (base64 string)
 */
export type EncodedLoginContext = string;

// ============================================================================
// AVAILABLE TENANTS API TYPES
// ============================================================================

/**
 * Tenant information from the availabletenants endpoint
 */
export interface Tenant {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Response from GET /mcs/api-services/api-services/apiservice/users/me/availabletenants
 */
export interface AvailableTenantsResponse {
  tenants: Tenant[];
  loginContext: EncodedLoginContext;
  userInfo?: {
    userId: string;
    email: string;
    name?: string;
    roles?: string[];
  };
  metadata?: Record<string, any>;
}

// ============================================================================
// RELATED USERS (CHILDREN) API TYPES
// ============================================================================

/**
 * School level session information for a student
 */
export interface SchoolLevelSession {
  id: string;
  schoolId: string;
  schoolName: string;
  levelId: string;
  levelName: string;
  grade: string;
  academicYear?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * Related user (child/student) information from the API
 */
export interface RelatedUser {
  userId: string;
  name: string;
  surnames: string;
  login: string;
  image?: string;
  schoolLevelSessions: SchoolLevelSession[];
  isActive?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Response from GET /mcs/api-services/api-services/apiservice/users/{userId}/relatedusers
 */
export interface RelatedUsersResponse {
  relatedUsers: RelatedUser[];
  hasRelatedUsers: boolean;
  totalCount?: number;
  metadata?: Record<string, any>;
}

// ============================================================================
// API REQUEST TYPES
// ============================================================================

/**
 * Parameters for the related users API call
 */
export interface RelatedUsersParams {
  userId: string;
  loginContext: EncodedLoginContext;
}

/**
 * Common API error response structure
 */
export interface ApiError {
  code: string;
  message: string;
  details?: string;
  timestamp?: string;
  requestId?: string;
}

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  metadata?: {
    requestId?: string;
    timestamp?: string;
    version?: string;
  };
}

// ============================================================================
// CACHE TYPES
// ============================================================================

/**
 * Cached user profile data
 */
export interface CachedUserProfile {
  user: {
    id: string;
    name: string;
    email: string;
    phone?: string;
    role: string;
    avatar: string;
    memberSince: string;
  };
  children: RelatedUser[];
  loginContext: EncodedLoginContext;
  tenants: Tenant[];
  lastUpdated: string;
  expiresAt: string;
}

/**
 * Cache configuration
 */
export interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxAge: number; // Maximum age in milliseconds
  enableRefresh: boolean;
  refreshThreshold: number; // Refresh when cache age exceeds this percentage of TTL
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * API endpoint configuration
 */
export interface ApiEndpoint {
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  requiresAuth: boolean;
  timeout?: number;
}

/**
 * API endpoints configuration
 */
export interface ApiEndpoints {
  availableTenants: ApiEndpoint;
  relatedUsers: ApiEndpoint;
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * API endpoints configuration
 */
export const API_ENDPOINTS: ApiEndpoints = {
  availableTenants: {
    path: '/mcs/api-services/api-services/apiservice/users/me/availabletenants',
    method: 'GET',
    requiresAuth: true,
    timeout: 30000
  },
  relatedUsers: {
    path: '/mcs/api-services/api-services/apiservice/users/{userId}/relatedusers',
    method: 'GET',
    requiresAuth: true,
    timeout: 30000
  }
};

/**
 * Default cache configuration
 */
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  ttl: 15 * 60 * 1000, // 15 minutes
  maxAge: 60 * 60 * 1000, // 1 hour
  enableRefresh: true,
  refreshThreshold: 0.8 // Refresh when cache is 80% of TTL age
};

/**
 * Cache keys for Capacitor.Preferences
 */
export const CACHE_KEYS = {
  USER_PROFILE: 'santillana_user_profile',
  LOGIN_CONTEXT: 'santillana_login_context',
  TENANTS: 'santillana_tenants',
  LAST_SYNC: 'santillana_last_sync'
} as const;
