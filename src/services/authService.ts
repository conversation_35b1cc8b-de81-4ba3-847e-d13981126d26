import { InAppBrowser, DefaultSystemBrowserOptions } from '@capacitor/inappbrowser';
import { Capacitor, CapacitorHttp } from '@capacitor/core';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { AUTH_CONFIG, getRedirectUri } from '../config/auth';

export interface UserInfo {
  sub: string;
  name?: string;
  email?: string;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  email_verified?: boolean;
  locale?: string;
  zoneinfo?: string;
  updated_at?: number;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
  id_token?: string;
  scope?: string;
}

class AuthService {
  constructor() {
    console.log('🔐 [AUTH] AuthService inicializado con Browser + App URL Listener');
    console.log('🔐 [AUTH] - Authorization URL:', AUTH_CONFIG.authorizationEndpoint);
    console.log('🔐 [AUTH] - Token URL:', AUTH_CONFIG.tokenEndpoint);
    console.log('🔐 [AUTH] - Client ID:', AUTH_CONFIG.clientId);
    console.log('🔐 [AUTH] - Redirect URI:', getRedirectUri());
    console.log('🔐 [AUTH] - Scope:', AUTH_CONFIG.scope);
    console.log('🔐 [AUTH] - Platform:', Capacitor.getPlatform());
  }

  // Generar string aleatorio base64url-safe para PKCE según RFC 7636
  private generateCodeVerifier(): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    const array = new Uint8Array(43); // RFC 7636: mínimo 43 caracteres
    crypto.getRandomValues(array);
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }

  // Generar code challenge usando SHA-256 (compatible con Capacitor)
  private async generateCodeChallenge(verifier: string): Promise<string> {
    // Para simplificar y evitar problemas, usar el verifier directamente como challenge
    // Esto es menos seguro pero funcional para testing
    console.log('🔐 [AUTH] Usando verifier como challenge (modo testing)');
    const encoded = new TextEncoder().encode(verifier);
    return this.base64URLEncode(encoded.buffer as ArrayBuffer);
  }



  // Codificación base64url según RFC 7636
  private base64URLEncode(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let str = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      str += String.fromCharCode(bytes[i]);
    }
    return btoa(str)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  // Generar state aleatorio
  private generateState(): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }

  // Método principal para generar PKCE
  private async generatePKCE(): Promise<{ codeVerifier: string; codeChallenge: string; state: string }> {
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    const state = this.generateState();

    console.log('🔐 [AUTH] PKCE generado correctamente');
    console.log('🔐 [AUTH] Code verifier length:', codeVerifier.length);
    console.log('🔐 [AUTH] Code challenge length:', codeChallenge.length);
    console.log('🔐 [AUTH] Code verifier:', codeVerifier.substring(0, 10) + '...');
    console.log('🔐 [AUTH] Code challenge:', codeChallenge.substring(0, 10) + '...');
    console.log('🔐 [AUTH] State:', state.substring(0, 10) + '...');

    return { codeVerifier, codeChallenge, state };
  }

  /**
   * Inicia el proceso de autenticación OAuth2
   */
  async login(): Promise<UserInfo> {
    try {
      console.log('🔐 [AUTH] === INICIANDO LOGIN CON INAPPBROWSER (MODO SISTEMA) ===');
      console.log('🔐 [AUTH] Plataforma:', Capacitor.getPlatform());
      console.log('🔐 [AUTH] Es nativo:', Capacitor.isNativePlatform());

      // Generar solo state (sin PKCE para testing)
      const state = this.generateState();

      // Guardar parámetros temporales
      await Preferences.set({ key: 'authState', value: state });

      // Construir URL de autorización (SIN PKCE)
      const redirectUri = getRedirectUri();
      const authUrl = new URL(AUTH_CONFIG.authorizationEndpoint);
      authUrl.searchParams.append('client_id', AUTH_CONFIG.clientId);
      authUrl.searchParams.append('redirect_uri', redirectUri);
      authUrl.searchParams.append('response_type', 'code');
      authUrl.searchParams.append('scope', AUTH_CONFIG.scope);
      authUrl.searchParams.append('state', state);
      // NO agregar PKCE para testing

      console.log('🔐 [AUTH] Redirect URI sin codificar:', redirectUri);
      console.log('🔐 [AUTH] Redirect URI codificada:', encodeURIComponent(redirectUri));

      console.log('🔐 [AUTH] URL de autorización:', authUrl.toString());
      console.log('🔐 [AUTH] Redirect URI:', redirectUri);

      if (Capacitor.isNativePlatform()) {
        // Flujo nativo con InAppBrowser
        return await this.handleNativeFlow(authUrl.toString(), redirectUri);
      } else {
        // Flujo web (si es necesario)
        throw new Error('Web flow not implemented');
      }

    } catch (error) {
      console.error('🔐 [AUTH] === ERROR EN LOGIN ===');
      console.error('🔐 [AUTH] Error details:', error);
      console.error('🔐 [AUTH] Error message:', error instanceof Error ? error.message : 'Unknown error');
      console.error('🔐 [AUTH] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  /**
   * Maneja el flujo nativo con InAppBrowser en modo sistema
   */
  private async handleNativeFlow(authUrl: string, expectedRedirectUri: string): Promise<UserInfo> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('🔐 [AUTH] Ejecutando flujo nativo con InAppBrowser (modo sistema)...');

        // Configurar listener para deep links
        const listener = await App.addListener('appUrlOpen', async (event) => {
          console.log('🔐 [AUTH] Deep link recibido:', event.url);

          // Check if URL starts with our redirect URI (more permissive check)
          if (event.url.includes('localhost/callback')) {
            // Remover listener
            listener.remove();

            try {
              // Cerrar InAppBrowser
              await InAppBrowser.close();

              // Procesar callback
              const userInfo = await this.handleCallback(event.url);
              resolve(userInfo);
            } catch (error) {
              reject(error);
            }
          }
        });

        // Abrir InAppBrowser en modo sistema (usa el navegador del sistema)
        console.log('🔐 [AUTH] Abriendo InAppBrowser en modo sistema...');
        await InAppBrowser.openInSystemBrowser({
          url: authUrl,
          options: DefaultSystemBrowserOptions
        });

        console.log('🔐 [AUTH] InAppBrowser (modo sistema) abierto exitosamente');

      } catch (error) {
        console.error('🔐 [AUTH] Error en flujo nativo:', error);
        reject(error);
      }
    });
  }

  /**
   * Maneja el callback de OAuth2
   */
  private async handleCallback(callbackUrl: string): Promise<UserInfo> {
    try {
      console.log('🔐 [AUTH] === PROCESANDO CALLBACK ===');
      console.log('🔐 [AUTH] Callback URL:', callbackUrl);

      // Extraer parámetros de la URL
      const url = new URL(callbackUrl);
      const code = url.searchParams.get('code');
      const state = url.searchParams.get('state');
      const error = url.searchParams.get('error');

      if (error) {
        throw new Error(`OAuth2 error: ${error}`);
      }

      if (!code) {
        throw new Error('No authorization code received');
      }

      // Verificar state
      const savedState = await Preferences.get({ key: 'authState' });
      if (state !== savedState.value) {
        throw new Error('Invalid state parameter');
      }

      console.log('🔐 [AUTH] Authorization code recibido:', code.substring(0, 10) + '...');

      // Intercambiar código por tokens
      const tokenResponse = await this.exchangeCodeForTokens(code);

      // Guardar tokens
      await this.saveTokens(tokenResponse);

      // Obtener información del usuario
      const userInfo = await this.getUserInfo(tokenResponse.access_token);

      // Limpiar datos temporales
      await Preferences.remove({ key: 'authState' });

      console.log('🔐 [AUTH] === CALLBACK PROCESADO EXITOSAMENTE ===');
      return userInfo;

    } catch (error) {
      console.error('🔐 [AUTH] Error procesando callback:', error);
      throw error;
    }
  }

  /**
   * Intercambia el authorization code por tokens
   */
  private async exchangeCodeForTokens(code: string): Promise<TokenResponse> {
    try {
      console.log('🔐 [AUTH] === INTERCAMBIANDO CÓDIGO POR TOKENS (SIN PKCE) ===');

      // Preparar parámetros del request (SIN PKCE)
      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: AUTH_CONFIG.clientId,
        code: code,
        redirect_uri: getRedirectUri()
        // NO incluir code_verifier para testing
      });

      console.log('🔐 [AUTH] Enviando request al token endpoint...');

      // Realizar request HTTP
      const response = await CapacitorHttp.post({
        url: AUTH_CONFIG.tokenEndpoint,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        },
        data: body.toString()
      });

      if (response.status !== 200) {
        throw new Error(`Token request failed: ${response.status} ${JSON.stringify(response.data)}`);
      }

      console.log('🔐 [AUTH] Tokens obtenidos exitosamente');
      return response.data;

    } catch (error) {
      console.error('🔐 [AUTH] Error intercambiando código por tokens:', error);
      throw error;
    }
  }

  /**
   * Obtiene información del usuario usando el access token
   */
  private async getUserInfo(accessToken: string): Promise<UserInfo> {
    try {
      console.log('🔐 [AUTH] === OBTENIENDO INFO DEL USUARIO ===');

      const response = await CapacitorHttp.get({
        url: AUTH_CONFIG.userInfoEndpoint,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json'
        }
      });

      if (response.status !== 200) {
        throw new Error(`UserInfo request failed: ${response.status}`);
      }

      const userInfo = response.data;
      console.log('🔐 [AUTH] User info obtenida exitosamente');
      console.log('🔐 [AUTH] - Sub:', userInfo.sub);
      console.log('🔐 [AUTH] - Name:', userInfo.name);
      console.log('🔐 [AUTH] - Email:', userInfo.email);

      return userInfo;
    } catch (error) {
      console.error('🔐 [AUTH] Error obteniendo user info:', error);
      throw error;
    }
  }

  /**
   * Guarda los tokens en Preferences
   */
  private async saveTokens(tokens: TokenResponse): Promise<void> {
    try {
      console.log('🔐 [AUTH] === GUARDANDO TOKENS ===');
      
      await Preferences.set({ key: 'access_token', value: tokens.access_token });
      await Preferences.set({ key: 'token_type', value: tokens.token_type });
      await Preferences.set({ key: 'expires_in', value: tokens.expires_in.toString() });
      
      if (tokens.refresh_token) {
        await Preferences.set({ key: 'refresh_token', value: tokens.refresh_token });
      }
      
      if (tokens.id_token) {
        await Preferences.set({ key: 'id_token', value: tokens.id_token });
      }
      
      if (tokens.scope) {
        await Preferences.set({ key: 'scope', value: tokens.scope });
      }

      // Guardar timestamp de expiración
      const expiresAt = Date.now() + (tokens.expires_in * 1000);
      await Preferences.set({ key: 'expires_at', value: expiresAt.toString() });

      console.log('🔐 [AUTH] Tokens guardados exitosamente');
    } catch (error) {
      console.error('🔐 [AUTH] Error guardando tokens:', error);
      throw error;
    }
  }

  /**
   * Obtiene los tokens guardados
   */
  async getTokens(): Promise<TokenResponse | null> {
    try {
      const accessToken = await Preferences.get({ key: 'access_token' });
      
      if (!accessToken.value) {
        return null;
      }

      const tokenType = await Preferences.get({ key: 'token_type' });
      const expiresIn = await Preferences.get({ key: 'expires_in' });
      const refreshToken = await Preferences.get({ key: 'refresh_token' });
      const idToken = await Preferences.get({ key: 'id_token' });
      const scope = await Preferences.get({ key: 'scope' });

      return {
        access_token: accessToken.value,
        token_type: tokenType.value || 'Bearer',
        expires_in: parseInt(expiresIn.value || '3600'),
        refresh_token: refreshToken.value || undefined,
        id_token: idToken.value || undefined,
        scope: scope.value || undefined
      };
    } catch (error) {
      console.error('🔐 [AUTH] Error obteniendo tokens:', error);
      return null;
    }
  }

  /**
   * Verifica si el usuario está autenticado
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      const tokens = await this.getTokens();
      
      if (!tokens) {
        return false;
      }

      // Verificar si el token ha expirado
      const expiresAt = await Preferences.get({ key: 'expires_at' });
      if (expiresAt.value) {
        const expirationTime = parseInt(expiresAt.value);
        if (Date.now() >= expirationTime) {
          console.log('🔐 [AUTH] Token expirado');
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('🔐 [AUTH] Error verificando autenticación:', error);
      return false;
    }
  }

  /**
   * Cierra sesión
   */
  async logout(): Promise<void> {
    try {
      console.log('🔐 [AUTH] === INICIANDO LOGOUT ===');
      
      // Limpiar tokens de Preferences
      await Preferences.remove({ key: 'access_token' });
      await Preferences.remove({ key: 'token_type' });
      await Preferences.remove({ key: 'expires_in' });
      await Preferences.remove({ key: 'refresh_token' });
      await Preferences.remove({ key: 'id_token' });
      await Preferences.remove({ key: 'scope' });
      await Preferences.remove({ key: 'expires_at' });

      console.log('🔐 [AUTH] Logout completado exitosamente');
    } catch (error) {
      console.error('🔐 [AUTH] Error en logout:', error);
      throw error;
    }
  }

  /**
   * Obtiene el usuario actual si está autenticado
   */
  async getCurrentUser(): Promise<UserInfo | null> {
    try {
      const isAuth = await this.isAuthenticated();
      if (!isAuth) {
        return null;
      }

      const tokens = await this.getTokens();
      if (!tokens) {
        return null;
      }

      return await this.getUserInfo(tokens.access_token);
    } catch (error) {
      console.error('🔐 [AUTH] Error obteniendo usuario actual:', error);
      return null;
    }
  }

  /**
   * Obtiene el access token actual
   */
  async getAccessToken(): Promise<string | null> {
    try {
      const tokens = await this.getTokens();
      return tokens?.access_token || null;
    } catch (error) {
      console.error('🔐 [AUTH] Error obteniendo access token:', error);
      return null;
    }
  }
}

const authService = new AuthService();
export { authService };
export default authService;
