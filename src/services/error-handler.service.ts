/**
 * ErrorHandlerService - Comprehensive Error Handling
 * Handles all types of HTTP errors with user-friendly messages using native Ionic components
 * Integrates with LoadingService and works with Capacitor HTTP requests
 */

import { 
  HttpError, 
  ErrorHandlingConfig, 
  Error<PERSON>everity,
  ToastConfig,
  AlertConfig 
} from '../types/loading.types';
import { debugLog } from '../config/environment.config';
import { Capacitor } from '@capacitor/core';

/**
 * Error event types for error handling notifications
 */
type ErrorEventType = 'error-occurred' | 'error-handled' | 'error-dismissed';

interface ErrorEvent {
  type: ErrorEventType;
  error: HttpError;
  config: ErrorHandlingConfig;
  timestamp: number;
}

/**
 * ErrorHandlerService class for managing application-wide error handling
 */
class ErrorHandlerServiceClass {
  private errorHistory: HttpError[] = [];
  private listeners: Map<string, (event: ErrorEvent) => void> = new Map();
  private maxHistorySize = 50;
  private toastController: any = null;
  private alertController: any = null;

  constructor() {
    this.log('ErrorHandlerService initialized');
    this.initializeControllers();
  }

  /**
   * Initialize Ionic controllers for toast and alert
   */
  private async initializeControllers(): Promise<void> {
    try {
      // Import controllers directly from @ionic/core
      const { toastController, alertController } = await import('@ionic/core/components');
      this.toastController = toastController;
      this.alertController = alertController;
      this.log('Ionic controllers initialized');
    } catch (error) {
      console.error('Failed to initialize Ionic controllers:', error);

      // Fallback implementation if needed
      this.toastController = {
        create: async (options: any) => {
          console.warn('Toast fallback used:', options);
          return {
            present: async () => {},
            dismiss: async () => {}
          };
        }
      };

      this.alertController = {
        create: async (options: any) => {
          console.warn('Alert fallback used:', options);
          return {
            present: async () => {},
            dismiss: async () => {}
          };
        }
      };
    }
  }

  /**
   * Handle an error with specified configuration
   */
  async handleError(error: any, config: Partial<ErrorHandlingConfig> = {}): Promise<void> {
    const httpError = this.parseError(error);
    const errorConfig = this.getErrorConfig(httpError, config);

    // Add to error history
    this.addToHistory(httpError);

    // Log error if configured
    if (errorConfig.logError) {
      this.logError(httpError, errorConfig);
    }

    // Notify listeners
    this.notifyListeners('error-occurred', httpError, errorConfig);

    // Show user notification based on configuration
    if (errorConfig.showToast) {
      await this.showToast(httpError, errorConfig);
    } else if (errorConfig.showAlert) {
      await this.showAlert(httpError, errorConfig);
    }

    this.notifyListeners('error-handled', httpError, errorConfig);
  }

  /**
   * Parse any error into a standardized HttpError format
   */
  parseError(error: any): HttpError {
    const timestamp = Date.now();

    // Handle Capacitor HTTP errors
    if (error && typeof error === 'object') {
      if (error.status !== undefined) {
        return {
          status: error.status,
          statusText: error.statusText || this.getStatusText(error.status),
          message: error.message || this.getDefaultErrorMessage(error.status),
          data: error.data,
          url: error.url,
          timestamp,
          operation: error.operation
        };
      }

      // Handle fetch errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const typedError = error as any;
        return {
          status: 0,
          statusText: 'Network Error',
          message: 'Error de conexión. Verifica tu conexión a internet.',
          timestamp,
          operation: typedError.operation
        };
      }

      // Handle generic Error objects
      if (error instanceof Error) {
        const typedError = error as any;
        return {
          status: 0,
          statusText: 'Unknown Error',
          message: error.message || 'Ha ocurrido un error inesperado.',
          timestamp,
          operation: typedError.operation
        };
      }
    }

    // Fallback for unknown error types
    return {
      status: 0,
      statusText: 'Unknown Error',
      message: 'Ha ocurrido un error inesperado.',
      timestamp
    };
  }

  /**
   * Get error configuration based on error type and user preferences
   */
  private getErrorConfig(error: HttpError, userConfig: Partial<ErrorHandlingConfig>): ErrorHandlingConfig {
    const defaultConfig: ErrorHandlingConfig = {
      showToast: true,
      showAlert: false,
      logError: true,
      severity: this.determineSeverity(error),
      retryable: this.isRetryable(error),
      customMessage: userConfig.customMessage
    };

    // Adjust defaults based on error severity
    if (defaultConfig.severity === 'critical') {
      defaultConfig.showAlert = true;
      defaultConfig.showToast = false;
    }

    return { ...defaultConfig, ...userConfig };
  }

  /**
   * Determine error severity based on status code and context
   */
  private determineSeverity(error: HttpError): ErrorSeverity {
    if (error.status === 0) return 'high'; // Network errors
    if (error.status >= 500) return 'critical'; // Server errors
    if (error.status === 401 || error.status === 403) return 'high'; // Auth errors
    if (error.status === 404) return 'medium'; // Not found
    if (error.status >= 400) return 'medium'; // Client errors
    return 'low';
  }

  /**
   * Check if an error is retryable
   */
  private isRetryable(error: HttpError): boolean {
    // Network errors are retryable
    if (error.status === 0) return true;
    
    // Server errors (5xx) are retryable
    if (error.status >= 500) return true;
    
    // Rate limiting is retryable
    if (error.status === 429) return true;
    
    // Timeout errors are retryable
    if (error.status === 408) return true;
    
    return false;
  }

  /**
   * Show toast notification for error
   */
  private async showToast(error: HttpError, config: ErrorHandlingConfig): Promise<void> {
    if (!this.toastController) {
      console.warn('Toast controller not available');
      return;
    }

    const message = config.customMessage || this.getUserFriendlyMessage(error);
    const toastConfig: ToastConfig = {
      message,
      duration: this.getToastDuration(config.severity),
      position: 'bottom',
      color: this.getToastColor(config.severity),
      buttons: config.retryable ? [
        {
          text: 'Reintentar',
          handler: () => {
            this.notifyListeners('error-dismissed', error, config);
          }
        },
        {
          text: 'Cerrar',
          role: 'cancel'
        }
      ] : [
        {
          text: 'Cerrar',
          role: 'cancel'
        }
      ]
    };

    try {
      const toast = await this.toastController.create(toastConfig);
      await toast.present();
      this.log('Toast presented for error', { error, config });
    } catch (toastError) {
      console.error('Failed to show toast:', toastError);
    }
  }

  /**
   * Show alert dialog for critical errors
   */
  private async showAlert(error: HttpError, config: ErrorHandlingConfig): Promise<void> {
    if (!this.alertController) {
      console.warn('Alert controller not available');
      return;
    }

    const message = config.customMessage || this.getUserFriendlyMessage(error);
    const alertConfig: AlertConfig = {
      header: this.getAlertHeader(config.severity),
      message,
      buttons: config.retryable ? [
        {
          text: 'Reintentar',
          handler: () => {
            this.notifyListeners('error-dismissed', error, config);
          }
        },
        {
          text: 'Cancelar',
          role: 'cancel'
        }
      ] : [
        {
          text: 'Aceptar',
          role: 'confirm'
        }
      ]
    };

    try {
      const alert = await this.alertController.create(alertConfig);
      await alert.present();
      this.log('Alert presented for error', { error, config });
    } catch (alertError) {
      console.error('Failed to show alert:', alertError);
    }
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: HttpError): string {
    switch (error.status) {
      case 0:
        return 'Error de conexión. Verifica tu conexión a internet y vuelve a intentar.';
      case 400:
        return 'Los datos enviados no son válidos. Por favor, revisa la información.';
      case 401:
        return 'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.';
      case 403:
        return 'No tienes permisos para realizar esta acción.';
      case 404:
        return 'La información solicitada no se encontró.';
      case 408:
        return 'La solicitud tardó demasiado tiempo. Inténtalo de nuevo.';
      case 429:
        return 'Demasiadas solicitudes. Espera un momento antes de intentar nuevamente.';
      case 500:
        return 'Error interno del servidor. Inténtalo más tarde.';
      case 502:
      case 503:
      case 504:
        return 'El servicio no está disponible temporalmente. Inténtalo más tarde.';
      default:
        return error.message || 'Ha ocurrido un error inesperado. Inténtalo nuevamente.';
    }
  }

  /**
   * Get toast duration based on severity
   */
  private getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case 'low': return 3000;
      case 'medium': return 5000;
      case 'high': return 8000;
      case 'critical': return 0; // Manual dismiss
      default: return 5000;
    }
  }

  /**
   * Get toast color based on severity
   */
  private getToastColor(severity: ErrorSeverity): 'primary' | 'secondary' | 'tertiary' | 'success' | 'warning' | 'danger' | 'light' | 'medium' | 'dark' {
    switch (severity) {
      case 'low': return 'medium';
      case 'medium': return 'warning';
      case 'high': return 'danger';
      case 'critical': return 'danger';
      default: return 'medium';
    }
  }

  /**
   * Get alert header based on severity
   */
  private getAlertHeader(severity: ErrorSeverity): string {
    switch (severity) {
      case 'low': return 'Información';
      case 'medium': return 'Atención';
      case 'high': return 'Error';
      case 'critical': return 'Error Crítico';
      default: return 'Error';
    }
  }

  /**
   * Get default status text for HTTP status codes
   */
  private getStatusText(status: number): string {
    const statusTexts: { [key: number]: string } = {
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      408: 'Request Timeout',
      429: 'Too Many Requests',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable',
      504: 'Gateway Timeout'
    };
    return statusTexts[status] || 'Unknown Error';
  }

  /**
   * Get default error message for HTTP status codes
   */
  private getDefaultErrorMessage(status: number): string {
    return this.getUserFriendlyMessage({ status } as HttpError);
  }

  /**
   * Add error to history
   */
  private addToHistory(error: HttpError): void {
    this.errorHistory.unshift(error);
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * Get error history
   */
  getErrorHistory(): HttpError[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    this.log('Error history cleared');
  }

  /**
   * Subscribe to error events
   */
  subscribe(listenerId: string, callback: (event: ErrorEvent) => void): void {
    this.listeners.set(listenerId, callback);
    this.log(`Error listener subscribed: ${listenerId}`);
  }

  /**
   * Unsubscribe from error events
   */
  unsubscribe(listenerId: string): void {
    this.listeners.delete(listenerId);
    this.log(`Error listener unsubscribed: ${listenerId}`);
  }

  /**
   * Notify listeners of error events
   */
  private notifyListeners(type: ErrorEventType, error: HttpError, config: ErrorHandlingConfig): void {
    const event: ErrorEvent = {
      type,
      error,
      config,
      timestamp: Date.now()
    };

    this.listeners.forEach((callback, listenerId) => {
      try {
        callback(event);
      } catch (listenerError) {
        console.error(`Error in error listener ${listenerId}:`, listenerError);
      }
    });
  }

  /**
   * Log error with context
   */
  private logError(error: HttpError, config: ErrorHandlingConfig): void {
    const logLevel = config.severity === 'critical' ? 'error' : 'warn';
    const logData = {
      error,
      config,
      platform: Capacitor.getPlatform(),
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };

    if (logLevel === 'error') {
      console.error('[ErrorHandler] Critical error occurred:', logData);
    } else {
      console.warn('[ErrorHandler] Error occurred:', logData);
    }

    this.log(`Error logged with severity: ${config.severity}`, logData);
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    debugLog(`[ErrorHandler] ${message}`, data);
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    this.listeners.clear();
    this.errorHistory = [];
    this.log('ErrorHandlerService cleaned up');
  }
}

// Export singleton instance
export const ErrorHandlerService = new ErrorHandlerServiceClass();

// Export class for testing
export { ErrorHandlerServiceClass };

// Export types for convenience
export type { ErrorEvent, ErrorEventType };
