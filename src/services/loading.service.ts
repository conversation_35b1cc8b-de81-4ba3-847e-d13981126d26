/**
 * LoadingService - Global Loading State Management
 * Manages loading states throughout the application with support for multiple concurrent operations
 * Integrates with Capacitor HTTP and follows established service patterns
 */

import { 
  LoadingState, 
  GlobalLoadingState, 
  LoadingServiceConfig,
  LoadingOverlayConfig,
  LoadingAnimation
} from '../types/loading.types';
import { debugLog } from '../config/environment.config';

/**
 * Event types for loading state changes
 */
type LoadingEventType = 'loading-started' | 'loading-stopped' | 'loading-updated';

interface LoadingEvent {
  type: LoadingEventType;
  operationId: string;
  state: LoadingState;
  globalState: GlobalLoadingState;
}

/**
 * LoadingService class for managing application-wide loading states
 */
class LoadingServiceClass {
  private state: GlobalLoadingState;
  private config: LoadingServiceConfig;
  private listeners: Map<string, (event: LoadingEvent) => void>;
  private operationTimeouts: Map<string, NodeJS.Timeout>;

  constructor() {
    this.state = {
      activeOperations: new Map(),
      isGlobalLoading: false,
      defaultMessage: 'Cargando...'
    };

    this.config = {
      defaultMessage: 'Cargando...',
      minDisplayTime: 300, // Minimum 300ms to prevent flashing
      maxDisplayTime: 30000, // Maximum 30 seconds
      enableDebugLogging: true
    };

    this.listeners = new Map();
    this.operationTimeouts = new Map();

    this.log('LoadingService initialized');
  }

  /**
   * Configure the loading service
   */
  configure(config: Partial<LoadingServiceConfig>): void {
    this.config = { ...this.config, ...config };
    this.log('LoadingService configured', this.config);
  }

  /**
   * Start a loading operation
   * @param operation - Optional operation identifier
   * @param message - Optional loading message
   * @returns Operation ID for tracking
   */
  showLoading(operation?: string, message?: string): string {
    const operationId = operation || `operation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const loadingMessage = message || this.config.defaultMessage;

    const loadingState: LoadingState = {
      isLoading: true,
      message: loadingMessage,
      operation: operation,
      startTime: Date.now()
    };

    this.state.activeOperations.set(operationId, loadingState);
    this.updateGlobalState();

    // Set maximum display timeout
    const timeout = setTimeout(() => {
      this.log(`Auto-hiding loading for operation: ${operationId} (max time reached)`);
      this.hideLoading(operationId);
    }, this.config.maxDisplayTime);

    this.operationTimeouts.set(operationId, timeout);

    this.log(`Loading started for operation: ${operationId}`, loadingState);
    this.notifyListeners('loading-started', operationId, loadingState);

    return operationId;
  }

  /**
   * Stop a specific loading operation
   * @param operationId - The operation ID to stop
   */
  hideLoading(operationId: string): void {
    const loadingState = this.state.activeOperations.get(operationId);
    
    if (!loadingState) {
      this.log(`Attempted to hide non-existent loading operation: ${operationId}`);
      return;
    }

    const duration = Date.now() - (loadingState.startTime || 0);
    const minTimeRemaining = this.config.minDisplayTime - duration;

    // Clear timeout
    const timeout = this.operationTimeouts.get(operationId);
    if (timeout) {
      clearTimeout(timeout);
      this.operationTimeouts.delete(operationId);
    }

    const hideOperation = () => {
      this.state.activeOperations.delete(operationId);
      this.updateGlobalState();
      
      this.log(`Loading stopped for operation: ${operationId} (duration: ${duration}ms)`);
      this.notifyListeners('loading-stopped', operationId, { ...loadingState, isLoading: false });
    };

    // Ensure minimum display time to prevent flashing
    if (minTimeRemaining > 0) {
      setTimeout(hideOperation, minTimeRemaining);
    } else {
      hideOperation();
    }
  }

  /**
   * Stop all loading operations
   */
  hideAllLoading(): void {
    const operationIds = Array.from(this.state.activeOperations.keys());
    
    this.log(`Hiding all loading operations (${operationIds.length} active)`);
    
    operationIds.forEach(operationId => {
      this.hideLoading(operationId);
    });
  }

  /**
   * Check if a specific operation is loading
   */
  isOperationLoading(operation: string): boolean {
    for (const [, state] of this.state.activeOperations) {
      if (state.operation === operation && state.isLoading) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get all active operation IDs
   */
  getActiveOperations(): string[] {
    return Array.from(this.state.activeOperations.keys());
  }

  /**
   * Get current global loading state
   */
  getGlobalState(): GlobalLoadingState {
    return { ...this.state };
  }

  /**
   * Check if any loading operation is active
   */
  isGlobalLoading(): boolean {
    return this.state.isGlobalLoading;
  }

  /**
   * Get current loading message (from most recent operation)
   */
  getCurrentMessage(): string {
    if (this.state.activeOperations.size === 0) {
      return this.config.defaultMessage;
    }

    // Get the most recent operation's message
    let latestTime = 0;
    let latestMessage = this.config.defaultMessage;

    for (const [, state] of this.state.activeOperations) {
      if (state.startTime && state.startTime > latestTime) {
        latestTime = state.startTime;
        latestMessage = state.message || this.config.defaultMessage;
      }
    }

    return latestMessage;
  }

  /**
   * Subscribe to loading state changes
   */
  subscribe(listenerId: string, callback: (event: LoadingEvent) => void): void {
    this.listeners.set(listenerId, callback);
    this.log(`Listener subscribed: ${listenerId}`);
  }

  /**
   * Unsubscribe from loading state changes
   */
  unsubscribe(listenerId: string): void {
    this.listeners.delete(listenerId);
    this.log(`Listener unsubscribed: ${listenerId}`);
  }

  /**
   * Create loading overlay configuration for IonLoading
   */
  createOverlayConfig(message?: string, options?: Partial<LoadingOverlayConfig>): LoadingOverlayConfig {
    const defaultSpinner: LoadingAnimation = 'crescent';
    
    return {
      message: message || this.getCurrentMessage(),
      spinner: defaultSpinner,
      duration: 0, // Manual control
      showBackdrop: true,
      backdropDismiss: false,
      cssClass: 'loading-overlay',
      keyboardClose: false,
      ...options
    };
  }

  /**
   * Update global loading state based on active operations
   */
  private updateGlobalState(): void {
    const wasLoading = this.state.isGlobalLoading;
    this.state.isGlobalLoading = this.state.activeOperations.size > 0;

    if (wasLoading !== this.state.isGlobalLoading) {
      this.log(`Global loading state changed: ${this.state.isGlobalLoading}`);
    }
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(type: LoadingEventType, operationId: string, state: LoadingState): void {
    const event: LoadingEvent = {
      type,
      operationId,
      state,
      globalState: this.getGlobalState()
    };

    this.listeners.forEach((callback, listenerId) => {
      try {
        callback(event);
      } catch (error) {
        console.error(`Error in loading listener ${listenerId}:`, error);
      }
    });
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableDebugLogging) {
      debugLog(`[LoadingService] ${message}`, data);
    }
  }

  /**
   * Cleanup method for service shutdown
   */
  cleanup(): void {
    this.hideAllLoading();
    this.listeners.clear();
    
    // Clear all timeouts
    this.operationTimeouts.forEach(timeout => clearTimeout(timeout));
    this.operationTimeouts.clear();
    
    this.log('LoadingService cleaned up');
  }
}

// Export singleton instance
export const LoadingService = new LoadingServiceClass();

// Export class for testing
export { LoadingServiceClass };

// Export types for convenience
export type { LoadingEvent, LoadingEventType };
