/**
 * EnhancedHttpService - HTTP Service with Loading, Error, and Authentication Integration
 * Extends existing HttpService to automatically integrate with LoadingService, ErrorHandlerService,
 * and authentication system. Maintains compatibility with current authentication patterns and Capacitor HTTP usage
 */

import { Capacitor } from "@capacitor/core";
import {
  HttpRequestOptions,
  EnhancedHttpResponse,
  ErrorHandlingConfig,
  HttpInterceptorConfig,
} from "../types/loading.types";
import { LoadingService } from "./loading.service";
import { ErrorHandlerService } from "./error-handler.service";
import { AuthStorageService } from "./auth-storage.service";
import { CapacitorAuthService } from "./capacitor-auth.service";
import { debugLog } from "../config/environment.config";

// Import Capacitor HTTP for native requests
import { CapacitorHttp, HttpOptions, HttpResponse } from "@capacitor/core";

/**
 * Enhanced HTTP Service with integrated loading, error handling, and authentication
 */
class EnhancedHttpServiceClass {
  private isNative = Capacitor.getPlatform() !== "web";
  private config: HttpInterceptorConfig;
  private requestCounter = 0;

  constructor() {
    this.config = {
      enableLoading: true, // Always enabled for HTTP requests
      enableErrorHandling: true,
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableDebugLogging: true,
    };

    this.log(
      "EnhancedHttpService initialized with authentication integration",
      { isNative: this.isNative }
    );
  }

  /**
   * Configure the HTTP service
   */
  configure(config: Partial<HttpInterceptorConfig>): void {
    this.config = { ...this.config, ...config };
    this.log("EnhancedHttpService configured", this.config);
  }

  /**
   * Add authentication headers to request
   */
  private async addAuthHeaders(
    headers: Record<string, string>
  ): Promise<Record<string, string>> {
    try {
      // Get access token from appropriate service based on platform
      let accessToken: string | null = null;

      if (this.isNative) {
        // For native platforms, use CapacitorAuthService
        const currentUser = await CapacitorAuthService.getCurrentUser();
        accessToken = currentUser?.accessToken || null;
      } else {
        // For web platforms, try multiple sources
        try {
          // First try to get from userManager (most reliable for web)
          const { userManager } = await import("../config/user-manager.config");
          const user = await userManager.getUser();
          accessToken = user?.access_token || null;
        } catch (userManagerError) {
          // Fallback to AuthStorageService if userManager fails
          accessToken = await AuthStorageService.getAccessToken();
        }
      }

      if (accessToken) {
        this.log("Adding authentication header to request");
        return {
          ...headers,
          Authorization: `Bearer ${accessToken}`,
        };
      }

      this.log("No access token available, proceeding without authentication");
      return headers;
    } catch (error) {
      this.log("Error adding authentication headers:", error);
      // Return original headers if auth fails
      return headers;
    }
  }

  /**
   * Handle authentication errors (401/403)
   */
  private async handleAuthError(error: any): Promise<void> {
    if (error.status === 401) {
      this.log("Authentication error detected, clearing tokens");

      try {
        // Clear tokens from appropriate service
        if (this.isNative) {
          await CapacitorAuthService.signOut();
        } else {
          // For web platforms, clear from both userManager and AuthStorageService
          try {
            const { userManager } = await import(
              "../config/user-manager.config"
            );
            await userManager.removeUser();
          } catch (userManagerError) {
            this.log("Error clearing userManager:", userManagerError);
          }

          await AuthStorageService.clearTokens();
        }

        // Redirect to auth page after a short delay
        setTimeout(() => {
          window.location.href = "/auth";
        }, 1000);
      } catch (clearError) {
        this.log("Error clearing tokens after auth failure:", clearError);
      }
    }
  }

  /**
   * Make a GET request with integrated loading and error handling
   */
  async get<T = any>(
    url: string,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    return this.request<T>("GET", url, undefined, options);
  }

  /**
   * Make a POST request with integrated loading and error handling
   */
  async post<T = any>(
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    return this.request<T>("POST", url, data, options);
  }

  /**
   * Make a PUT request with integrated loading and error handling
   */
  async put<T = any>(
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    return this.request<T>("PUT", url, data, options);
  }

  /**
   * Make a DELETE request with integrated loading and error handling
   */
  async delete<T = any>(
    url: string,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    return this.request<T>("DELETE", url, undefined, options);
  }

  /**
   * Make a PATCH request with integrated loading and error handling
   */
  async patch<T = any>(
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    return this.request<T>("PATCH", url, data, options);
  }

  /**
   * Core request method with full integration
   */
  private async request<T = any>(
    method: string,
    url: string,
    data?: any,
    options: HttpRequestOptions = {}
  ): Promise<EnhancedHttpResponse<T>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();
    let loadingOperationId: string | null = null;

    // Extract options with defaults - Loading always enabled for HTTP requests
    const {
      headers = {},
      timeout = 30000,
      showLoading = true, // Always show loading for HTTP requests
      loadingMessage = "Cargando...",
      operation = `${method} ${url}`,
      errorHandling = {},
    } = options;

    try {
      // Always start loading for HTTP requests
      loadingOperationId = LoadingService.showLoading(
        operation,
        loadingMessage
      );
      this.log(`Loading started for HTTP request ${requestId}`, {
        operation,
        loadingOperationId,
      });

      // Add authentication headers
      const authenticatedHeaders = await this.addAuthHeaders(headers);

      // Make the HTTP request with retry logic
      const response = await this.makeRequestWithRetry(
        method,
        url,
        data,
        { headers: authenticatedHeaders, timeout },
        requestId
      );

      // Calculate duration
      const duration = Date.now() - startTime;

      // Create enhanced response
      const enhancedResponse: EnhancedHttpResponse<T> = {
        data: response.data,
        status: response.status,
        headers: response.headers,
        ok: response.status >= 200 && response.status < 300,
        url,
        operation,
        duration,
      };

      this.log(`Request ${requestId} completed successfully`, {
        status: response.status,
        duration,
        operation,
      });

      return enhancedResponse;
    } catch (error) {
      const duration = Date.now() - startTime;

      this.log(`Request ${requestId} failed`, {
        error,
        duration,
        operation,
      });

      // Handle authentication errors first
      await this.handleAuthError(error);

      // Handle error if enabled
      if (this.config.enableErrorHandling) {
        const errorConfig: Partial<ErrorHandlingConfig> = {
          ...errorHandling,
          // Add operation context to error
        };

        // Add operation context to error object
        if (error && typeof error === "object") {
          (error as any).operation = operation;
          (error as any).url = url;
          (error as any).duration = duration;
        }

        await ErrorHandlerService.handleError(error, errorConfig);
      }

      // Re-throw the error for the caller to handle
      throw error;
    } finally {
      // Always hide loading for HTTP requests
      if (loadingOperationId) {
        LoadingService.hideLoading(loadingOperationId);
        this.log(`Loading stopped for HTTP request ${requestId}`, {
          loadingOperationId,
        });
      }
    }
  }

  /**
   * Make HTTP request with retry logic
   */
  private async makeRequestWithRetry(
    method: string,
    url: string,
    data: any,
    options: { headers: any; timeout: number },
    requestId: string,
    attempt: number = 1
  ): Promise<HttpResponse> {
    try {
      const response = await this.makeHttpRequest(method, url, data, options);

      this.log(`Request ${requestId} attempt ${attempt} succeeded`, {
        status: response.status,
        method,
        url,
      });

      return response;
    } catch (error) {
      this.log(`Request ${requestId} attempt ${attempt} failed`, {
        error,
        method,
        url,
        attempt,
        maxRetries: this.config.maxRetries,
      });

      // Check if we should retry
      if (
        this.config.enableRetry &&
        attempt < this.config.maxRetries &&
        this.isRetryableError(error)
      ) {
        this.log(
          `Retrying request ${requestId} in ${this.config.retryDelay}ms`,
          {
            attempt: attempt + 1,
            maxRetries: this.config.maxRetries,
          }
        );

        // Wait before retry
        await this.delay(this.config.retryDelay * attempt); // Exponential backoff

        // Retry the request
        return this.makeRequestWithRetry(
          method,
          url,
          data,
          options,
          requestId,
          attempt + 1
        );
      }

      // No more retries, throw the error
      throw error;
    }
  }

  /**
   * Make the actual HTTP request (native or web)
   */
  private async makeHttpRequest(
    method: string,
    url: string,
    data: any,
    options: { headers: any; timeout: number }
  ): Promise<HttpResponse> {
    if (this.isNative) {
      return this.makeNativeRequest(method, url, data, options);
    } else {
      return this.makeWebRequest(method, url, data, options);
    }
  }

  /**
   * Make native HTTP request using Capacitor
   */
  private async makeNativeRequest(
    method: string,
    url: string,
    data: any,
    options: { headers: any; timeout: number }
  ): Promise<HttpResponse> {
    const httpOptions: HttpOptions = {
      url,
      method: method as any,
      headers: options.headers,
      data,
    };

    this.log("Making native HTTP request", {
      method,
      url,
      headers: options.headers,
    });

    const response = await CapacitorHttp.request(httpOptions);

    this.log("Native HTTP response received", {
      status: response.status,
      hasData: !!response.data,
      headers: response.headers,
    });

    return response;
  }

  /**
   * Make web HTTP request using fetch (fallback)
   */
  private async makeWebRequest(
    method: string,
    url: string,
    data: any,
    options: { headers: any; timeout: number }
  ): Promise<HttpResponse> {
    this.log("Making web HTTP request (fetch fallback)", { method, url });

    // Create AbortController for timeout (compatible with Node.js 14)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, options.timeout);

    const fetchOptions: RequestInit = {
      method,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
      signal: controller.signal,
    };

    try {
      const response = await fetch(url, fetchOptions);

      // Clear the timeout since request completed
      clearTimeout(timeoutId);

      const responseData = await response.json().catch(() => null);

      this.log("Web HTTP response received", {
        status: response.status,
        statusText: response.statusText,
        hasData: !!responseData,
      });

      // Convert fetch response to Capacitor HTTP response format
      return {
        data: responseData,
        status: response.status,
        headers: this.convertFetchHeaders(response.headers),
        url: response.url,
      };
    } catch (error) {
      // Clear the timeout in case of error
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Convert fetch headers to Capacitor format
   */
  private convertFetchHeaders(headers: Headers): { [key: string]: string } {
    const result: { [key: string]: string } = {};
    headers.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  /**
   * Check if an error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors (status 0)
    if (error && error.status === 0) return true;

    // Server errors (5xx)
    if (error && error.status >= 500) return true;

    // Timeout errors
    if (error && error.status === 408) return true;

    // Rate limiting
    if (error && error.status === 429) return true;

    // Fetch timeout errors
    if (error && error.name === "AbortError") return true;

    // Network errors from fetch
    if (error instanceof TypeError && error.message.includes("fetch"))
      return true;

    return false;
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    this.requestCounter++;
    return `req-${Date.now()}-${this.requestCounter}`;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableDebugLogging) {
      debugLog(`[EnhancedHttpService] ${message}`, data);
    }
  }

  /**
   * Get service statistics
   */
  getStats(): {
    requestCounter: number;
    isNative: boolean;
    config: HttpInterceptorConfig;
  } {
    return {
      requestCounter: this.requestCounter,
      isNative: this.isNative,
      config: { ...this.config },
    };
  }

  /**
   * Reset service statistics
   */
  resetStats(): void {
    this.requestCounter = 0;
    this.log("Service statistics reset");
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    this.requestCounter = 0;
    this.log("EnhancedHttpService cleaned up");
  }
}

// Export singleton instance
export const EnhancedHttpService = new EnhancedHttpServiceClass();

// Export class for testing
export { EnhancedHttpServiceClass };
