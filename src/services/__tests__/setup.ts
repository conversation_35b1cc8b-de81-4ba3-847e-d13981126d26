/**
 * Test Setup for Services
 * Common test configuration and utilities for service tests
 */

// Mock console methods to reduce test noise
const originalConsole = { ...console };

beforeAll(() => {
  // Mock console methods for cleaner test output
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
});

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole);
});

// Mock environment config
jest.mock('../../config/environment.config', () => ({
  debugLog: jest.fn(),
  environmentConfig: {
    debugAuth: false,
    authority: 'https://test-auth.example.com',
    clientId: 'test-client-id',
    scope: 'openid profile email'
  }
}));

// Global test utilities
export const TestUtils = {
  /**
   * Create a mock HTTP response
   */
  createMockResponse: (data: any, status?: number, ok?: boolean) => {
    const responseStatus = status || 200;
    const responseOk = ok !== undefined ? ok : true;

    return {
      ok: responseOk,
      status: responseStatus,
      statusText: responseOk ? 'OK' : 'Error',
      json: jest.fn().mockResolvedValue(data),
      headers: new Headers(),
      url: 'http://test.com/api'
    };
  },

  /**
   * Create a mock error
   */
  createMockError: (status: number, message: string) => {
    return {
      status,
      message,
      statusText: message
    };
  },

  /**
   * Wait for a specified amount of time
   */
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Create a large test object for storage quota tests
   */
  createLargeObject: (sizeInKB: number) => {
    return {
      data: 'x'.repeat(sizeInKB * 1024),
      timestamp: Date.now()
    };
  },

  /**
   * Mock localStorage for consistent testing
   */
  mockLocalStorage: () => {
    const store: { [key: string]: string } = {};

    return {
      getItem: jest.fn((key: string) => store[key] || null),
      setItem: jest.fn((key: string, value: string) => {
        store[key] = value;
      }),
      removeItem: jest.fn((key: string) => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        Object.keys(store).forEach(key => delete store[key]);
      }),
      get length() {
        return Object.keys(store).length;
      },
      key: jest.fn((index: number) => {
        const keys = Object.keys(store);
        return keys[index] || null;
      })
    };
  }
};

// Common test data
export const TestData = {
  user: {
    id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    preferences: {
      theme: 'dark',
      language: 'es'
    }
  },

  student: {
    id: '456',
    name: 'Test Student',
    grade: '5to Grado',
    section: 'A'
  },

  httpResponse: {
    success: {
      data: { message: 'Success' },
      status: 200
    },
    error: {
      status: 500,
      message: 'Internal Server Error'
    },
    unauthorized: {
      status: 401,
      message: 'Unauthorized'
    }
  }
};

// Mock timers helper
export const MockTimers = {
  setup: () => {
    jest.useFakeTimers();
  },
  
  cleanup: () => {
    jest.useRealTimers();
  },
  
  advanceTime: (ms: number) => {
    jest.advanceTimersByTime(ms);
  },
  
  runAllTimers: () => {
    jest.runAllTimers();
  }
};

// Service test helpers
export const ServiceTestHelpers = {
  /**
   * Test service configuration
   */
  testServiceConfiguration: (service: any, configMethod: string, testConfig: any) => {
    expect(() => {
      service[configMethod](testConfig);
    }).not.toThrow();
  },

  /**
   * Test service cleanup
   */
  testServiceCleanup: async (service: any) => {
    expect(() => {
      service.cleanup();
    }).not.toThrow();
  },

  /**
   * Test error handling
   */
  testErrorHandling: async (operation: () => Promise<any>, expectedError?: string) => {
    try {
      await operation();
      fail('Expected operation to throw an error');
    } catch (error) {
      if (expectedError) {
        expect(error.message).toContain(expectedError);
      }
      expect(error).toBeDefined();
    }
  }
};

// Export for use in tests
export default {
  TestUtils,
  TestData,
  MockTimers,
  ServiceTestHelpers
};
