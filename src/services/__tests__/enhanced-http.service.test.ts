/**
 * EnhancedHttpService Integration Tests
 * Tests for HTTP service with authentication, loading, and error handling
 */

import { EnhancedHttpServiceClass } from '../enhanced-http.service';
import { LoadingService } from '../loading.service';
import { ErrorHandlerService } from '../error-handler.service';

// Mock Capacitor
jest.mock('@capacitor/core', () => ({
  Capacitor: {
    getPlatform: jest.fn(() => 'web'),
    isNativePlatform: jest.fn(() => false),
    isPluginAvailable: jest.fn(() => false)
  }
}));

// Mock authentication services
jest.mock('../auth-storage.service', () => ({
  AuthStorageService: {
    getStoredTokens: jest.fn(),
    getAccessToken: jest.fn(),
    clearTokens: jest.fn()
  }
}));

jest.mock('../capacitor-auth.service', () => ({
  CapacitorAuthService: {
    getCurrentUser: jest.fn(),
    signOut: jest.fn()
  }
}));

// Mock userManager config
jest.mock('../../config/user-manager.config', () => ({
  userManager: {
    getUser: jest.fn(),
    removeUser: jest.fn()
  }
}));

// Mock LoadingService
jest.mock('../loading.service', () => ({
  LoadingService: {
    showLoading: jest.fn(() => 'loading-id-123'),
    hideLoading: jest.fn()
  }
}));

// Mock ErrorHandlerService
jest.mock('../error-handler.service', () => ({
  ErrorHandlerService: {
    handleError: jest.fn()
  }
}));

// Mock fetch
global.fetch = jest.fn();

describe('EnhancedHttpService', () => {
  let httpService: EnhancedHttpServiceClass;
  const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    httpService = new EnhancedHttpServiceClass();
    httpService.configure({ enableDebugLogging: false });
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup default successful response
    mockFetch.mockResolvedValue({
      ok: true,
      status: 200,
      statusText: 'OK',
      json: jest.fn().mockResolvedValue({ data: 'test' }),
      headers: new Headers(),
      url: 'http://test.com/api'
    } as any);
  });

  afterEach(() => {
    httpService.cleanup();
  });

  describe('Basic HTTP Methods', () => {
    it('should make GET request successfully', async () => {
      const response = await httpService.get('/api/test');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );

      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);
      expect(response.data).toEqual({ data: 'test' });
    });

    it('should make POST request with data', async () => {
      const testData = { name: 'Test User' };
      
      await httpService.post('/api/users', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/users',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData),
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      );
    });

    it('should make PUT request with data', async () => {
      const testData = { id: 1, name: 'Updated User' };
      
      await httpService.put('/api/users/1', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/users/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(testData)
        })
      );
    });

    it('should make DELETE request', async () => {
      await httpService.delete('/api/users/1');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/users/1',
        expect.objectContaining({
          method: 'DELETE'
        })
      );
    });

    it('should make PATCH request with data', async () => {
      const testData = { name: 'Patched User' };
      
      await httpService.patch('/api/users/1', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/users/1',
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify(testData)
        })
      );
    });
  });

  describe('Authentication Integration', () => {
    it('should add authentication header when token is available from userManager', async () => {
      const { userManager } = require('../../config/user-manager.config');
      userManager.getUser.mockResolvedValue({
        access_token: 'test-token-123'
      });

      await httpService.get('/api/protected');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/protected',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token-123'
          })
        })
      );
    });

    it('should fallback to AuthStorageService when userManager fails', async () => {
      const { userManager } = require('../../config/user-manager.config');
      const { AuthStorageService } = require('../auth-storage.service');

      userManager.getUser.mockRejectedValue(new Error('UserManager error'));
      AuthStorageService.getAccessToken.mockResolvedValue('fallback-token-456');

      await httpService.get('/api/protected');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/protected',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer fallback-token-456'
          })
        })
      );
    });

    it('should proceed without auth header when no token available', async () => {
      const { userManager } = require('../../config/user-manager.config');
      userManager.getUser.mockResolvedValue(null);

      await httpService.get('/api/public');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/public',
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'Authorization': expect.any(String)
          })
        })
      );
    });

    it('should handle authentication errors (401)', async () => {
      const { AuthStorageService } = require('../auth-storage.service');
      const { userManager } = require('../../config/user-manager.config');

      mockFetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        json: jest.fn().mockResolvedValue({ error: 'Unauthorized' }),
        headers: new Headers(),
        url: 'http://test.com/api'
      } as any);

      // Mock window.location
      delete (window as any).location;
      (window as any).location = { href: '' };

      try {
        await httpService.get('/api/protected');
      } catch (error) {
        // Error should be thrown after handling
      }

      expect(userManager.removeUser).toHaveBeenCalled();
      expect(AuthStorageService.clearTokens).toHaveBeenCalled();
    });
  });

  describe('Loading Integration', () => {
    it('should show and hide loading during request', async () => {
      await httpService.get('/api/test');

      expect(LoadingService.showLoading).toHaveBeenCalledWith(
        'GET /api/test',
        'Cargando...'
      );
      expect(LoadingService.hideLoading).toHaveBeenCalledWith('loading-id-123');
    });

    it('should use custom loading message', async () => {
      await httpService.get('/api/test', {
        loadingMessage: 'Cargando datos...'
      });

      expect(LoadingService.showLoading).toHaveBeenCalledWith(
        'GET /api/test',
        'Cargando datos...'
      );
    });

    it('should hide loading even when request fails', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      try {
        await httpService.get('/api/test');
      } catch (error) {
        // Expected to throw
      }

      expect(LoadingService.hideLoading).toHaveBeenCalledWith('loading-id-123');
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle errors through ErrorHandlerService', async () => {
      const networkError = new Error('Network error');
      mockFetch.mockRejectedValue(networkError);

      try {
        await httpService.get('/api/test');
      } catch (error) {
        // Expected to throw
      }

      expect(ErrorHandlerService.handleError).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'GET /api/test',
          url: '/api/test'
        }),
        expect.any(Object)
      );
    });

    it('should add operation context to errors', async () => {
      const networkError = new Error('Network error');
      mockFetch.mockRejectedValue(networkError);

      try {
        await httpService.get('/api/test', {
          operation: 'fetch-user-data'
        });
      } catch (error) {
        // Expected to throw
      }

      expect(ErrorHandlerService.handleError).toHaveBeenCalledWith(
        expect.objectContaining({
          operation: 'fetch-user-data',
          url: '/api/test'
        }),
        expect.any(Object)
      );
    });
  });

  describe('Retry Logic', () => {
    it('should retry on retryable errors', async () => {
      // First call fails with 500, second succeeds
      mockFetch
        .mockRejectedValueOnce({ status: 500, message: 'Server Error' })
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          json: jest.fn().mockResolvedValue({ data: 'success' }),
          headers: new Headers(),
          url: 'http://test.com/api'
        } as any);

      const response = await httpService.get('/api/test');

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(response.ok).toBe(true);
    });

    it('should not retry on non-retryable errors', async () => {
      mockFetch.mockRejectedValue({ status: 400, message: 'Bad Request' });

      try {
        await httpService.get('/api/test');
      } catch (error) {
        // Expected to throw
      }

      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should respect max retry limit', async () => {
      mockFetch.mockRejectedValue({ status: 500, message: 'Server Error' });

      try {
        await httpService.get('/api/test');
      } catch (error) {
        // Expected to throw after max retries
      }

      // Should be called 1 + maxRetries times (initial + retries)
      expect(mockFetch).toHaveBeenCalledTimes(4); // 1 + 3 retries
    });
  });

  describe('Custom Headers and Options', () => {
    it('should include custom headers', async () => {
      await httpService.get('/api/test', {
        headers: {
          'Custom-Header': 'custom-value',
          'Another-Header': 'another-value'
        }
      });

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Custom-Header': 'custom-value',
            'Another-Header': 'another-value'
          })
        })
      );
    });

    it('should respect timeout option', async () => {
      await httpService.get('/api/test', {
        timeout: 5000
      });

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          signal: expect.any(AbortSignal)
        })
      );
    });
  });

  describe('Response Processing', () => {
    it('should process successful response correctly', async () => {
      const mockData = { id: 1, name: 'Test User' };
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        json: jest.fn().mockResolvedValue(mockData),
        headers: new Headers({ 'Content-Type': 'application/json' }),
        url: 'http://test.com/api/users'
      } as any);

      const response = await httpService.get('/api/users');

      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);
      expect(response.data).toEqual(mockData);
      expect(response.url).toBe('/api/users');
      expect(response.duration).toBeGreaterThan(0);
    });

    it('should handle non-JSON responses gracefully', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        status: 200,
        statusText: 'OK',
        json: jest.fn().mockRejectedValue(new Error('Not JSON')),
        headers: new Headers(),
        url: 'http://test.com/api'
      } as any);

      const response = await httpService.get('/api/test');

      expect(response.ok).toBe(true);
      expect(response.data).toBeNull();
    });
  });

  describe('Service Statistics', () => {
    it('should track request statistics', async () => {
      await httpService.get('/api/test1');
      await httpService.get('/api/test2');

      const stats = httpService.getStats();

      expect(stats.requestCounter).toBe(2);
      expect(stats.isNative).toBe(false);
      expect(stats.config).toBeDefined();
    });

    it('should reset statistics', async () => {
      await httpService.get('/api/test');
      
      httpService.resetStats();
      
      const stats = httpService.getStats();
      expect(stats.requestCounter).toBe(0);
    });
  });
});
