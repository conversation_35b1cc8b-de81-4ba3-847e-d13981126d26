/**
 * Tests for UserApiService
 * Verifies the real API integration implementation
 */

import { UserApiService } from "../user-api.service";
import { EnhancedHttpService } from "../enhanced-http.service";
import { Preferences } from "@capacitor/preferences";
import { CACHE_KEYS } from "../../types/api.types";

// Mock dependencies
jest.mock("../enhanced-http.service");
jest.mock("@capacitor/preferences");
jest.mock("../../config/environment.config", () => ({
  environmentConfig: {
    apiBaseUrl: "https://pre-neds-api.santillana.com",
    apiTimeout: 30000,
    debugApi: true,
  },
  debugApiLog: jest.fn(),
}));
jest.mock("../../config/user-manager.config", () => ({
  debugLog: jest.fn(),
}));

const mockEnhancedHttpService = EnhancedHttpService as jest.Mocked<
  typeof EnhancedHttpService
>;
const mockPreferences = Preferences as jest.Mocked<typeof Preferences>;

describe("UserApiService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("fetchUserProfile", () => {
    const mockOidcProfile = {
      sub: "user123",
      name: "Test User",
      email: "<EMAIL>",
      preferred_username: "testuser",
    };
    const mockAccessToken = "mock-access-token";

    it("should fetch user profile with real API calls", async () => {
      // Mock API responses
      const mockTenantsResponse = {
        tenants: [{ id: "tenant1", name: "Test Tenant", isActive: true }],
        loginContext: btoa(
          JSON.stringify({ userId: "user123", roleId: "parent" })
        ),
      };

      const mockRelatedUsersResponse = {
        relatedUsers: [
          {
            userId: "child1",
            name: "Test",
            surnames: "Child",
            login: "testchild",
            image: "/test.jpg",
            schoolLevelSessions: [
              {
                id: "session1",
                schoolId: "school1",
                schoolName: "Test School",
                levelId: "level1",
                levelName: "5th Grade",
                grade: "5th Grade",
                isActive: true,
              },
            ],
          },
        ],
        hasRelatedUsers: true,
      };

      // Mock HTTP service responses
      mockEnhancedHttpService.get
        .mockResolvedValueOnce({
          ok: true,
          data: mockTenantsResponse,
          status: 200,
          headers: {},
          url: "",
          operation: "",
          duration: 100,
        })
        .mockResolvedValueOnce({
          ok: true,
          data: mockRelatedUsersResponse,
          status: 200,
          headers: {},
          url: "",
          operation: "",
          duration: 100,
        });

      // Mock cache (no cached data)
      mockPreferences.get.mockResolvedValue({ value: null });
      mockPreferences.set.mockResolvedValue();

      const result = await UserApiService.fetchUserProfile(
        mockOidcProfile,
        mockAccessToken
      );

      expect(result).toEqual({
        id: "user123",
        name: "Test User",
        email: "<EMAIL>",
        phone: "+34 123 456 789",
        role: "Padre/Madre",
        avatar: "https://ionicframework.com/docs/img/demos/avatar.svg",
        memberSince: expect.any(String),
        children: [
          {
            id: "child1",
            name: "Test Child",
            grade: "5th Grade",
            school: "Test School",
            avatar: "/test.jpg",
            surnames: "Child",
            login: "testchild",
            schoolLevelSessions: expect.any(Array),
            isActive: undefined,
            metadata: undefined,
          },
        ],
      });

      // Verify API calls were made
      expect(mockEnhancedHttpService.get).toHaveBeenCalledTimes(2);
      expect(mockPreferences.set).toHaveBeenCalledWith({
        key: CACHE_KEYS.USER_PROFILE,
        value: expect.any(String),
      });
    });

    it("should use cached data when available and valid", async () => {
      const mockCachedProfile = {
        user: {
          id: "user123",
          name: "Cached User",
          email: "<EMAIL>",
          role: "Padre/Madre",
          avatar: "cached-avatar.jpg",
          memberSince: "2023",
        },
        children: [],
        loginContext: "cached-context",
        tenants: [],
        lastUpdated: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 60000).toISOString(), // 1 minute from now
      };

      mockPreferences.get.mockResolvedValue({
        value: JSON.stringify(mockCachedProfile),
      });

      const result = await UserApiService.fetchUserProfile(
        mockOidcProfile,
        mockAccessToken
      );

      expect(result.name).toBe("Cached User");
      expect(result.email).toBe("<EMAIL>");
      expect(mockEnhancedHttpService.get).not.toHaveBeenCalled();
    });

    it("should handle API errors gracefully", async () => {
      // Mock cache (no cached data)
      mockPreferences.get.mockResolvedValue({ value: null });

      // Mock API error
      mockEnhancedHttpService.get.mockRejectedValue(new Error("API Error"));

      await expect(
        UserApiService.fetchUserProfile(mockOidcProfile, mockAccessToken)
      ).rejects.toThrow("Failed to fetch user profile from Santillana APIs");
    });
  });

  describe("refreshUserProfile", () => {
    it("should clear cache and fetch fresh data", async () => {
      const mockOidcProfile = { sub: "user123", name: "Test User" };
      const mockAccessToken = "token";

      // Mock successful API calls
      mockEnhancedHttpService.get.mockResolvedValue({
        ok: true,
        data: {
          tenants: [],
          loginContext: btoa(
            JSON.stringify({ userId: "user123", roleId: "parent" })
          ),
        },
        status: 200,
        headers: {},
        url: "",
        operation: "",
        duration: 100,
      });

      mockPreferences.get.mockResolvedValue({ value: null });
      mockPreferences.remove.mockResolvedValue();
      mockPreferences.set.mockResolvedValue();

      await UserApiService.refreshUserProfile(mockOidcProfile, mockAccessToken);

      // Verify cache was cleared
      expect(mockPreferences.remove).toHaveBeenCalledWith({
        key: CACHE_KEYS.USER_PROFILE,
      });
    });
  });

  describe("clearCache", () => {
    it("should remove all cached data", async () => {
      mockPreferences.remove.mockResolvedValue();

      await UserApiService.clearCache();

      expect(mockPreferences.remove).toHaveBeenCalledWith({
        key: CACHE_KEYS.USER_PROFILE,
      });
      expect(mockPreferences.remove).toHaveBeenCalledWith({
        key: CACHE_KEYS.LOGIN_CONTEXT,
      });
      expect(mockPreferences.remove).toHaveBeenCalledWith({
        key: CACHE_KEYS.TENANTS,
      });
      expect(mockPreferences.remove).toHaveBeenCalledWith({
        key: CACHE_KEYS.LAST_SYNC,
      });
    });
  });

  describe("getCacheStatus", () => {
    it("should return cache status information", async () => {
      const mockCachedProfile = {
        user: {
          id: "user123",
          name: "Test",
          email: "<EMAIL>",
          role: "parent",
          avatar: "",
          memberSince: "2023",
        },
        children: [],
        loginContext: "context",
        tenants: [],
        lastUpdated: new Date(Date.now() - 5000).toISOString(), // 5 seconds ago
        expiresAt: new Date(Date.now() + 60000).toISOString(), // 1 minute from now
      };

      mockPreferences.get.mockResolvedValue({
        value: JSON.stringify(mockCachedProfile),
      });

      const status = await UserApiService.getCacheStatus();

      expect(status.hasCache).toBe(true);
      expect(status.isValid).toBe(true);
      expect(status.age).toBeGreaterThan(0);
    });

    it("should handle no cache scenario", async () => {
      mockPreferences.get.mockResolvedValue({ value: null });

      const status = await UserApiService.getCacheStatus();

      expect(status.hasCache).toBe(false);
      expect(status.isValid).toBe(false);
    });
  });
});
