/**
 * StorageService Unit Tests
 * Tests for the unified storage management service
 */

import { StorageServiceClass } from '../storage.service';

// Mock Capacitor
jest.mock('@capacitor/core', () => ({
  Capacitor: {
    getPlatform: jest.fn(() => 'web'),
    isNativePlatform: jest.fn(() => false)
  }
}));

// Mock Preferences
jest.mock('@capacitor/preferences', () => ({
  Preferences: {
    set: jest.fn(),
    get: jest.fn(),
    remove: jest.fn(),
    clear: jest.fn(),
    keys: jest.fn()
  }
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: { [key: string]: string } = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

describe('StorageService', () => {
  let storageService: StorageServiceClass;

  beforeEach(() => {
    storageService = new StorageServiceClass();
    storageService.configure({ enableDebugLogging: false });
    localStorageMock.clear();
  });

  afterEach(async () => {
    await storageService.clear();
    storageService.cleanup();
  });

  describe('Basic Operations', () => {
    it('should store and retrieve string data', async () => {
      const testValue = 'test string';
      
      const setResult = await storageService.set('test-key', testValue);
      expect(setResult.success).toBe(true);
      expect(setResult.data).toBe(testValue);
      
      const getResult = await storageService.get('test-key');
      expect(getResult.success).toBe(true);
      expect(getResult.data).toBe(testValue);
    });

    it('should store and retrieve object data', async () => {
      const testObject = {
        name: 'Test User',
        age: 25,
        preferences: {
          theme: 'dark',
          language: 'es'
        }
      };
      
      const setResult = await storageService.set('test-object', testObject);
      expect(setResult.success).toBe(true);
      
      const getResult = await storageService.get('test-object');
      expect(getResult.success).toBe(true);
      expect(getResult.data).toEqual(testObject);
    });

    it('should store and retrieve array data', async () => {
      const testArray = [1, 2, 3, 'four', { five: 5 }];
      
      const setResult = await storageService.set('test-array', testArray);
      expect(setResult.success).toBe(true);
      
      const getResult = await storageService.get('test-array');
      expect(getResult.success).toBe(true);
      expect(getResult.data).toEqual(testArray);
    });

    it('should return default value when key does not exist', async () => {
      const defaultValue = { default: true };
      
      const result = await storageService.get('non-existent-key', defaultValue);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(defaultValue);
    });

    it('should return error when key does not exist and no default provided', async () => {
      const result = await storageService.get('non-existent-key');
      expect(result.success).toBe(false);
      expect(result.error).toBe('Key not found');
    });
  });

  describe('Key Management', () => {
    beforeEach(async () => {
      await storageService.set('key1', 'value1');
      await storageService.set('key2', 'value2');
      await storageService.set('key3', 'value3');
    });

    it('should retrieve all keys', async () => {
      const result = await storageService.keys();
      expect(result.success).toBe(true);
      // Filter out localStorage mock methods and only check for actual keys
      const actualKeys = result.data?.filter(key =>
        !['getItem', 'setItem', 'removeItem', 'clear', 'length', 'key'].includes(key)
      ) || [];
      expect(actualKeys).toContain('key1');
      expect(actualKeys).toContain('key2');
      expect(actualKeys).toContain('key3');
    });

    it('should check if key exists', async () => {
      const exists1 = await storageService.has('key1');
      const exists2 = await storageService.has('non-existent');
      
      expect(exists1).toBe(true);
      expect(exists2).toBe(false);
    });

    it('should remove specific key', async () => {
      const removeResult = await storageService.remove('key2');
      expect(removeResult.success).toBe(true);
      
      const exists = await storageService.has('key2');
      expect(exists).toBe(false);
      
      // Other keys should still exist
      const exists1 = await storageService.has('key1');
      const exists3 = await storageService.has('key3');
      expect(exists1).toBe(true);
      expect(exists3).toBe(true);
    });

    it('should clear all storage', async () => {
      const clearResult = await storageService.clear();
      expect(clearResult.success).toBe(true);

      const keysResult = await storageService.keys();
      // Filter out localStorage mock methods
      const actualKeys = keysResult.data?.filter(key =>
        !['getItem', 'setItem', 'removeItem', 'clear', 'length', 'key'].includes(key)
      ) || [];
      expect(actualKeys).toHaveLength(0);
    });
  });

  describe('Storage Statistics', () => {
    it('should provide accurate storage statistics', async () => {
      await storageService.set('test1', 'small value');
      await storageService.set('test2', { large: 'x'.repeat(1000) });

      const stats = await storageService.getStats();

      // The totalKeys includes localStorage mock methods, so we need to account for that
      expect(stats.totalKeys).toBeGreaterThanOrEqual(2);
      expect(stats.estimatedSize).toBeGreaterThan(0);
      expect(stats.platform).toBe('web');
      expect(stats.isNative).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle JSON parsing errors gracefully', async () => {
      // Manually set invalid JSON in localStorage
      localStorageMock.setItem('invalid-json', 'invalid json data');

      const result = await storageService.get('invalid-json');
      expect(result.success).toBe(false);
      expect(result.error).toContain('error');
    });

    it('should handle storage quota exceeded', async () => {
      // Configure very small storage limit
      storageService.configure({ maxStorageSize: 100 });
      
      const largeData = 'x'.repeat(200);
      const result = await storageService.set('large-key', largeData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Storage quota exceeded');
    });
  });

  describe('Type Safety', () => {
    interface TestInterface {
      id: number;
      name: string;
      active: boolean;
    }

    it('should maintain type safety with TypeScript generics', async () => {
      const testData: TestInterface = {
        id: 1,
        name: 'Test Item',
        active: true
      };
      
      const setResult = await storageService.set<TestInterface>('typed-data', testData);
      expect(setResult.success).toBe(true);
      
      const getResult = await storageService.get<TestInterface>('typed-data');
      expect(getResult.success).toBe(true);
      expect(getResult.data?.id).toBe(1);
      expect(getResult.data?.name).toBe('Test Item');
      expect(getResult.data?.active).toBe(true);
    });
  });

  describe('Configuration', () => {
    it('should apply configuration changes', () => {
      const newConfig = {
        enableDebugLogging: true,
        maxStorageSize: 5 * 1024 * 1024,
        cleanupThreshold: 0.9
      };
      
      storageService.configure(newConfig);
      
      // Configuration should be applied (we can't directly test private config,
      // but we can test behavior changes)
      expect(() => storageService.configure(newConfig)).not.toThrow();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      await storageService.set('test1', 'value1');
      await storageService.set('test2', 'value2');
      
      storageService.cleanup();
      
      // After cleanup, service should still function
      const result = await storageService.set('test3', 'value3');
      expect(result.success).toBe(true);
    });
  });
});
