/**
 * AlertService Unit Tests
 * Tests for the user notification management service
 */

import { AlertServiceClass } from '../alert.service';

// Mock Ionic controllers
const mockToastController = {
  create: jest.fn().mockResolvedValue({
    present: jest.fn().mockResolvedValue(undefined),
    dismiss: jest.fn().mockResolvedValue(undefined),
    onDidDismiss: jest.fn().mockReturnValue(Promise.resolve())
  })
};

const mockAlertController = {
  create: jest.fn().mockResolvedValue({
    present: jest.fn().mockResolvedValue(undefined),
    dismiss: jest.fn().mockResolvedValue(undefined),
    onDidDismiss: jest.fn().mockReturnValue(Promise.resolve())
  })
};

// Mock @ionic/core/components
jest.mock('@ionic/core/components', () => ({
  toastController: mockToastController,
  alertController: mockAlertController
}));

// Mock Haptics
jest.mock('@capacitor/haptics', () => ({
  Haptics: {
    impact: jest.fn().mockResolvedValue(undefined)
  },
  ImpactStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy'
  }
}));

describe('AlertService', () => {
  let alertService: AlertServiceClass;

  beforeEach(() => {
    alertService = new AlertServiceClass();
    alertService.configure({ enableDebugLogging: false });
    
    // Reset mocks
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await alertService.cleanup();
  });

  describe('Toast Notifications', () => {
    it('should show success toast with correct configuration', async () => {
      await alertService.showSuccess('Success message');

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Success message',
          color: 'success',
          duration: 3000,
          position: 'bottom'
        })
      );
    });

    it('should show error toast with longer duration', async () => {
      await alertService.showError('Error message');

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Error message',
          color: 'danger',
          duration: 6000 // 2x default duration
        })
      );
    });

    it('should show warning toast with medium duration', async () => {
      await alertService.showWarning('Warning message');

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Warning message',
          color: 'warning',
          duration: 4500 // 1.5x default duration
        })
      );
    });

    it('should show info toast with default configuration', async () => {
      await alertService.showInfo('Info message');

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Info message',
          color: 'primary',
          duration: 3000
        })
      );
    });

    it('should show custom toast with provided options', async () => {
      const customOptions = {
        message: 'Custom message',
        duration: 5000,
        position: 'top' as const,
        color: 'secondary',
        cssClass: 'custom-toast-class'
      };

      await alertService.showToast(customOptions);

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Custom message',
          duration: 5000,
          position: 'top',
          color: 'secondary',
          cssClass: 'custom-toast-class'
        })
      );
    });

    it('should add close button when showCloseButton is true', async () => {
      await alertService.showToast({
        message: 'Test message',
        showCloseButton: true
      });

      expect(mockToastController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          buttons: [
            {
              text: 'Cerrar',
              role: 'cancel'
            }
          ]
        })
      );
    });
  });

  describe('Alert Dialogs', () => {
    it('should show alert with default configuration', async () => {
      await alertService.showAlert({
        message: 'Alert message'
      });

      expect(mockAlertController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          header: 'Información',
          message: 'Alert message',
          buttons: [
            {
              text: 'Aceptar',
              role: 'confirm'
            }
          ],
          backdropDismiss: true
        })
      );
    });

    it('should show alert with custom configuration', async () => {
      const customOptions = {
        header: 'Custom Header',
        subHeader: 'Custom SubHeader',
        message: 'Custom message',
        buttons: [
          {
            text: 'Custom Button',
            role: 'confirm' as const,
            handler: jest.fn()
          }
        ],
        cssClass: 'custom-alert',
        backdropDismiss: false
      };

      await alertService.showAlert(customOptions);

      expect(mockAlertController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          header: 'Custom Header',
          subHeader: 'Custom SubHeader',
          message: 'Custom message',
          buttons: customOptions.buttons,
          cssClass: 'custom-alert',
          backdropDismiss: false
        })
      );
    });
  });

  describe('Confirmation Dialogs', () => {
    it('should show confirmation dialog with default configuration', async () => {
      const confirmationPromise = alertService.showConfirmation({
        message: 'Are you sure?'
      });

      expect(mockAlertController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          header: 'Confirmar',
          message: 'Are you sure?',
          buttons: expect.arrayContaining([
            expect.objectContaining({
              text: 'Cancelar',
              role: 'cancel'
            }),
            expect.objectContaining({
              text: 'Confirmar',
              role: 'confirm'
            })
          ])
        })
      );

      // The promise should be pending until user interaction
      expect(confirmationPromise).toBeInstanceOf(Promise);
    });

    it('should show confirmation dialog with custom text', async () => {
      alertService.showConfirmation({
        header: 'Delete Item',
        message: 'This action cannot be undone',
        confirmText: 'Delete',
        cancelText: 'Keep'
      });

      expect(mockAlertController.create).toHaveBeenCalledWith(
        expect.objectContaining({
          header: 'Delete Item',
          message: 'This action cannot be undone',
          buttons: expect.arrayContaining([
            expect.objectContaining({
              text: 'Keep',
              role: 'cancel'
            }),
            expect.objectContaining({
              text: 'Delete',
              role: 'confirm'
            })
          ])
        })
      );
    });
  });

  describe('Active Notification Management', () => {
    it('should track active notifications count', () => {
      const initialCount = alertService.getActiveCount();
      expect(initialCount.toasts).toBe(0);
      expect(initialCount.alerts).toBe(0);
      expect(initialCount.total).toBe(0);
    });

    it('should dismiss all toasts', async () => {
      const mockToast = {
        dismiss: jest.fn().mockResolvedValue(undefined)
      };

      // Simulate active toasts
      (alertService as any).activeToasts.add(mockToast);

      await alertService.dismissAllToasts();

      expect(mockToast.dismiss).toHaveBeenCalled();
    });

    it('should dismiss all alerts', async () => {
      const mockAlert = {
        dismiss: jest.fn().mockResolvedValue(undefined)
      };

      // Simulate active alerts
      (alertService as any).activeAlerts.add(mockAlert);

      await alertService.dismissAllAlerts();

      expect(mockAlert.dismiss).toHaveBeenCalled();
    });

    it('should dismiss all notifications', async () => {
      const mockToast = {
        dismiss: jest.fn().mockResolvedValue(undefined)
      };
      const mockAlert = {
        dismiss: jest.fn().mockResolvedValue(undefined)
      };

      // Simulate active notifications
      (alertService as any).activeToasts.add(mockToast);
      (alertService as any).activeAlerts.add(mockAlert);

      await alertService.dismissAll();

      expect(mockToast.dismiss).toHaveBeenCalled();
      expect(mockAlert.dismiss).toHaveBeenCalled();
    });
  });

  describe('Configuration', () => {
    it('should apply configuration changes', () => {
      const newConfig = {
        enableDebugLogging: true,
        defaultToastDuration: 5000,
        defaultToastPosition: 'top' as const,
        enableHapticFeedback: false
      };

      alertService.configure(newConfig);

      // Configuration should be applied without throwing
      expect(() => alertService.configure(newConfig)).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle toast controller errors gracefully', async () => {
      mockToastController.create.mockRejectedValueOnce(new Error('Toast error'));

      // Should not throw error
      await expect(alertService.showSuccess('Test message')).resolves.not.toThrow();
    });

    it('should handle alert controller errors gracefully', async () => {
      mockAlertController.create.mockRejectedValueOnce(new Error('Alert error'));

      // Should not throw error
      await expect(alertService.showAlert({ message: 'Test message' })).resolves.not.toThrow();
    });

    it('should handle missing controllers gracefully', async () => {
      // Create service instance without controllers
      const serviceWithoutControllers = new AlertServiceClass();
      (serviceWithoutControllers as any).toastController = null;
      (serviceWithoutControllers as any).alertController = null;

      // Should not throw errors
      await expect(serviceWithoutControllers.showSuccess('Test')).resolves.not.toThrow();
      await expect(serviceWithoutControllers.showAlert({ message: 'Test' })).resolves.not.toThrow();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      const mockToast = {
        dismiss: jest.fn().mockResolvedValue(undefined)
      };

      // Add active toast
      (alertService as any).activeToasts.add(mockToast);

      await alertService.cleanup();

      expect(mockToast.dismiss).toHaveBeenCalled();
    });
  });
});
