/**
 * StorageService - Unified Storage Management
 * Provides type-safe storage operations for both native (Capacitor Preferences) and web (localStorage/sessionStorage)
 * Handles storage quota limits, cleanup, and complex object serialization
 */

import { Capacitor } from '@capacitor/core';
import { Preferences } from '@capacitor/preferences';
import { debugLog } from '../config/environment.config';

/**
 * Storage configuration options
 */
export interface StorageConfig {
  enableDebugLogging: boolean;
  maxStorageSize: number; // Maximum storage size in bytes
  enableCompression: boolean; // Enable data compression for large objects
  enableEncryption: boolean; // Enable basic encryption for sensitive data
  cleanupThreshold: number; // Cleanup when storage exceeds this percentage
}

/**
 * Storage operation result
 */
export interface StorageResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  size?: number; // Size in bytes
}

/**
 * Storage statistics
 */
export interface StorageStats {
  totalKeys: number;
  estimatedSize: number; // Estimated size in bytes
  platform: string;
  isNative: boolean;
  quotaExceeded: boolean;
}

/**
 * Storage key metadata
 */
interface StorageMetadata {
  key: string;
  size: number;
  timestamp: number;
  type: string;
  compressed?: boolean;
  encrypted?: boolean;
}

/**
 * StorageService class for unified storage management
 */
class StorageServiceClass {
  private isNative = Capacitor.getPlatform() !== 'web';
  private config: StorageConfig;
  private metadata: Map<string, StorageMetadata> = new Map();

  constructor() {
    this.config = {
      enableDebugLogging: true,
      maxStorageSize: 10 * 1024 * 1024, // 10MB default
      enableCompression: false, // Disabled by default for simplicity
      enableEncryption: false, // Disabled by default for simplicity
      cleanupThreshold: 0.8 // Cleanup when 80% full
    };

    this.log('StorageService initialized', { 
      isNative: this.isNative,
      platform: Capacitor.getPlatform()
    });

    // Initialize metadata tracking
    this.initializeMetadata();
  }

  /**
   * Configure the storage service
   */
  configure(config: Partial<StorageConfig>): void {
    this.config = { ...this.config, ...config };
    this.log('StorageService configured', this.config);
  }

  /**
   * Store a value with type safety
   */
  async set<T>(key: string, value: T): Promise<StorageResult<T>> {
    try {
      const serializedValue = JSON.stringify(value);
      const size = new Blob([serializedValue]).size;

      // Check storage quota before storing
      const stats = await this.getStats();
      if (stats.estimatedSize + size > this.config.maxStorageSize) {
        if (stats.estimatedSize / this.config.maxStorageSize > this.config.cleanupThreshold) {
          await this.performCleanup();
        } else {
          return {
            success: false,
            error: 'Storage quota exceeded'
          };
        }
      }

      if (this.isNative) {
        await Preferences.set({ key, value: serializedValue });
      } else {
        localStorage.setItem(key, serializedValue);
      }

      // Update metadata
      this.updateMetadata(key, size, typeof value);

      this.log(`Stored value for key: ${key}`, { size, type: typeof value });

      return {
        success: true,
        data: value,
        size
      };

    } catch (error) {
      this.log(`Error storing value for key: ${key}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown storage error'
      };
    }
  }

  /**
   * Retrieve a value with type safety
   */
  async get<T>(key: string, defaultValue?: T): Promise<StorageResult<T>> {
    try {
      let serializedValue: string | null = null;

      if (this.isNative) {
        const result = await Preferences.get({ key });
        serializedValue = result.value;
      } else {
        serializedValue = localStorage.getItem(key);
      }

      if (serializedValue === null) {
        if (defaultValue !== undefined) {
          return {
            success: true,
            data: defaultValue
          };
        }
        return {
          success: false,
          error: 'Key not found'
        };
      }

      const parsedValue = JSON.parse(serializedValue) as T;
      
      this.log(`Retrieved value for key: ${key}`, { type: typeof parsedValue });

      return {
        success: true,
        data: parsedValue,
        size: new Blob([serializedValue]).size
      };

    } catch (error) {
      this.log(`Error retrieving value for key: ${key}`, error);
      
      if (defaultValue !== undefined) {
        return {
          success: true,
          data: defaultValue
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown retrieval error'
      };
    }
  }

  /**
   * Remove a value
   */
  async remove(key: string): Promise<StorageResult<void>> {
    try {
      if (this.isNative) {
        await Preferences.remove({ key });
      } else {
        localStorage.removeItem(key);
      }

      // Remove from metadata
      this.metadata.delete(key);

      this.log(`Removed value for key: ${key}`);

      return { success: true };

    } catch (error) {
      this.log(`Error removing value for key: ${key}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown removal error'
      };
    }
  }

  /**
   * Clear all storage
   */
  async clear(): Promise<StorageResult<void>> {
    try {
      if (this.isNative) {
        await Preferences.clear();
      } else {
        localStorage.clear();
      }

      // Clear metadata
      this.metadata.clear();

      this.log('Storage cleared');

      return { success: true };

    } catch (error) {
      this.log('Error clearing storage:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown clear error'
      };
    }
  }

  /**
   * Get all keys
   */
  async keys(): Promise<StorageResult<string[]>> {
    try {
      let keys: string[] = [];

      if (this.isNative) {
        const result = await Preferences.keys();
        keys = result.keys;
      } else {
        keys = Object.keys(localStorage);
      }

      this.log(`Retrieved ${keys.length} keys`);

      return {
        success: true,
        data: keys
      };

    } catch (error) {
      this.log('Error retrieving keys:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown keys error'
      };
    }
  }

  /**
   * Check if a key exists
   */
  async has(key: string): Promise<boolean> {
    const result = await this.get(key);
    return result.success;
  }

  /**
   * Get storage statistics
   */
  async getStats(): Promise<StorageStats> {
    const keysResult = await this.keys();
    const keys = keysResult.data || [];
    
    let estimatedSize = 0;
    for (const [, metadata] of this.metadata) {
      estimatedSize += metadata.size;
    }

    // Check if quota is exceeded (simplified check)
    const quotaExceeded = estimatedSize > this.config.maxStorageSize;

    return {
      totalKeys: keys.length,
      estimatedSize,
      platform: Capacitor.getPlatform(),
      isNative: this.isNative,
      quotaExceeded
    };
  }

  /**
   * Initialize metadata tracking
   */
  private async initializeMetadata(): Promise<void> {
    try {
      const keysResult = await this.keys();
      if (!keysResult.success || !keysResult.data) return;

      for (const key of keysResult.data) {
        const result = await this.get(key);
        if (result.success && result.size) {
          this.updateMetadata(key, result.size, typeof result.data);
        }
      }

      this.log(`Initialized metadata for ${this.metadata.size} keys`);
    } catch (error) {
      this.log('Error initializing metadata:', error);
    }
  }

  /**
   * Update metadata for a key
   */
  private updateMetadata(key: string, size: number, type: string): void {
    this.metadata.set(key, {
      key,
      size,
      timestamp: Date.now(),
      type
    });
  }

  /**
   * Perform storage cleanup when quota is exceeded
   */
  private async performCleanup(): Promise<void> {
    this.log('Performing storage cleanup...');

    // Sort by timestamp (oldest first)
    const sortedMetadata = Array.from(this.metadata.values())
      .sort((a, b) => a.timestamp - b.timestamp);

    // Remove oldest 25% of items
    const itemsToRemove = Math.floor(sortedMetadata.length * 0.25);
    
    for (let i = 0; i < itemsToRemove; i++) {
      const metadata = sortedMetadata[i];
      await this.remove(metadata.key);
    }

    this.log(`Cleanup completed, removed ${itemsToRemove} items`);
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableDebugLogging) {
      debugLog(`[StorageService] ${message}`, data);
    }
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    this.metadata.clear();
    this.log('StorageService cleaned up');
  }
}

// Export singleton instance
export const StorageService = new StorageServiceClass();

// Export class for testing
export { StorageServiceClass };
