/**
 * AlertService - User Notification Management
 * Provides unified alert, toast, and confirmation dialog functionality
 * Integrates with Ionic's alert and toast controllers for consistent UI
 */

import { debugLog } from '../config/environment.config';

/**
 * Alert types for different notification scenarios
 */
export type AlertType = 'success' | 'error' | 'warning' | 'info';

/**
 * Toast position options
 */
export type ToastPosition = 'top' | 'middle' | 'bottom';

/**
 * Alert button configuration
 */
export interface AlertButton {
  text: string;
  role?: 'cancel' | 'destructive' | 'confirm';
  cssClass?: string;
  handler?: () => void | Promise<void>;
}

/**
 * Toast configuration
 */
export interface ToastOptions {
  message: string;
  duration?: number;
  position?: ToastPosition;
  color?: string;
  cssClass?: string;
  buttons?: AlertButton[];
  showCloseButton?: boolean;
}

/**
 * Alert configuration
 */
export interface AlertOptions {
  header?: string;
  subHeader?: string;
  message: string;
  buttons?: AlertButton[];
  cssClass?: string;
  backdropDismiss?: boolean;
}

/**
 * Confirmation dialog configuration
 */
export interface ConfirmationOptions {
  header?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmHandler?: () => void | Promise<void>;
  cancelHandler?: () => void | Promise<void>;
  cssClass?: string;
}

/**
 * Alert service configuration
 */
export interface AlertServiceConfig {
  enableDebugLogging: boolean;
  defaultToastDuration: number;
  defaultToastPosition: ToastPosition;
  enableHapticFeedback: boolean;
}

/**
 * AlertService class for managing user notifications
 */
class AlertServiceClass {
  private config: AlertServiceConfig;
  private toastController: any = null;
  private alertController: any = null;
  private activeToasts: Set<any> = new Set();
  private activeAlerts: Set<any> = new Set();

  constructor() {
    this.config = {
      enableDebugLogging: true,
      defaultToastDuration: 3000,
      defaultToastPosition: 'bottom',
      enableHapticFeedback: true
    };

    this.log('AlertService initialized');
    this.initializeControllers();
  }

  /**
   * Configure the alert service
   */
  configure(config: Partial<AlertServiceConfig>): void {
    this.config = { ...this.config, ...config };
    this.log('AlertService configured', this.config);
  }

  /**
   * Initialize Ionic controllers
   */
  private async initializeControllers(): Promise<void> {
    try {
      // Import controllers from @ionic/core
      const { toastController, alertController } = await import('@ionic/core/components');
      this.toastController = toastController;
      this.alertController = alertController;
      this.log('Ionic controllers initialized');
    } catch (error) {
      console.error('Failed to initialize Ionic controllers:', error);
      this.setupFallbackControllers();
    }
  }

  /**
   * Setup fallback controllers for testing/development
   */
  private setupFallbackControllers(): void {
    this.toastController = {
      create: async (options: any) => ({
        present: async () => console.log('Toast (fallback):', options.message),
        dismiss: async () => {}
      })
    };

    this.alertController = {
      create: async (options: any) => ({
        present: async () => console.log('Alert (fallback):', options.message),
        dismiss: async () => {}
      })
    };
  }

  /**
   * Show a success toast notification
   */
  async showSuccess(message: string, options?: Partial<ToastOptions>): Promise<void> {
    await this.showToast({
      message,
      color: 'success',
      duration: this.config.defaultToastDuration,
      ...options
    });
  }

  /**
   * Show an error toast notification
   */
  async showError(message: string, options?: Partial<ToastOptions>): Promise<void> {
    await this.showToast({
      message,
      color: 'danger',
      duration: this.config.defaultToastDuration * 2, // Show errors longer
      ...options
    });
  }

  /**
   * Show a warning toast notification
   */
  async showWarning(message: string, options?: Partial<ToastOptions>): Promise<void> {
    await this.showToast({
      message,
      color: 'warning',
      duration: this.config.defaultToastDuration * 1.5,
      ...options
    });
  }

  /**
   * Show an info toast notification
   */
  async showInfo(message: string, options?: Partial<ToastOptions>): Promise<void> {
    await this.showToast({
      message,
      color: 'primary',
      duration: this.config.defaultToastDuration,
      ...options
    });
  }

  /**
   * Show a generic toast notification
   */
  async showToast(options: ToastOptions): Promise<void> {
    if (!this.toastController) {
      console.warn('Toast controller not available');
      return;
    }

    try {
      const toastOptions = {
        message: options.message,
        duration: options.duration || this.config.defaultToastDuration,
        position: options.position || this.config.defaultToastPosition,
        color: options.color || 'medium',
        cssClass: options.cssClass || 'custom-toast',
        buttons: options.showCloseButton ? [
          {
            text: 'Cerrar',
            role: 'cancel'
          }
        ] : options.buttons
      };

      const toast = await this.toastController.create(toastOptions);
      this.activeToasts.add(toast);

      // Remove from active toasts when dismissed
      toast.onDidDismiss().then(() => {
        this.activeToasts.delete(toast);
      });

      await toast.present();
      this.log('Toast presented', { message: options.message, color: options.color });

      // Trigger haptic feedback if enabled
      if (this.config.enableHapticFeedback) {
        this.triggerHapticFeedback(options.color || 'medium');
      }

    } catch (error) {
      console.error('Failed to show toast:', error);
    }
  }

  /**
   * Show an alert dialog
   */
  async showAlert(options: AlertOptions): Promise<void> {
    if (!this.alertController) {
      console.warn('Alert controller not available');
      return;
    }

    try {
      const alertOptions = {
        header: options.header || 'Información',
        subHeader: options.subHeader,
        message: options.message,
        buttons: options.buttons || [
          {
            text: 'Aceptar',
            role: 'confirm'
          }
        ],
        cssClass: options.cssClass || 'custom-alert',
        backdropDismiss: options.backdropDismiss !== false
      };

      const alert = await this.alertController.create(alertOptions);
      this.activeAlerts.add(alert);

      // Remove from active alerts when dismissed
      alert.onDidDismiss().then(() => {
        this.activeAlerts.delete(alert);
      });

      await alert.present();
      this.log('Alert presented', { header: options.header, message: options.message });

    } catch (error) {
      console.error('Failed to show alert:', error);
    }
  }

  /**
   * Show a confirmation dialog
   */
  async showConfirmation(options: ConfirmationOptions): Promise<boolean> {
    return new Promise(async (resolve) => {
      const alertOptions: AlertOptions = {
        header: options.header || 'Confirmar',
        message: options.message,
        buttons: [
          {
            text: options.cancelText || 'Cancelar',
            role: 'cancel',
            handler: async () => {
              if (options.cancelHandler) {
                await options.cancelHandler();
              }
              resolve(false);
            }
          },
          {
            text: options.confirmText || 'Confirmar',
            role: 'confirm',
            handler: async () => {
              if (options.confirmHandler) {
                await options.confirmHandler();
              }
              resolve(true);
            }
          }
        ],
        cssClass: options.cssClass || 'confirmation-alert',
        backdropDismiss: false
      };

      await this.showAlert(alertOptions);
    });
  }

  /**
   * Dismiss all active toasts
   */
  async dismissAllToasts(): Promise<void> {
    const dismissPromises = Array.from(this.activeToasts).map(toast => 
      toast.dismiss().catch((error: any) => 
        console.warn('Error dismissing toast:', error)
      )
    );

    await Promise.all(dismissPromises);
    this.activeToasts.clear();
    this.log('All toasts dismissed');
  }

  /**
   * Dismiss all active alerts
   */
  async dismissAllAlerts(): Promise<void> {
    const dismissPromises = Array.from(this.activeAlerts).map(alert => 
      alert.dismiss().catch((error: any) => 
        console.warn('Error dismissing alert:', error)
      )
    );

    await Promise.all(dismissPromises);
    this.activeAlerts.clear();
    this.log('All alerts dismissed');
  }

  /**
   * Dismiss all active notifications
   */
  async dismissAll(): Promise<void> {
    await Promise.all([
      this.dismissAllToasts(),
      this.dismissAllAlerts()
    ]);
  }

  /**
   * Trigger haptic feedback based on notification type
   */
  private async triggerHapticFeedback(type: string): Promise<void> {
    try {
      // Import Haptics plugin if available
      const { Haptics, ImpactStyle } = await import('@capacitor/haptics');
      
      let impactStyle = ImpactStyle.Medium;
      
      switch (type) {
        case 'success':
          impactStyle = ImpactStyle.Light;
          break;
        case 'danger':
        case 'warning':
          impactStyle = ImpactStyle.Heavy;
          break;
        default:
          impactStyle = ImpactStyle.Medium;
      }

      await Haptics.impact({ style: impactStyle });
    } catch (error) {
      // Haptics not available or failed, continue silently
      this.log('Haptic feedback not available');
    }
  }

  /**
   * Get active notification counts
   */
  getActiveCount(): { toasts: number; alerts: number; total: number } {
    const toasts = this.activeToasts.size;
    const alerts = this.activeAlerts.size;
    return {
      toasts,
      alerts,
      total: toasts + alerts
    };
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableDebugLogging) {
      debugLog(`[AlertService] ${message}`, data);
    }
  }

  /**
   * Cleanup method
   */
  async cleanup(): Promise<void> {
    await this.dismissAll();
    this.log('AlertService cleaned up');
  }
}

// Export singleton instance
export const AlertService = new AlertServiceClass();

// Export class for testing
export { AlertServiceClass };
