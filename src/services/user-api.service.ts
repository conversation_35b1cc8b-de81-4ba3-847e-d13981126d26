import { User, Student } from "../contexts/UserContext";
import { debugLog } from "../config/user-manager.config";
import { EnhancedHttpService } from "./enhanced-http.service";
import { environmentConfig, debugApiLog } from "../config/environment.config";
import {
  AvailableTenantsResponse,
  RelatedUsersResponse,
  LoginContext,
  EncodedLoginContext,
  RelatedUser,
  API_ENDPOINTS,
  CACHE_KEYS,
  CachedUserProfile,
  DEFAULT_CACHE_CONFIG,
} from "../types/api.types";
import { Preferences } from "@capacitor/preferences";

/**
 * Service for fetching user data from Santillana's APIs
 * Implements real API integration with caching and error handling
 */
export class UserApiService {
  /**
   * Fetch user profile and children data from Santillana's backend
   * @param oidcProfile - The OIDC profile from Santillana Connect
   * @param accessToken - The access token for API calls
   */
  static async fetchUserProfile(
    oidcProfile: any,
    accessToken: string
  ): Promise<User> {
    try {
      debugApiLog(
        "UserApiService - Fetching user profile from Santillana APIs",
        {
          userId: oidcProfile.sub,
          email: oidcProfile.email,
          hasAccessToken: !!accessToken,
        }
      );

      // Check cache first
      const cachedProfile = await this.getCachedUserProfile();
      if (cachedProfile && this.isCacheValid(cachedProfile)) {
        debugApiLog("UserApiService - Using cached user profile");
        return this.convertCachedToUser(cachedProfile);
      }

      // Step 1: Fetch available tenants to get loginContext
      const tenantsResponse = await this.fetchAvailableTenants();
      debugApiLog("UserApiService - Available tenants fetched", {
        tenantsCount: tenantsResponse.tenants.length,
        hasLoginContext: !!tenantsResponse.loginContext,
      });

      // Step 2: Decode loginContext to get userId
      const loginContext = this.decodeLoginContext(
        tenantsResponse.loginContext
      );
      debugApiLog("UserApiService - LoginContext decoded", {
        userId: loginContext.userId,
        roleId: loginContext.roleId,
      });

      // Step 3: Fetch related users (children) using loginContext
      const relatedUsersResponse = await this.fetchRelatedUsers(
        loginContext.userId,
        tenantsResponse.loginContext
      );
      debugApiLog("UserApiService - Related users fetched", {
        relatedUsersCount: relatedUsersResponse.relatedUsers.length,
        hasRelatedUsers: relatedUsersResponse.hasRelatedUsers,
      });

      // Step 4: Convert API data to User format
      const children = this.convertRelatedUsersToStudents(
        relatedUsersResponse.relatedUsers
      );

      const user: User = {
        id: oidcProfile.sub || loginContext.userId || "unknown",
        name: oidcProfile.name || oidcProfile.preferred_username || "Usuario",
        email: oidcProfile.email || "<EMAIL>",
        phone:
          oidcProfile.phone_number || oidcProfile.phone || "+34 123 456 789",
        role: this.extractUserRole(oidcProfile),
        avatar:
          oidcProfile.picture ||
          "https://ionicframework.com/docs/img/demos/avatar.svg",
        memberSince: this.extractMemberSince(oidcProfile),
        children: children,
      };

      // Step 5: Cache the result
      await this.cacheUserProfile(user, tenantsResponse, loginContext);

      debugApiLog("UserApiService - User profile fetched successfully", {
        userName: user.name,
        userEmail: user.email,
        childrenCount: user.children.length,
      });

      return user;
    } catch (error) {
      console.error("UserApiService - Error fetching user profile:", error);

      // Try to return cached data as fallback
      const cachedProfile = await this.getCachedUserProfile();
      if (cachedProfile) {
        debugApiLog("UserApiService - Using cached profile as fallback");
        return this.convertCachedToUser(cachedProfile);
      }

      throw new Error("Failed to fetch user profile from Santillana APIs");
    }
  }

  /**
   * Fetch user's children/students from Santillana's backend
   * @param userId - The user ID from OIDC
   * @param accessToken - The access token for API calls
   * @deprecated Use fetchUserProfile instead, which includes children data
   */
  static async fetchUserChildren(
    userId: string,
    accessToken: string
  ): Promise<Student[]> {
    try {
      debugApiLog("UserApiService - Fetching user children (legacy method)", {
        userId,
      });

      // Get cached profile first
      const cachedProfile = await this.getCachedUserProfile();
      if (cachedProfile && this.isCacheValid(cachedProfile)) {
        debugApiLog("UserApiService - Returning cached children");
        return this.convertRelatedUsersToStudents(cachedProfile.children);
      }

      // If no cache, try to fetch fresh data
      try {
        const tenantsResponse = await this.fetchAvailableTenants();
        const loginContext = this.decodeLoginContext(
          tenantsResponse.loginContext
        );
        const relatedUsersResponse = await this.fetchRelatedUsers(
          loginContext.userId,
          tenantsResponse.loginContext
        );

        const children = this.convertRelatedUsersToStudents(
          relatedUsersResponse.relatedUsers
        );

        debugApiLog("UserApiService - User children fetched successfully", {
          childrenCount: children.length,
        });

        return children;
      } catch (apiError) {
        debugApiLog(
          "UserApiService - API call failed, returning empty array",
          apiError
        );
        return [];
      }
    } catch (error) {
      console.error("UserApiService - Error fetching user children:", error);
      return []; // Return empty array on error
    }
  }

  /**
   * Extract user role from OIDC profile
   * @param oidcProfile - The OIDC profile
   */
  private static extractUserRole(oidcProfile: any): string {
    // Try to extract role from various possible claims
    if (oidcProfile.role) return oidcProfile.role;
    if (oidcProfile.roles && Array.isArray(oidcProfile.roles))
      return oidcProfile.roles[0];
    if (oidcProfile.groups && Array.isArray(oidcProfile.groups))
      return oidcProfile.groups[0];
    if (oidcProfile["custom:role"]) return oidcProfile["custom:role"];

    // Default role
    return "Padre/Madre";
  }

  /**
   * Extract member since date from OIDC profile
   * @param oidcProfile - The OIDC profile
   */
  private static extractMemberSince(oidcProfile: any): string {
    // Try to extract creation date from various possible claims
    if (oidcProfile.created_at) {
      return new Date(oidcProfile.created_at).getFullYear().toString();
    }
    if (oidcProfile.iat) {
      return new Date(oidcProfile.iat * 1000).getFullYear().toString();
    }

    // Default to current year
    return new Date().getFullYear().toString();
  }

  /**
   * Update user profile in Santillana's backend
   * @param userId - The user ID
   * @param updates - The profile updates
   * @param accessToken - The access token for API calls
   */
  static async updateUserProfile(
    userId: string,
    updates: Partial<User>,
    accessToken: string
  ): Promise<void> {
    try {
      debugLog("UserApiService - Updating user profile in Santillana APIs", {
        userId,
        updates: Object.keys(updates),
      });

      // TODO: Replace with real API call
      // Example: PUT /api/users/{userId}/profile
      // Headers: { Authorization: `Bearer ${accessToken}` }
      // Body: updates

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500));

      debugLog("UserApiService - User profile updated successfully");
    } catch (error) {
      console.error("UserApiService - Error updating user profile:", error);
      throw new Error("Failed to update user profile in Santillana APIs");
    }
  }

  // ============================================================================
  // REAL API IMPLEMENTATION METHODS
  // ============================================================================

  /**
   * Fetch available tenants from Santillana API
   */
  private static async fetchAvailableTenants(): Promise<AvailableTenantsResponse> {
    try {
      debugApiLog("UserApiService - Fetching available tenants");

      const url = `${environmentConfig.apiBaseUrl}${API_ENDPOINTS.availableTenants.path}`;
      const response = await EnhancedHttpService.get<AvailableTenantsResponse>(
        url,
        {
          timeout: environmentConfig.apiTimeout,
          loadingMessage: "Obteniendo información de usuario...",
          operation: "fetch-available-tenants",
        }
      );

      if (!response.ok || !response.data) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      debugApiLog("UserApiService - Available tenants fetched successfully", {
        tenantsCount: response.data.tenants?.length || 0,
        hasLoginContext: !!response.data.loginContext,
      });

      return response.data;
    } catch (error) {
      debugApiLog("UserApiService - Error fetching available tenants", error);
      throw error;
    }
  }

  /**
   * Fetch related users (children) from Santillana API
   */
  private static async fetchRelatedUsers(
    userId: string,
    loginContext: EncodedLoginContext
  ): Promise<RelatedUsersResponse> {
    try {
      debugApiLog("UserApiService - Fetching related users", { userId });

      const path = API_ENDPOINTS.relatedUsers.path.replace("{userId}", userId);
      const url = `${
        environmentConfig.apiBaseUrl
      }${path}?loginContext=${encodeURIComponent(loginContext)}`;

      const response = await EnhancedHttpService.get<RelatedUsersResponse>(
        url,
        {
          timeout: environmentConfig.apiTimeout,
          loadingMessage: "Obteniendo información de hijos...",
          operation: "fetch-related-users",
        }
      );

      if (!response.ok || !response.data) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      debugApiLog("UserApiService - Related users fetched successfully", {
        relatedUsersCount: response.data.relatedUsers?.length || 0,
        hasRelatedUsers: response.data.hasRelatedUsers,
      });

      return response.data;
    } catch (error) {
      debugApiLog("UserApiService - Error fetching related users", error);
      throw error;
    }
  }

  /**
   * Decode base64 loginContext to extract user information
   */
  private static decodeLoginContext(
    encodedLoginContext: EncodedLoginContext
  ): LoginContext {
    try {
      debugApiLog("UserApiService - Decoding loginContext");

      const decodedString = atob(encodedLoginContext);
      const loginContext = JSON.parse(decodedString) as LoginContext;

      debugApiLog("UserApiService - LoginContext decoded successfully", {
        userId: loginContext.userId,
        roleId: loginContext.roleId,
      });

      return loginContext;
    } catch (error) {
      debugApiLog("UserApiService - Error decoding loginContext", error);
      throw new Error("Failed to decode loginContext");
    }
  }

  /**
   * Convert RelatedUser[] to Student[] format
   */
  private static convertRelatedUsersToStudents(
    relatedUsers: RelatedUser[]
  ): Student[] {
    return relatedUsers.map((relatedUser) => {
      // Get the primary school level session (first active one)
      const primarySession =
        relatedUser.schoolLevelSessions.find((session) => session.isActive) ||
        relatedUser.schoolLevelSessions[0];

      return {
        id: relatedUser.userId,
        name: `${relatedUser.name} ${relatedUser.surnames}`.trim(),
        grade:
          primarySession?.grade || primarySession?.levelName || "Sin grado",
        school: primarySession?.schoolName || "Sin escuela",
        avatar: relatedUser.image || "/api/placeholder/25/25",
        // Enhanced fields from API
        surnames: relatedUser.surnames,
        login: relatedUser.login,
        schoolLevelSessions: relatedUser.schoolLevelSessions.map((session) => ({
          id: session.id,
          schoolId: session.schoolId,
          schoolName: session.schoolName,
          levelId: session.levelId,
          levelName: session.levelName,
          grade: session.grade,
          academicYear: session.academicYear,
          isActive: session.isActive,
        })),
        isActive: relatedUser.isActive,
        metadata: relatedUser.metadata,
      };
    });
  }

  // ============================================================================
  // CACHING METHODS
  // ============================================================================

  /**
   * Get cached user profile from Capacitor Preferences
   */
  private static async getCachedUserProfile(): Promise<CachedUserProfile | null> {
    try {
      const { value } = await Preferences.get({ key: CACHE_KEYS.USER_PROFILE });
      if (value) {
        return JSON.parse(value) as CachedUserProfile;
      }
      return null;
    } catch (error) {
      debugApiLog("UserApiService - Error getting cached profile", error);
      return null;
    }
  }

  /**
   * Check if cached profile is still valid
   */
  private static isCacheValid(cachedProfile: CachedUserProfile): boolean {
    const now = new Date().getTime();
    const expiresAt = new Date(cachedProfile.expiresAt).getTime();
    const lastUpdated = new Date(cachedProfile.lastUpdated).getTime();

    // Check if cache has expired
    if (now > expiresAt) {
      debugApiLog("UserApiService - Cache expired");
      return false;
    }

    // Check if cache is within refresh threshold
    const age = now - lastUpdated;
    const ttl = DEFAULT_CACHE_CONFIG.ttl;
    const refreshThreshold = ttl * DEFAULT_CACHE_CONFIG.refreshThreshold;

    if (age > refreshThreshold) {
      debugApiLog("UserApiService - Cache needs refresh", {
        age,
        refreshThreshold,
      });
      return false;
    }

    return true;
  }

  /**
   * Cache user profile data
   */
  private static async cacheUserProfile(
    user: User,
    tenantsResponse: AvailableTenantsResponse,
    loginContext: LoginContext
  ): Promise<void> {
    try {
      const now = new Date();
      const cachedProfile: CachedUserProfile = {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          role: user.role,
          avatar: user.avatar,
          memberSince: user.memberSince,
        },
        children: this.convertStudentsToRelatedUsers(user.children),
        loginContext: tenantsResponse.loginContext,
        tenants: tenantsResponse.tenants,
        lastUpdated: now.toISOString(),
        expiresAt: new Date(
          now.getTime() + DEFAULT_CACHE_CONFIG.ttl
        ).toISOString(),
      };

      await Preferences.set({
        key: CACHE_KEYS.USER_PROFILE,
        value: JSON.stringify(cachedProfile),
      });

      debugApiLog("UserApiService - User profile cached successfully");
    } catch (error) {
      debugApiLog("UserApiService - Error caching user profile", error);
    }
  }

  /**
   * Convert cached profile back to User format
   */
  private static convertCachedToUser(cachedProfile: CachedUserProfile): User {
    return {
      id: cachedProfile.user.id,
      name: cachedProfile.user.name,
      email: cachedProfile.user.email,
      phone: cachedProfile.user.phone,
      role: cachedProfile.user.role,
      avatar: cachedProfile.user.avatar,
      memberSince: cachedProfile.user.memberSince,
      children: this.convertRelatedUsersToStudents(cachedProfile.children),
    };
  }

  /**
   * Convert Student[] back to RelatedUser[] for caching
   */
  private static convertStudentsToRelatedUsers(
    students: Student[]
  ): RelatedUser[] {
    return students.map((student) => ({
      userId: student.id,
      name: student.surnames
        ? student.name.replace(` ${student.surnames}`, "")
        : student.name.split(" ")[0] || student.name,
      surnames:
        student.surnames || student.name.split(" ").slice(1).join(" ") || "",
      login: student.login || student.id,
      image: student.avatar,
      schoolLevelSessions: student.schoolLevelSessions || [
        {
          id: `session-${student.id}`,
          schoolId: `school-${student.id}`,
          schoolName: student.school,
          levelId: `level-${student.id}`,
          levelName: student.grade,
          grade: student.grade,
          isActive: true,
        },
      ],
      isActive: student.isActive,
      metadata: student.metadata,
    }));
  }

  /**
   * Clear cached user profile
   */
  static async clearCache(): Promise<void> {
    try {
      await Preferences.remove({ key: CACHE_KEYS.USER_PROFILE });
      await Preferences.remove({ key: CACHE_KEYS.LOGIN_CONTEXT });
      await Preferences.remove({ key: CACHE_KEYS.TENANTS });
      await Preferences.remove({ key: CACHE_KEYS.LAST_SYNC });
      debugApiLog("UserApiService - Cache cleared successfully");
    } catch (error) {
      debugApiLog("UserApiService - Error clearing cache", error);
    }
  }

  /**
   * Refresh user profile data (for pull-to-refresh functionality)
   * Forces a fresh API call and updates the cache
   */
  static async refreshUserProfile(
    oidcProfile: any,
    accessToken: string
  ): Promise<User> {
    try {
      debugApiLog("UserApiService - Refreshing user profile (forced refresh)");

      // Clear cache to force fresh data
      await this.clearCache();

      // Fetch fresh data
      return await this.fetchUserProfile(oidcProfile, accessToken);
    } catch (error) {
      debugApiLog("UserApiService - Error refreshing user profile", error);

      // Try to return cached data as fallback
      const cachedProfile = await this.getCachedUserProfile();
      if (cachedProfile) {
        debugApiLog(
          "UserApiService - Using cached profile as fallback after refresh error"
        );
        return this.convertCachedToUser(cachedProfile);
      }

      throw error;
    }
  }

  /**
   * Check if user profile data is available (cached or fresh)
   */
  static async isUserProfileAvailable(): Promise<boolean> {
    try {
      const cachedProfile = await this.getCachedUserProfile();
      return !!cachedProfile;
    } catch (error) {
      debugApiLog(
        "UserApiService - Error checking profile availability",
        error
      );
      return false;
    }
  }

  /**
   * Get cache status information
   */
  static async getCacheStatus(): Promise<{
    hasCache: boolean;
    isValid: boolean;
    lastUpdated?: string;
    expiresAt?: string;
    age?: number;
  }> {
    try {
      const cachedProfile = await this.getCachedUserProfile();
      if (!cachedProfile) {
        return { hasCache: false, isValid: false };
      }

      const isValid = this.isCacheValid(cachedProfile);
      const now = new Date().getTime();
      const lastUpdated = new Date(cachedProfile.lastUpdated).getTime();
      const age = now - lastUpdated;

      return {
        hasCache: true,
        isValid,
        lastUpdated: cachedProfile.lastUpdated,
        expiresAt: cachedProfile.expiresAt,
        age,
      };
    } catch (error) {
      debugApiLog("UserApiService - Error getting cache status", error);
      return { hasCache: false, isValid: false };
    }
  }
}
