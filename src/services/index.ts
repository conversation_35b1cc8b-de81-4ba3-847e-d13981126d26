/**
 * Services Index
 * Essential application services for Agenda Familiar
 */

// Authentication Services
export { authService } from './authService';
export { AuthStorageService } from './auth-storage.service';
export { CapacitorAuthService } from './capacitor-auth.service';
export { UserApiService } from './user-api.service';

// Core Infrastructure Services
export { LoadingService, LoadingServiceClass } from './loading.service';
export { ErrorHandlerService, ErrorHandlerServiceClass } from './error-handler.service';
export { EnhancedHttpService, EnhancedHttpServiceClass } from './enhanced-http.service';
export { StorageService, StorageServiceClass } from './storage.service';
export { AlertService, AlertServiceClass } from './alert.service';

// Export types for convenience
export type { LoadingEvent, LoadingEventType } from './loading.service';
export type { ErrorEvent, ErrorEventType } from './error-handler.service';

// Export storage types
export type {
  StorageResult,
  StorageStats,
  StorageConfig
} from './storage.service';

// Export alert types
export type {
  AlertType,
  ToastPosition,
  AlertButton,
  ToastOptions,
  AlertOptions,
  ConfirmationOptions
} from './alert.service';
