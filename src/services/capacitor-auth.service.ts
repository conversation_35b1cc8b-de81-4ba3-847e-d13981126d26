/**
 * Modern Capacitor OIDC authentication service
 * Implements secure WebView-based authentication flow for iOS and Android
 * Uses Capacitor InAppBrowser with proper deep link handling
 */

import { Capacitor, CapacitorCookies, CapacitorHttp } from '@capacitor/core';
import { InAppBrowser, DefaultSystemBrowserOptions, iOSViewStyle } from '@capacitor/inappbrowser';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { environmentConfig } from '../config/environment.config';
import { CryptoFallback } from '../utils/crypto-fallback';

/**
 * Authentication logging utility
 */
class AuthLogger {
  static log(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ${message}`;

    if (data !== undefined) {
      console.log(logMessage, data);
    } else {
      console.log(logMessage);
    }
  }

  static error(message: string, error?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ❌ ${message}`;
    console.error(logMessage, error || '');
  }

  static warn(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ⚠️ ${message}`;
    console.warn(logMessage, data || '');
  }

  static info(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ℹ️ ${message}`;
    console.info(logMessage, data || '');
  }
}

interface AuthState {
  state: string;
  codeVerifier: string;
  nonce: string;
  redirectUri: string;
  timestamp: number;
}

export interface AuthResult {
  accessToken: string;
  idToken: string;
  refreshToken?: string;
  expiresIn: number;
  profile: {
    sub: string;
    name?: string;
    email?: string;
    [key: string]: any;
  };
}

export class CapacitorAuthService {
  private static authInProgress = false;
  private static authPromiseResolve: ((value: AuthResult) => void) | null = null;
  private static authPromiseReject: ((reason?: any) => void) | null = null;
  private static deepLinkListener: any = null;

  /**
   * Initialize the authentication service
   * Sets up deep link listeners for handling callbacks
   */
  static async initialize(): Promise<void> {
    try {
      AuthLogger.log(' [AUTH] Initializing CapacitorAuthService');

      // Set up deep link listener for handling callbacks
      if (Capacitor.isNativePlatform()) {
        AuthLogger.log(' [AUTH] Setting up deep link listener');

        // Remove any existing listener to prevent duplicates
        if (this.deepLinkListener) {
          this.deepLinkListener.remove();
          this.deepLinkListener = null;
        }

        // Add new listener
        this.deepLinkListener = await App.addListener('appUrlOpen', async (event) => {
          AuthLogger.log(' [AUTH] Deep link received:', event.url);

          // Check if this is an authentication callback
          if (event.url.includes('/callback') && this.authInProgress) {
            AuthLogger.log(' [AUTH] Processing authentication callback');

            // Close InAppBrowser if it's still open
            try {
              await InAppBrowser.close();
            } catch (e) {
              // Ignore errors if browser is already closed
            }

            // Handle the callback
            await this.handleDeepLink(event.url);
          }
        });

        AuthLogger.log('✅ [AUTH] Deep link listener set up successfully');
      } else {
        AuthLogger.log('ℹ️ [AUTH] Not a native platform, skipping deep link setup');
      }

      AuthLogger.log('✅ [AUTH] CapacitorAuthService initialized successfully');
    } catch (error) {
      AuthLogger.error('Error initializing CapacitorAuthService:', error);
      throw error;
    }
  }

  /**
   * Check for existing authentication without forcing fresh login
   * Use this method to restore existing sessions on app startup
   */
  static async silentSignIn(): Promise<AuthResult | null> {
    if (!Capacitor.isNativePlatform()) {
      return null;
    }

    try {
      AuthLogger.log('🔍 [AUTH] Checking for existing authentication session');
      const existingUser = await this.getCurrentUser();

      if (existingUser) {
        AuthLogger.log('👤 [AUTH] Found existing valid session', {
          userId: existingUser.profile.sub,
          userName: existingUser.profile.name,
          userEmail: existingUser.profile.email
        });
        return existingUser;
      }

      AuthLogger.log('❌ [AUTH] No existing valid session found');
      return null;
    } catch (error) {
      AuthLogger.error('Error during silent sign in:', error);
      return null;
    }
  }

  /**
   * Sign in user with OAuth2 PKCE flow
   */
  static async signIn(): Promise<AuthResult> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('CapacitorAuthService is only for native platforms');
    }

    if (this.authInProgress) {
      AuthLogger.warn('Authentication already in progress');
      throw new Error('Authentication already in progress');
    }

    try {
      // Clear browser session to ensure fresh authentication
      await this.clearBrowserSession();

      // Start authentication flow
      return await this.startAuthentication();

    } catch (error) {
      this.cleanup();
      throw error;
    }
  }

  /**
   * Generate authentication state and PKCE parameters
   */
  private static async generateAuthState(): Promise<AuthState> {
    // Generate random state
    const state = CryptoFallback.generateRandomString(32);

    // Generate PKCE code verifier (random string)
    const codeVerifier = CryptoFallback.generateRandomString(64);

    // Generate nonce for OpenID Connect
    const nonce = CryptoFallback.generateRandomString(32);

    // Use the correct redirect URI - must match what's registered with the provider
    const redirectUri = 'capacitor://localhost/callback';

    return {
      state,
      codeVerifier,
      nonce,
      redirectUri,
      timestamp: Date.now()
    };
  }

  /**
   * Store authentication state securely
   */
  private static async storeAuthState(authState: AuthState): Promise<void> {
    try {
      // Store the complete auth state object
      await Preferences.set({
        key: 'auth_state_object',
        value: JSON.stringify(authState)
      });

      // Also store individual parameters for easier access in callback
      await Preferences.set({ key: 'auth_code_verifier', value: authState.codeVerifier });
      await Preferences.set({ key: 'auth_state', value: authState.state });
      await Preferences.set({ key: 'auth_nonce', value: authState.nonce });
      await Preferences.set({ key: 'auth_redirect_uri', value: authState.redirectUri });

      AuthLogger.log('💾 [AUTH] Authentication state stored securely');
    } catch (error) {
      AuthLogger.error('Error storing authentication state:', error);
      throw error;
    }
  }

  /**
   * Build authorization URL with PKCE parameters
   */
  private static async buildAuthorizationUrl(authState: AuthState): Promise<string> {
    try {
      // Generate code challenge from verifier
      const codeChallenge = await CryptoFallback.generateCodeChallenge(authState.codeVerifier);

      // Build URL with all required parameters
      const url = new URL(`${environmentConfig.authority}/connect/authorize`);

      // Standard OAuth2 parameters - use minimal set to avoid errors
      url.searchParams.append('client_id', environmentConfig.clientId);
      url.searchParams.append('redirect_uri', authState.redirectUri);
      url.searchParams.append('response_type', 'code');
      url.searchParams.append('scope', environmentConfig.scope);
      url.searchParams.append('state', authState.state);

      // PKCE parameters - try with explicit method
      url.searchParams.append('code_challenge', codeChallenge);
      url.searchParams.append('code_challenge_method', 'S256');

      // OpenID Connect parameters
      url.searchParams.append('nonce', authState.nonce);

      // Add minimal login parameters
      url.searchParams.append('prompt', 'login');

      // Explicitly set response_mode to query
      url.searchParams.append('response_mode', 'query');

      // Add random parameter to prevent caching
      url.searchParams.append('_', Date.now().toString());

      const authUrl = url.toString();
      AuthLogger.log('🔗 [AUTH] Built authorization URL', {
        urlPreview: authUrl.substring(0, 100) + '...',
        redirectUri: authState.redirectUri
      });

      return authUrl;
    } catch (error) {
      AuthLogger.error('Error building authorization URL:', error);
      throw new Error('Failed to build authorization URL');
    }
  }



  /**
   * Exchange authorization code for tokens
   */
  private static async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<any> {
    try {
      AuthLogger.log('🔄 [AUTH] Exchanging code for tokens');

      // Prepare token request parameters
      const tokenEndpoint = `${environmentConfig.authority}/connect/token`;
      const redirectUri = `capacitor://localhost/callback`;

      // Create form data for token request
      const formData = new URLSearchParams();
      formData.append('client_id', environmentConfig.clientId);
      formData.append('grant_type', 'authorization_code');
      formData.append('code', code);
      formData.append('redirect_uri', redirectUri);
      formData.append('code_verifier', codeVerifier);

      // Log token request details (without sensitive data)
      AuthLogger.log('🔄 [AUTH] Token request:', {
        endpoint: tokenEndpoint,
        clientId: environmentConfig.clientId,
        redirectUri,
        hasCodeVerifier: !!codeVerifier
      });

      // Check if we can use native HTTP (to bypass CORS)
      if (Capacitor.isNativePlatform() && Capacitor.isPluginAvailable('CapacitorHttp')) {
        AuthLogger.log('🔄 [AUTH] Using native HTTP for token request (bypasses CORS)');

        // Make token request using Capacitor's native HTTP
        const response = await CapacitorHttp.request({
          url: tokenEndpoint,
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          data: formData.toString()
        });

        // Check for HTTP errors
        if (response.status < 200 || response.status >= 300) {
          AuthLogger.error(`❌ [AUTH] Token request failed: ${response.status} - ${JSON.stringify(response.data)}`);
          throw new Error(`Token request failed: ${response.status}`);
        }

        // Parse token response
        const tokenResponse = response.data;

        // Validate token response
        if (!tokenResponse.access_token) {
          AuthLogger.error('❌ [AUTH] Invalid token response:', tokenResponse);
          throw new Error('Invalid token response');
        }

        AuthLogger.log('✅ [AUTH] Token exchange successful using native HTTP');

        return tokenResponse;
      } else {
        // Fallback to fetch API (will likely fail with CORS in browser)
        AuthLogger.log('⚠️ [AUTH] Using fetch API for token request (may fail with CORS)');

        // Make token request
        const response = await fetch(tokenEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData.toString()
        });

        // Check for HTTP errors
        if (!response.ok) {
          const errorText = await response.text();
          AuthLogger.error(`❌ [AUTH] Token request failed: ${response.status} - ${errorText}`);
          throw new Error(`Token request failed: ${response.status}`);
        }

        // Parse token response
        const tokenResponse = await response.json();

        // Validate token response
        if (!tokenResponse.access_token) {
          AuthLogger.error('❌ [AUTH] Invalid token response:', tokenResponse);
          throw new Error('Invalid token response');
        }

        AuthLogger.log('✅ [AUTH] Token exchange successful');

        return tokenResponse;
      }
    } catch (error) {
      AuthLogger.error('❌ [AUTH] Error exchanging code for tokens:', error);
      throw error;
    }
  }

  /**
   * Store authentication result securely
   */
  private static async storeAuthResult(authResult: AuthResult): Promise<void> {
    try {
      await Preferences.set({
        key: 'auth_tokens',
        value: JSON.stringify({
          accessToken: authResult.accessToken,
          idToken: authResult.idToken,
          refreshToken: authResult.refreshToken,
          expiresAt: Date.now() + (authResult.expiresIn * 1000),
          profile: authResult.profile
        })
      });

      AuthLogger.log('Auth result stored securely');
    } catch (error) {
      AuthLogger.error('Error storing auth result:', error);
      throw new Error('Failed to store authentication result');
    }
  }

  /**
   * Parse JWT payload
   */
  private static parseJwtPayload(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      AuthLogger.error('Error parsing JWT payload:', error);
      throw new Error('Failed to parse ID token');
    }
  }



  /**
   * Clean up stored authentication state
   */
  private static async cleanupAuthState(state: string): Promise<void> {
    try {
      await Preferences.remove({ key: `auth_state_${state}` });
      AuthLogger.log('Auth state cleaned up');
    } catch (error) {
      AuthLogger.error('Error cleaning up auth state:', error);
    }
  }

  /**
   * Clear browser session data to ensure fresh authentication
   * This prevents cached credentials from being reused
   */
  private static async clearBrowserSession(): Promise<void> {
    try {
      AuthLogger.log('🧹 [AUTH] Clearing browser session data for fresh authentication');

      // Clear all cookies to remove any cached authentication state
      if (Capacitor.isPluginAvailable('CapacitorCookies')) {
        AuthLogger.log('🍪 [AUTH] Clearing all browser cookies');
        await CapacitorCookies.clearAllCookies();

        // Also clear cookies specifically for the OAuth2 provider domain
        const oauthDomain = new URL(environmentConfig.authority).hostname;
        AuthLogger.log('🍪 [AUTH] Clearing cookies for OAuth2 provider domain:', oauthDomain);

        try {
          await CapacitorCookies.clearCookies({
            url: environmentConfig.authority
          });
        } catch (domainError) {
          AuthLogger.warn('Could not clear domain-specific cookies:', domainError);
        }
      } else {
        AuthLogger.warn('CapacitorCookies plugin not available, cannot clear browser cookies');
      }

      // Force logout from OAuth2 provider by opening logout URL briefly
      await this.forceOAuth2Logout();

      AuthLogger.log('✅ [AUTH] Browser session cleared successfully');
    } catch (error) {
      AuthLogger.error('Error clearing browser session:', error);
      // Don't throw error here - authentication can still proceed
      // The user will just need to manually log out if cached credentials exist
    }
  }

  /**
   * Sign out user and clear stored tokens with aggressive browser cleanup
   */
  static async signOut(): Promise<void> {
    try {
      AuthLogger.log('🔐 [AUTH] Performing aggressive sign out with complete browser cleanup');

      // 1. Clear WebView authentication state directly
      await this.clearWebViewAuthState();

      // 2. Clear browser session data
      await this.clearBrowserSession();

      // 3. Clear all Capacitor Preferences
      await Preferences.clear();
      AuthLogger.log('🧹 [AUTH] All Capacitor Preferences cleared');

      // 4. Clear all localStorage items
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          AuthLogger.log(`🧹 [AUTH] Removing localStorage item: ${key}`);
          localStorage.removeItem(key);
        }
      }

      // 5. Clear all sessionStorage items
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key) {
          sessionStorage.removeItem(key);
        }
      }

      // 6. Force clear cookies using CapacitorCookies
      if (Capacitor.isPluginAvailable('CapacitorCookies')) {
        AuthLogger.log('🍪 [AUTH] Clearing all cookies');
        await CapacitorCookies.clearAllCookies();

        // Also clear cookies for specific domains
        const domains = [
          environmentConfig.authority,
          'https://pre-identity.santillanaconnect.com',
          'pre-identity.santillanaconnect.com',
          'identity.santillanaconnect.com',
          'santillanaconnect.com'
        ];

        for (const domain of domains) {
          try {
            await CapacitorCookies.clearCookies({ url: `https://${domain}` });
            AuthLogger.log(`🍪 [AUTH] Cleared cookies for domain: ${domain}`);
          } catch (e) {
            AuthLogger.warn(`Could not clear cookies for domain: ${domain}`, e);
          }
        }
      }

      // 7. Force logout from OAuth2 provider with extended timeout
      await this.forceOAuth2Logout(true);

      // 8. Clean up any pending authentication
      this.cleanup();

      AuthLogger.log('✅ [AUTH] Aggressive sign out completed');
    } catch (error) {
      AuthLogger.error('Error during sign out:', error);

      // Even if there's an error, try to clean up as much as possible
      try {
        await Preferences.clear();
        localStorage.clear();
        sessionStorage.clear();
        if (Capacitor.isPluginAvailable('CapacitorCookies')) {
          await CapacitorCookies.clearAllCookies();
        }
        this.cleanup();
      } catch (e) {
        AuthLogger.error('Error during emergency cleanup:', e);
      }

      throw error;
    }
  }

  /**
   * Force logout from OAuth2 provider to clear server-side session
   * Uses a hidden iframe approach to avoid disrupting user experience
   */
  private static async forceOAuth2Logout(silent: boolean = false): Promise<void> {
    try {
      if (!silent) {
        AuthLogger.log('🚪 [AUTH] Forcing logout from OAuth2 provider');
      }

      AuthLogger.log('🌐 [AUTH] Performing silent logout to clear server session');

      // Use the correct endpoint: connect/endsession
      const logoutUrl = `${environmentConfig.authority}/connect/endsession`;

      // Use a valid HTTP URL for post_logout_redirect_uri instead of capacitor://
      // The server is rejecting the capacitor:// scheme
      const redirectUri = 'https://pre-identity.santillanaconnect.com/';

      // Add post_logout_redirect_uri parameter with a valid HTTP URL
      const fullLogoutUrl = `${logoutUrl}?post_logout_redirect_uri=${encodeURIComponent(redirectUri)}`;

      // Try using HTTP request first
      try {
        // Make HTTP request to logout endpoint
        const response = await CapacitorHttp.request({
          url: fullLogoutUrl,
          method: 'GET'
        });

        if (response.status >= 200 && response.status < 300) {
          AuthLogger.log('✅ [AUTH] Silent logout completed successfully');
          return;
        } else {
          AuthLogger.log('⚠️ [AUTH] Silent logout HTTP request failed, trying browser approach');
        }
      } catch (httpError) {
        AuthLogger.warn('HTTP logout request failed, falling back to browser approach:', httpError);
      }

      // Fallback to browser-based logout
      await this.fallbackBrowserLogout(fullLogoutUrl, true);

    } catch (error) {
      AuthLogger.warn('Error during OAuth2 logout:', error);
      // Continue execution even if logout fails
    }
  }

  /**
   * Fallback browser-based logout when HTTP approach fails
   */
  private static async fallbackBrowserLogout(logoutUrl: string, extended = false): Promise<void> {
    try {
      // Open logout URL in system browser briefly to clear server-side session
      await InAppBrowser.openInSystemBrowser({
        url: logoutUrl,
        options: DefaultSystemBrowserOptions
      });

      // Close the browser after a delay to allow logout to complete
      const timeout = extended ? 3000 : 1500; // 3 seconds if extended, 1.5 seconds otherwise

      setTimeout(async () => {
        try {
          await InAppBrowser.close();
          AuthLogger.log('✅ [AUTH] Logout browser closed');
        } catch (closeError) {
          AuthLogger.info('Logout browser already closed or error closing:', closeError);
        }
      }, timeout);

    } catch (error) {
      AuthLogger.warn('Fallback browser logout failed:', error);
    }
  }

  /**
   * Clean up authentication state and promises
   */
  private static cleanup(): void {
    console.log('🧹 [AUTH] Cleaning up authentication state');
    this.authInProgress = false;
    this.authPromiseResolve = null;
    this.authPromiseReject = null;

    // Clean up InAppBrowser listeners (but keep deep link listener for future auth attempts)
    try {
      InAppBrowser.removeAllListeners();
    } catch (e) {
      // Ignore errors when removing listeners
    }

    // Note: Don't remove deep link listener here as it should persist for future auth attempts
    // The listener is only removed when the service is destroyed
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<AuthResult | null> {
    try {
      const result = await Preferences.get({ key: 'auth_tokens' });

      if (!result.value) {
        return null;
      }

      const storedAuth = JSON.parse(result.value);

      // Check if token is expired
      if (Date.now() >= storedAuth.expiresAt) {
        AuthLogger.log('Stored token is expired');
        await this.signOut();
        return null;
      }

      return {
        accessToken: storedAuth.accessToken,
        idToken: storedAuth.idToken,
        refreshToken: storedAuth.refreshToken,
        expiresIn: Math.floor((storedAuth.expiresAt - Date.now()) / 1000),
        profile: storedAuth.profile
      };
    } catch (error) {
      AuthLogger.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Check if authentication is currently in progress
   */
  static isAuthInProgress(): boolean {
    return this.authInProgress;
  }

  /**
   * Debug helper to check authentication state
   */
  static async debugAuthState(): Promise<void> {
    console.log('🔍 [DEBUG] Current authentication state:');
    console.log('  - Auth in progress:', this.authInProgress);
    console.log('  - Has promise resolver:', !!this.authPromiseResolve);
    console.log('  - Has promise rejector:', !!this.authPromiseReject);
    console.log('  - Has deep link listener:', !!this.deepLinkListener);

    try {
      const currentUser = await this.getCurrentUser();
      console.log('  - Current user:', currentUser ? {
        userId: currentUser.profile.sub,
        userName: currentUser.profile.name,
        userEmail: currentUser.profile.email,
        tokenExpiry: new Date(Date.now() + (currentUser.expiresIn * 1000)).toISOString()
      } : 'None');
    } catch (error) {
      console.log('  - Error getting current user:', error);
    }

    try {
      const keys = await Preferences.keys();
      const authKeys = keys.keys.filter(key => key.startsWith('auth_'));
      console.log('  - Stored auth keys:', authKeys);
    } catch (error) {
      console.log('  - Error getting stored keys:', error);
    }
  }

  /**
   * Completely clears all authentication data
   */
  static async clearAllAuthData(): Promise<void> {
    console.log('🧹 [AUTH] Clearing all authentication data');

    // Clear WebView authentication state directly
    await this.clearWebViewAuthState();

    // Clear localStorage
    localStorage.clear();

    // Clear sessionStorage
    sessionStorage.clear();

    // Clear Capacitor Preferences
    if (Capacitor.isPluginAvailable('Preferences')) {
      await Preferences.clear();
    }

    // Clear cookies
    if (Capacitor.isPluginAvailable('CapacitorCookies')) {
      await CapacitorCookies.clearAllCookies();

      // Also clear cookies for specific domains
      const domains = [
        'https://pre-identity.santillanaconnect.com',
        'pre-identity.santillanaconnect.com',
        'identity.santillanaconnect.com',
        'santillanaconnect.com',
        'localhost',
      ];

      for (const domain of domains) {
        try {
          await CapacitorCookies.clearCookies({ url: `https://${domain}` });
        } catch (e) {
          console.warn(`Could not clear cookies for domain: ${domain}`, e);
        }
      }
    }

    // Force logout from OAuth2 provider
    try {
      await this.forceOAuth2Logout(true);
    } catch (e) {
      console.warn('Could not force OAuth2 logout:', e);
    }

    console.log('🧹 [AUTH] All authentication data cleared');
  }



  /**
   * Clear WebView authentication state using direct URL access
   * This method directly accesses the identity provider's logout endpoint
   */
  private static async clearWebViewAuthState(): Promise<void> {
    try {
      AuthLogger.log('🔄 [AUTH] Clearing WebView authentication state with minimal UI');

      // Use the correct endpoint: connect/endsession
      const logoutUrl = `${environmentConfig.authority}/connect/endsession`;

      // Use a valid HTTP URL for post_logout_redirect_uri instead of capacitor://
      const redirectUri = 'https://pre-identity.santillanaconnect.com/';

      // Add post_logout_redirect_uri parameter with a valid HTTP URL
      const fullLogoutUrl = `${logoutUrl}?post_logout_redirect_uri=${encodeURIComponent(redirectUri)}`;

      AuthLogger.log('🔄 [AUTH] Accessing logout endpoint with hidden WebView:', fullLogoutUrl);

      // Use InAppBrowser in system browser to ensure cookies are properly cleared
      // This is necessary because WebView cookies need to be cleared through the browser context
      await InAppBrowser.openInSystemBrowser({
        url: fullLogoutUrl,
        options: {
          ...DefaultSystemBrowserOptions,
          iOS: {
            ...DefaultSystemBrowserOptions.iOS,
            viewStyle: iOSViewStyle.FORM_SHEET // Smaller, less intrusive presentation
          }
        }
      });

      // Reduced wait time - just enough for the logout to process
      await new Promise(resolve => setTimeout(resolve, 600));

      // Close the WebView quickly
      try {
        await InAppBrowser.close();
      } catch (e) {
        AuthLogger.warn('Could not close WebView after logout:', e);
      }

      AuthLogger.log('✅ [AUTH] WebView authentication state cleared with minimal UI impact');
    } catch (e) {
      AuthLogger.warn('Error clearing WebView authentication state:', e);
    }
  }

  /**
   * Start authentication flow with InAppBrowser
   */
  private static async startAuthentication(): Promise<AuthResult> {
    try {
      AuthLogger.log('🔐 [AUTH] Starting authentication with InAppBrowser');
      this.authInProgress = true;

      // Clear browser session to ensure fresh authentication
      await this.clearBrowserSession();

      // Generate PKCE parameters and state
      AuthLogger.log('🔑 [AUTH] Generating authentication state and PKCE parameters');
      const authState = await this.generateAuthState();

      // Store auth state securely
      AuthLogger.log('💾 [AUTH] Storing authentication state securely');
      await this.storeAuthState(authState);

      // Verify state was stored correctly
      const storedState = await Preferences.get({ key: 'auth_state' });
      AuthLogger.log('🔍 [AUTH] Verifying stored state:', {
        generatedState: authState.state,
        storedState: storedState.value
      });

      if (!storedState.value || storedState.value !== authState.state) {
        AuthLogger.error('❌ [AUTH] Failed to store state correctly');
        throw new Error('Failed to store authentication state');
      }

      // Build authorization URL with minimal parameters
      AuthLogger.log('🔗 [AUTH] Building authorization URL');
      const authUrl = await this.buildAuthorizationUrl(authState);

      // Add response_mode=query to ensure callback parameters are in query string
      const finalUrl = new URL(authUrl);
      finalUrl.searchParams.append('response_mode', 'query');
      finalUrl.searchParams.append('prompt', 'login');

      AuthLogger.log('🌐 [AUTH] Opening secure WebView', {
        urlPreview: finalUrl.toString().substring(0, 100) + '...',
        state: authState.state,
        redirectUri: authState.redirectUri
      });

      // Set up deep link listener BEFORE opening browser
      await this.setupDeepLinkListener();

      // Use the default system browser options
      await InAppBrowser.openInSystemBrowser({
        url: finalUrl.toString(),
        options: {
          ...DefaultSystemBrowserOptions,
          iOS: {
            ...DefaultSystemBrowserOptions.iOS,
            viewStyle: iOSViewStyle.PAGE_SHEET
          }
        }
      });

      AuthLogger.log('✅ [AUTH] WebView opened successfully, waiting for callback');

      // Return promise that resolves when authentication completes
      return new Promise<AuthResult>((resolve, reject) => {
        this.authPromiseResolve = resolve;
        this.authPromiseReject = reject;

        // Set up browser close detection with delay to avoid false positives
        let authCompleted = false;
        const browserCloseListener = () => {
          // Add a small delay to check if authentication was completed
          setTimeout(() => {
            if (this.authInProgress && !authCompleted) {
              AuthLogger.warn('🚫 [AUTH] Browser closed by user - cancelling authentication');
              this.cleanup();
              reject(new Error('Authentication cancelled by user'));
            }
          }, 1000); // 1 second delay to allow callback processing
        };

        // Listen for browser close events
        InAppBrowser.addListener('browserClosed', browserCloseListener);

        // Set authentication timeout
        const authTimeout = setTimeout(() => {
          if (this.authInProgress) {
            AuthLogger.error('Authentication timeout after 5 minutes');
            this.cleanup();
            InAppBrowser.removeAllListeners();
            reject(new Error('Authentication timeout - please try again'));
          }
        }, 300000); // 5 minutes timeout

        // Clean up listeners when promise resolves/rejects
        const originalResolve = resolve;
        const originalReject = reject;

        this.authPromiseResolve = (result: AuthResult) => {
          authCompleted = true; // Mark authentication as completed successfully
          clearTimeout(authTimeout);
          InAppBrowser.removeAllListeners();
          originalResolve(result);
        };

        this.authPromiseReject = (error: Error) => {
          authCompleted = true; // Mark authentication as completed (even if failed)
          clearTimeout(authTimeout);
          InAppBrowser.removeAllListeners();
          originalReject(error);
        };
      });
    } catch (error) {
      AuthLogger.error('Error starting authentication:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * Set up deep link listener for authentication callback
   */
  private static async setupDeepLinkListener(): Promise<void> {
    try {
      // Remove any existing listener
      if (this.deepLinkListener) {
        this.deepLinkListener.remove();
      }

      // Add new listener with improved logging
      this.deepLinkListener = await App.addListener('appUrlOpen', async (event) => {
        AuthLogger.log('🔗 [AUTH] Deep link received:', event.url);

        // Check if this is an authentication callback
        if (event.url.includes('/callback') && this.authInProgress) {
          AuthLogger.log('🔗 [AUTH] Processing authentication callback from deep link');

          // Close InAppBrowser if it's still open
          try {
            await InAppBrowser.close();
          } catch (e) {
            // Ignore errors if browser is already closed
          }

          // Handle the callback
          await this.handleDeepLink(event.url);
        } else {
          AuthLogger.log('🔗 [AUTH] Deep link is not an auth callback or auth not in progress', {
            url: event.url.substring(0, 30) + '...',
            authInProgress: this.authInProgress
          });
        }
      });

      AuthLogger.log('✅ [AUTH] Deep link listener set up successfully');
    } catch (error) {
      AuthLogger.error(`Error setting up deep link listener: ${error}`);
    }
  }

  /**
   * Handle deep link callback from OAuth provider
   */
  private static async handleDeepLink(url: string): Promise<void> {
    try {
      AuthLogger.log('🔐 [AUTH] Received deep link callback:', url.substring(0, 100) + '...');

      // Check if this is an OIDC callback URL
      if (!url.includes('/callback') && !url.includes('code=') && !url.includes('error=')) {
        AuthLogger.info('URL is not an OIDC callback, ignoring');
        return;
      }

      AuthLogger.log('🔒 [AUTH] Valid OIDC callback detected, processing...');

      // Close the WebView
      try {
        AuthLogger.log('🚪 [AUTH] Closing WebView');
        await InAppBrowser.close();
      } catch (closeError) {
        AuthLogger.info('WebView already closed or error closing:', closeError);
      }

      // Parse the URL to extract code and state
      const parsedUrl = new URL(url);
      const params = new URLSearchParams(parsedUrl.search);
      const code = params.get('code');
      const state = params.get('state');
      const error = params.get('error');

      // Handle errors in the callback
      if (error) {
        const errorDescription = params.get('error_description') || 'No description';
        AuthLogger.error(`🔒 [AUTH] Error in callback: ${error} - ${errorDescription}`);

        if (this.authPromiseReject) {
          this.authPromiseReject(new Error(`Authentication error: ${error} - ${errorDescription}`));
        }

        this.cleanup();
        return;
      }

      // Validate required parameters
      if (!code || !state) {
        const errorMsg = !code ? 'No authorization code in callback' : 'No state parameter in callback';
        AuthLogger.error(`🔒 [AUTH] ${errorMsg}`);

        if (this.authPromiseReject) {
          this.authPromiseReject(new Error(errorMsg));
        }

        this.cleanup();
        return;
      }

      // Debug: Log all stored preferences to diagnose state issues
      const keys = await Preferences.keys();
      AuthLogger.log('🔍 [AUTH] Available preference keys:', keys.keys);

      // Verify state parameter to prevent CSRF attacks
      const storedState = await Preferences.get({ key: 'auth_state' });

      AuthLogger.log('🔍 [AUTH] State verification:', {
        receivedState: state,
        storedState: storedState.value
      });

      if (!storedState.value) {
        // Try to get state from the complete auth state object as fallback
        const authStateObj = await Preferences.get({ key: 'auth_state_object' });
        if (authStateObj.value) {
          const parsedAuthState = JSON.parse(authStateObj.value);
          if (parsedAuthState.state === state) {
            AuthLogger.log('✅ [AUTH] State verification passed using auth_state_object fallback');
            // Continue with token exchange
            await this.processCallbackWithCode(code, state);
            return;
          }
        }

        AuthLogger.error(`🔒 [AUTH] No stored state found`);
        if (this.authPromiseReject) {
          this.authPromiseReject(new Error('No stored state found - authentication session may have expired'));
        }
        this.cleanup();
        return;
      }

      if (state !== storedState.value) {
        AuthLogger.error(`🔒 [AUTH] State mismatch: ${state} vs ${storedState.value}`);

        if (this.authPromiseReject) {
          this.authPromiseReject(new Error('Invalid state parameter - possible security issue'));
        }

        this.cleanup();
        return;
      }

      AuthLogger.log('✅ [AUTH] State verification passed, exchanging code for tokens');
      await this.processCallbackWithCode(code, state);

    } catch (error) {
      AuthLogger.error(`🔒 [AUTH] Error handling deep link: ${error}`);

      if (this.authPromiseReject) {
        this.authPromiseReject(error);
      }

      this.cleanup();
    }
  }

  /**
   * Process callback with verified code and state
   */
  private static async processCallbackWithCode(code: string, state: string): Promise<void> {
    try {
      // Process the authorization code
      AuthLogger.log('🔄 [AUTH] Processing authorization code');
      const authResult = await this.processAuthCallback(code, state);

      // Clean up the auth state
      AuthLogger.log('🧹 [AUTH] Cleaning up auth state');
      await this.cleanupAuthState(state);

      // Store tokens securely
      AuthLogger.log('💾 [AUTH] Storing authentication result securely');
      await this.storeAuthResult(authResult);

      // Resolve the authentication promise
      AuthLogger.log('✅ [AUTH] Resolving authentication promise');
      if (this.authPromiseResolve) {
        this.authPromiseResolve(authResult);
      } else {
        AuthLogger.warn('No promise resolver available');
      }
    } catch (error) {
      AuthLogger.error(`🔒 [AUTH] Error processing callback: ${error}`);

      if (this.authPromiseReject) {
        this.authPromiseReject(error);
      }
    } finally {
      this.cleanup();
    }
  }

  /**
   * Process authentication callback
   */
  private static async processAuthCallback(code: string, _state: string): Promise<AuthResult> {
    try {
      AuthLogger.log('🔄 [AUTH] Processing authentication callback');

      // Retrieve code verifier from storage
      const codeVerifier = await Preferences.get({ key: 'auth_code_verifier' });
      if (!codeVerifier.value) {
        throw new Error('Code verifier not found');
      }

      // Exchange code for tokens
      AuthLogger.log('🔄 [AUTH] Exchanging code for tokens');
      const tokenResponse = await this.exchangeCodeForTokens(code, codeVerifier.value);

      // Parse ID token to extract user profile
      const profile = this.parseJwtPayload(tokenResponse.id_token);

      // Create auth result
      const authResult: AuthResult = {
        accessToken: tokenResponse.access_token,
        idToken: tokenResponse.id_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in || 3600,
        profile: {
          sub: profile.sub,
          name: profile.name || profile.preferred_username || profile.sub,
          email: profile.email,
          ...profile
        }
      };

      AuthLogger.log('✅ [AUTH] Authentication callback processed successfully');

      return authResult;
    } catch (error) {
      AuthLogger.error('Error processing authentication callback:', error);
      throw error;
    }
  }


}
