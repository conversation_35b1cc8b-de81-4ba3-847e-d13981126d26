import { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { CapacitorAuthService, AuthResult } from '../services/capacitor-auth.service';
import { ROUTES } from '../routes/routes';

interface UseAuthReturn {
  user: AuthResult | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
}

export const useAuth = (): UseAuthReturn => {
  const [user, setUser] = useState<AuthResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const history = useHistory();

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const currentUser = await CapacitorAuthService.getCurrentUser();

        if (currentUser) {
          setUser(currentUser);
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
        }
      } catch (err) {
        console.error('Error checking authentication status:', err);
        await CapacitorAuthService.signOut();
        setIsAuthenticated(false);
        setUser(null);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const authResult = await CapacitorAuthService.signIn();

      if (authResult) {
        setUser(authResult);
        setIsAuthenticated(true);
        history.replace(ROUTES.HOME);
      }
    } catch (err) {
      console.error('Authentication error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error de autenticación con Santillana Connect';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await CapacitorAuthService.signOut();
      setUser(null);
      setError(null);
      setIsAuthenticated(false);
      history.replace(ROUTES.AUTH);
    } catch (err) {
      console.error('Logout error:', err);
      // Force local cleanup even if remote logout fails
      setUser(null);
      setError(null);
      setIsAuthenticated(false);
      history.replace(ROUTES.AUTH);
    }
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    error
  };
};
