import { useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';

/**
 * Hook to detect the currently active tab and update the sliding indicator
 */
export const useActiveTab = () => {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState<string>('inicio');

  useEffect(() => {
    const path = location.pathname;
    
    // Extract tab name from path
    if (path.startsWith('/tabs/')) {
      const tabName = path.split('/tabs/')[1];
      if (tabName) {
        setActiveTab(tabName);
      }
    }
  }, [location.pathname]);

  useEffect(() => {
    // Update the data attribute on the tab bar for CSS animations
    const tabBar = document.querySelector('.figma-tabbar');
    if (tabBar) {
      tabBar.setAttribute('data-active-tab', activeTab);
    }
  }, [activeTab]);

  return {
    activeTab,
    isTabActive: (tabName: string) => activeTab === tabName
  };
};
