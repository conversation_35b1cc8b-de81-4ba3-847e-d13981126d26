import { useHistory, useLocation } from 'react-router-dom';
import { useEffect, useRef } from 'react';

/**
 * Hook to control navigation within tabs context
 * Prevents navigation outside of /tabs routes
 */
export const useRestrictedNavigation = () => {
  const history = useHistory();
  const location = useLocation();
  const tabsHistoryRef = useRef<string[]>([]);

  useEffect(() => {
    const currentPath = location.pathname;
    
    // Track navigation within tabs
    if (currentPath.startsWith('/tabs')) {
      // Add to tabs history if it's a new path
      const lastPath = tabsHistoryRef.current[tabsHistoryRef.current.length - 1];
      if (lastPath !== currentPath) {
        tabsHistoryRef.current.push(currentPath);
        
        // Keep only last 10 entries to prevent memory issues
        if (tabsHistoryRef.current.length > 10) {
          tabsHistoryRef.current = tabsHistoryRef.current.slice(-10);
        }
      }
    } else if (currentPath === '/account') {
      // Account page is accessible from tabs, keep tabs history
      // but don't add account to tabs history
    } else {
      // Clear tabs history when completely outside tabs context
      tabsHistoryRef.current = [];
    }
  }, [location.pathname]);

  const canGoBackInTabs = (): boolean => {
    const currentPath = location.pathname;
    
    // For account page, can go back if we have tabs history
    if (currentPath === '/account') {
      return tabsHistoryRef.current.length > 0;
    }
    
    // For tabs routes, can go back if we have more than one entry
    if (currentPath.startsWith('/tabs')) {
      return tabsHistoryRef.current.length > 1;
    }
    
    return false;
  };

  const getRestrictedDefaultHref = (): string => {
    const currentPath = location.pathname;
    
    // For account page, return to last visited tab
    if (currentPath === '/account') {
      const lastTab = tabsHistoryRef.current[tabsHistoryRef.current.length - 1];
      return lastTab || '/tabs/inicio';
    }
    
    // For tabs routes, return to previous tab or default
    if (currentPath.startsWith('/tabs') && tabsHistoryRef.current.length > 1) {
      return tabsHistoryRef.current[tabsHistoryRef.current.length - 2];
    }
    
    return '/tabs/inicio';
  };

  const handleRestrictedBack = (): boolean => {
    if (!canGoBackInTabs()) {
      return false;
    }

    const defaultHref = getRestrictedDefaultHref();
    history.push(defaultHref);
    return true;
  };

  return {
    canGoBackInTabs,
    getRestrictedDefaultHref,
    handleRestrictedBack,
    isInTabsContext: location.pathname.startsWith('/tabs') || location.pathname === '/account',
    tabsHistory: tabsHistoryRef.current
  };
};
