/**
 * Test Setup for Agenda Familiar
 * Global test configuration and polyfills
 */

// Mock console methods to reduce test noise
const originalConsole = { ...console };

beforeAll(() => {
  // Mock console methods for cleaner test output
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  console.info = jest.fn();
});

afterAll(() => {
  // Restore console methods
  Object.assign(console, originalConsole);
});

// Mock environment config
jest.mock('./config/environment.config', () => ({
  debugLog: jest.fn(),
  environmentConfig: {
    debugAuth: false,
    authority: 'https://test-auth.example.com',
    clientId: 'test-client-id',
    scope: 'openid profile email'
  }
}));

// Mock window.location
delete (window as any).location;
(window as any).location = {
  href: '',
  origin: 'http://localhost:3000',
  pathname: '/',
  search: '',
  hash: ''
};

// Mock localStorage
const localStorageMock = (() => {
  let store: { [key: string]: string } = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    }
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
const sessionStorageMock = (() => {
  let store: { [key: string]: string } = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    }
  };
})();

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock fetch globally
global.fetch = jest.fn();

// Mock Blob for storage size calculations
global.Blob = jest.fn().mockImplementation((content: any[]) => ({
  size: JSON.stringify(content).length
})) as any;

// Store original setTimeout and clearTimeout
const originalSetTimeout = global.setTimeout;
const originalClearTimeout = global.clearTimeout;

// Mock setTimeout and clearTimeout
global.setTimeout = jest.fn().mockImplementation((fn: Function, delay: number) => {
  // For tests, execute immediately but return a mock timer ID
  if (delay === 0 || process.env.NODE_ENV === 'test') {
    fn();
    return 123; // Mock timer ID
  }
  return originalSetTimeout(fn, delay);
}) as any;

global.clearTimeout = jest.fn().mockImplementation((id: any) => {
  return originalClearTimeout(id);
});

// Mock AbortController for Node.js 14 compatibility
if (!global.AbortController) {
  global.AbortController = class AbortController {
    signal: AbortSignal;

    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn()
      } as any;
    }

    abort() {
      (this.signal as any).aborted = true;
    }
  } as any;
}

// Export test utilities for use in individual test files
export const TestUtils = {
  /**
   * Create a mock HTTP response
   */
  createMockResponse: (data: any, status = 200, ok = true) => ({
    ok,
    status,
    statusText: ok ? 'OK' : 'Error',
    json: jest.fn().mockResolvedValue(data),
    headers: new Headers(),
    url: 'http://test.com/api'
  }),

  /**
   * Create a mock error
   */
  createMockError: (status: number, message: string) => ({
    status,
    message,
    statusText: message
  }),

  /**
   * Wait for a specified amount of time
   */
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * Create a large test object for storage quota tests
   */
  createLargeObject: (sizeInKB: number) => ({
    data: 'x'.repeat(sizeInKB * 1024),
    timestamp: Date.now()
  })
};

// Common test data
export const TestData = {
  user: {
    id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    preferences: {
      theme: 'dark',
      language: 'es'
    }
  },

  student: {
    id: '456',
    name: 'Test Student',
    grade: '5to Grado',
    section: 'A'
  },

  httpResponse: {
    success: {
      data: { message: 'Success' },
      status: 200
    },
    error: {
      status: 500,
      message: 'Internal Server Error'
    },
    unauthorized: {
      status: 401,
      message: 'Unauthorized'
    }
  }
};
