import React, { useState } from 'react';
import {
  IonSearchbar,
  IonCard,
  IonCardContent,
  IonAvatar,
  IonBadge,
  IonButton,
  IonIcon,
  IonText,
  IonChip
} from '@ionic/react';
import { searchOutline, chevronDownOutline } from 'ionicons/icons';
import TabLayout from '../../components/Layout/TabLayout';
import './ConnectionPage.css';

interface MessageData {
  id: string;
  teacherName: string;
  teacherRole: string;
  subject: string;
  message: string;
  timestamp: string;
  unreadCount?: number;
  avatarColor: string;
}

interface CalendarEvent {
  id: string;
  title: string;
  subject: string;
  date: Date;
  type: 'tarea' | 'evaluacion' | 'evento';
  description: string;
}

interface CalendarDay {
  date: number;
  isCurrentMonth: boolean;
  events: CalendarEvent[];
}

const ConnectionPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'eventos' | 'mensajes'>('mensajes');
  const [searchText, setSearchText] = useState('');

  // Function to handle tab change
  const handleTabChange = (tab: 'eventos' | 'mensajes') => {
    setActiveTab(tab);
  };

  // Calendar state
  const [currentDate, setCurrentDate] = useState(new Date(2024, 0, 1)); // January 2024 as shown in Figma
  const [selectedFilters, setSelectedFilters] = useState<string[]>(['tareas', 'evaluaciones', 'eventos']);
  const [eventSearchText, setEventSearchText] = useState('');

  // Mock calendar events data
  const mockCalendarEvents: CalendarEvent[] = [
    {
      id: '1',
      title: 'Desafío Matemático: Álgebra y Geometría',
      subject: 'Matemáticas',
      date: new Date(2024, 0, 14), // January 14, 2024
      type: 'tarea',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.'
    },
    {
      id: '2',
      title: 'Desafío Matemático: Álgebra y Geometría',
      subject: 'Matemáticas',
      date: new Date(2024, 0, 15), // January 15, 2024
      type: 'evaluacion',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.'
    },
    {
      id: '3',
      title: 'Desafío Matemático: Álgebra y Geometría',
      subject: 'Matemáticas',
      date: new Date(2024, 0, 16), // January 16, 2024
      type: 'evento',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.'
    },
    {
      id: '4',
      title: 'Desafío Matemático: Álgebra y Geometría',
      subject: 'Matemáticas',
      date: new Date(2024, 0, 16), // January 16, 2024 (same day, different event)
      type: 'tarea',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.'
    }
  ];

  // Mock data for unanswered messages
  const unansweredMessages: MessageData[] = [
    {
      id: '1',
      teacherName: 'Camilo Espitia R.',
      teacherRole: 'Docente',
      subject: 'Matemáticas',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: 'Hace 2 días',
      unreadCount: 1,
      avatarColor: '#FF6B6B'
    },
    {
      id: '2',
      teacherName: 'Andrea Riaño P.',
      teacherRole: 'Docente',
      subject: 'Inglés',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: 'Hace 6 días',
      unreadCount: 20,
      avatarColor: '#4ECDC4'
    },
    {
      id: '3',
      teacherName: 'Daniel Ortíz V.',
      teacherRole: 'Docente',
      subject: 'Ciencias naturales',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: 'Hace 12 días',
      unreadCount: 3,
      avatarColor: '#45B7D1'
    }
  ];

  // Mock data for all messages
  const allMessages: MessageData[] = [
    {
      id: '4',
      teacherName: 'Camilo Espitia R.',
      teacherRole: 'Docente',
      subject: 'Matemáticas',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: '12 ener',
      avatarColor: '#FF6B6B'
    },
    {
      id: '5',
      teacherName: 'Camilo Espitia R.',
      teacherRole: 'Docente',
      subject: 'Lenguas',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: '09 ener',
      avatarColor: '#96CEB4'
    },
    {
      id: '6',
      teacherName: 'Camilo Espitia R.',
      teacherRole: 'Docente',
      subject: 'Ciencias naturales',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: '06 ener',
      avatarColor: '#FFEAA7'
    },
    {
      id: '7',
      teacherName: 'Camilo Espitia R.',
      teacherRole: 'Docente',
      subject: 'Matemáticas',
      message: 'Recuerda entregar tu tarea de matemátic...',
      timestamp: '03 ener',
      avatarColor: '#DDA0DD'
    }
  ];

  const handleMessageClick = (messageId: string) => {
    console.log('Message clicked:', messageId);
    // Here you would typically navigate to the message detail view
    // or open a message modal
  };

  // Calendar utility functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1).getDay();
    return firstDay === 0 ? 6 : firstDay - 1; // Convert Sunday (0) to be last (6), Monday (1) to be first (0)
  };

  const getEventsForDate = (date: number) => {
    const targetDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), date);
    return mockCalendarEvents.filter(event =>
      event.date.getDate() === date &&
      event.date.getMonth() === targetDate.getMonth() &&
      event.date.getFullYear() === targetDate.getFullYear()
    );
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'tarea': return '#FC71A9';
      case 'evaluacion': return '#8324CC';
      case 'evento': return '#245CCC';
      default: return '#FC71A9';
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const toggleFilter = (filter: string) => {
    setSelectedFilters(prev =>
      prev.includes(filter)
        ? prev.filter(f => f !== filter)
        : [...prev, filter]
    );
  };

  // Calendar constants
  const monthNames = ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'];
  const dayNames = ['Lun', 'Mar', 'Mie', 'Jue', 'Vie', 'Sab', 'Dom'];

  const MessageCard: React.FC<{ message: MessageData; showUnreadBadge?: boolean }> = ({
    message,
    showUnreadBadge = false
  }) => (
    <IonCard
      className="message-card"
      onClick={() => handleMessageClick(message.id)}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleMessageClick(message.id);
        }
      }}
    >
      <IonCardContent className="message-card-content">
        <div className="message-card-layout">
          <IonAvatar className="message-avatar">
            <div
              className="avatar-placeholder"
              style={{ backgroundColor: message.avatarColor }}
            >
              {message.teacherName.split(' ').map(n => n[0]).join('').substring(0, 2)}
            </div>
          </IonAvatar>

          <div className="message-content">
            <div className="message-header">
              <div className="teacher-info">
                <h3 className="teacher-name">{message.teacherName}</h3>
                <div className="teacher-meta">
                  <span className="teacher-role">{message.teacherRole}</span>
                  <div className="subject-divider"></div>
                  <span className="subject-name">{message.subject}</span>
                </div>
              </div>
            </div>

            <p className="message-preview">{message.message}</p>
          </div>

          <div className="message-actions">
            {showUnreadBadge && message.unreadCount && (
              <IonBadge className="unread-badge" color="danger">{message.unreadCount}</IonBadge>
            )}
            <span className="message-timestamp">{message.timestamp}</span>
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentDate);
    const firstDayOfMonth = getFirstDayOfMonth(currentDate);

    // Create calendar grid
    const calendarDays = [];

    // Add previous month's trailing days
    const prevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 0);
    const prevMonthDays = prevMonth.getDate();
    for (let i = firstDayOfMonth - 1; i >= 0; i--) {
      calendarDays.push({
        date: prevMonthDays - i,
        isCurrentMonth: false,
        events: []
      });
    }

    // Add current month's days
    for (let day = 1; day <= daysInMonth; day++) {
      calendarDays.push({
        date: day,
        isCurrentMonth: true,
        events: getEventsForDate(day)
      });
    }

    // Add next month's leading days to complete the grid
    const totalCells = Math.ceil(calendarDays.length / 7) * 7;
    let nextMonthDay = 1;
    while (calendarDays.length < totalCells) {
      calendarDays.push({
        date: nextMonthDay,
        isCurrentMonth: false,
        events: []
      });
      nextMonthDay++;
    }

    return (
      <div className="calendar-container">
        {/* Calendar Header */}
        <div className="calendar-header">
          <button
            className="nav-button"
            onClick={() => navigateMonth('prev')}
            aria-label="Mes anterior"
          >
            <IonIcon icon={chevronDownOutline} className="nav-icon rotate-90" />
          </button>

          <div className="month-year-display">
            <IonText className="month-text">{monthNames[currentDate.getMonth()]}</IonText>
            <IonText className="year-text">{currentDate.getFullYear()}</IonText>
          </div>

          <button
            className="nav-button"
            onClick={() => navigateMonth('next')}
            aria-label="Mes siguiente"
          >
            <IonIcon icon={chevronDownOutline} className="nav-icon -rotate-90" />
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="calendar-grid">
          {/* Day headers */}
          {dayNames.map(day => (
            <div key={day} className="day-header">
              <IonText className="day-header-text">{day}</IonText>
            </div>
          ))}

          {/* Calendar days */}
          {calendarDays.map((day, index) => (
            <div key={index} className="calendar-day">
              <IonText className={`day-number ${!day.isCurrentMonth ? 'other-month' : ''}`}>
                {day.date}
              </IonText>

              {/* Event indicators */}
              {day.events.length > 0 && (
                <div className="event-indicators">
                  {day.events.slice(0, 3).map((event) => (
                    <div
                      key={event.id}
                      className="event-indicator"
                      style={{ backgroundColor: getEventTypeColor(event.type) }}
                    />
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    if (activeTab === 'eventos') {
      return (
        <div className="events-content">
          {/* Calendar */}
          {renderCalendar()}

          {/* Search Bar */}
          <div className="event-search-container">
            <IonSearchbar
              value={eventSearchText}
              onIonInput={(e) => setEventSearchText(e.detail.value!)}
              placeholder="Buscador de eventos próximos"
              className="event-searchbar"
              searchIcon={searchOutline}
            />
          </div>

          {/* Filter Tags */}
          <div className='flex'>
            <IonChip
              className={` ${selectedFilters.includes('tareas') ? 'active tarea' : ''}`}
              onClick={() => toggleFilter('tareas')}
              color={selectedFilters.includes('tareas') ? undefined : 'medium'}
            >
              <IonText>Tareas (9)</IonText>
            </IonChip>
            <IonChip
              className={`${selectedFilters.includes('evaluaciones') ? 'active evaluacion' : ''}`}
              onClick={() => toggleFilter('evaluaciones')}
              color={selectedFilters.includes('evaluaciones') ? undefined : 'medium'}
            >
              <IonText>Evaluaciones (5)</IonText>
            </IonChip>
            <IonChip
              className={` ${selectedFilters.includes('eventos') ? 'active evento' : ''}`}
              onClick={() => toggleFilter('eventos')}
              color={selectedFilters.includes('eventos') ? undefined : 'medium'}
            >
              <IonText>Eventos escolares (4)</IonText>
            </IonChip>

          </div>

          {/* Event Cards */}
          <div className="event-cards">
            {mockCalendarEvents.map(event => (
              <IonCard key={event.id} className="event-card">
                <IonCardContent className="event-card-content">
                  <div className="event-header">
                    <div className="event-meta">
                      <IonChip className={`event-type-chip ${event.type}`}>
                        <IonText className="event-type-text">
                          {event.type === 'tarea' ? 'Tarea' :
                            event.type === 'evaluacion' ? 'Evaluación' : 'Evento escolar'}
                        </IonText>
                      </IonChip>
                      <div className="event-subject-date">
                        <IonText className="event-subject">{event.subject}</IonText>
                        <div className="event-divider"></div>
                        <IonText className="event-date">
                          {event.date.getDate()} de {monthNames[event.date.getMonth()].toLowerCase()}
                        </IonText>
                      </div>
                    </div>
                  </div>
                  <IonText className="event-title">{event.title}</IonText>
                  <IonText className="event-description">{event.description}</IonText>
                </IonCardContent>
              </IonCard>
            ))}
          </div>
        </div>
      );
    }

    // Tab de mensajes
    return (
      <div className="messages-content">
        {/* Search Bar */}
        <IonSearchbar
          value={searchText}
          onIonInput={(e) => setSearchText(e.detail.value!)}
          placeholder="Buscador de mensajes"
          className="custom-searchbar"
          searchIcon={searchOutline}
        />

        {/* Messages Sections */}
        <div className="messages-container">
          {/* Unanswered Messages Section */}
          <div className="messages-section">
            <h2 className="section-title">Mensajes sin responder</h2>
            <div className="messages-list">
              {unansweredMessages.map((message, index) => (
                <React.Fragment key={message.id}>
                  <MessageCard message={message} showUnreadBadge={true} />
                  {index < unansweredMessages.length - 1 && <div className="message-divider"></div>}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* All Messages Section */}
          <div className="messages-section">
            <h2 className="section-title">Mensajes</h2>
            <div className="messages-list">
              {allMessages.map((message, index) => (
                <React.Fragment key={message.id}>
                  <MessageCard message={message} showUnreadBadge={false} />
                  {index < allMessages.length - 1 && <div className="message-divider"></div>}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <TabLayout>
      {/* Tab Navigation */}
      <div className="tab-navigation">
        <div className="tab-container">
          <IonButton
            fill={activeTab === 'eventos' ? 'solid' : 'outline'}
            size="small"
            className={`tab-button ${activeTab === 'eventos' ? 'active' : ''}`}
            onClick={() => handleTabChange('eventos')}
          >
            <span className="tab-text">Eventos próximos</span>
          </IonButton>

          <IonButton
            fill={activeTab === 'mensajes' ? 'solid' : 'outline'}
            size="small"
            className={`tab-button ${activeTab === 'mensajes' ? 'active' : ''}`}
            onClick={() => handleTabChange('mensajes')}
          >
            <span className="tab-text">Mensajes</span>
          </IonButton>
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        {renderTabContent()}
      </div>
    </TabLayout>
  );
};

export default ConnectionPage;
