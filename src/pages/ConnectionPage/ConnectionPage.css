/* Connection Page - Modular IonCard Implementation */

.connection-container {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: none;
  margin: 0;
}

/* Ensure proper display and visibility */
.connection-container .ion-card-content {
  padding: 0;
  display: block;
  visibility: visible;
}

/* Tab Navigation Styles */
.tab-navigation {
  padding: 16px 16px 0 16px;
}

.tab-container {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.tab-button {
  flex: 1;
}

.tab-text {
  font-size: 14px;
}

/* Main Content */
.main-content /* Event Search */
.event-search-container {
  width: 100%;
  padding: 0;
  margin: 16px 0;
}

.event-searchbar {
  --background: var(--ion-card-background);
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  padding: 0 0;
  margin: 0;
}

/* Main Content */
.main-content {
  padding: 16px 16px 0 16px;
  min-height: 200px;
}

/* Tab Navigation - Updated for IonCard structure */

.tab-navigation {
  position: relative;
  z-index: 1;
}

.tab-container {
  display: flex;
  gap: 0.5rem;
}

.tab-button {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  --border-color: var(--ion-color-primary);
  --color-activated: var(--ion-color-primary-contrast);
  --background-activated: var(--ion-color-primary);
  margin: 0;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: none;
  letter-spacing: normal;
  transition: all 0.2s ease-in-out;
}

.tab-button.active {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
}

.tab-button:not(.active) {
  --background: transparent;
  --color: var(--ion-color-primary);
}

/* Content Sections */
.main-content {
  min-height: 200px;
}

.events-content {
  padding: 2rem 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.empty-message {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  margin: 0;
}


.custom-searchbar {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  --placeholder-color: var(--ion-color-medium);
  --icon-color: var(--ion-color-medium);
  --border-radius: 12px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0;
  padding: 0 0
}

.messages-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.messages-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message-divider {
  height: 1px;
  background: var(--ion-color-step-200);
  margin: 0.5rem 0;
}

/* Legacy styles cleaned up - using modular approach above */

.custom-searchbar .searchbar-input {
  --placeholder-color: var(--ion-color-medium);
  --color: var(--ion-text-color);
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  padding: 0;
}



/* Messages Container */
.messages-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Messages Section */
.messages-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section-title {
  font-weight: 600;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
  padding: 0;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Message Card */
.message-card {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  margin: 0 0 16px 0;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}

.message-card-content {
  padding: 16px;
}

.message-card-layout {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border: 1px solid var(--ion-color-step-200);
  border-radius: 50%;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  color: var(--ion-color-primary-contrast);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  margin-bottom: 8px;
}

.teacher-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.teacher-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0;
}

.teacher-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--ion-color-medium);
}

.teacher-role {
  font-weight: 400;
}

.subject-divider {
  width: 4px;
  height: 4px;
  background-color: var(--ion-color-medium);
  border-radius: 50%;
  flex-shrink: 0;
}

.subject-name {
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-preview {
  font-size: 14px;
  font-weight: 400;
  color: var(--ion-color-medium);
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;
}

.message-timestamp {
  font-size: 12px;
  color: var(--ion-color-medium);
}

.message-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 140px);
}

.message-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.teacher-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 2px;
  max-width: 198px;
  min-height: 27px;
}

.teacher-name {
  font-weight: 700;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.teacher-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
}

.teacher-role {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
  white-space: nowrap;
}

.subject-divider {
  width: 0;
  height: 10px;
  border-left: 1px solid var(--ion-color-primary);
  flex-shrink: 0;
}

.subject-name {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-preview {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.message-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
  min-width: 75px;
  height: 100%;
}

.unread-badge {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  border-radius: 24px;
  padding: 4px;
  min-width: 21px;
  width: 21px;
  height: auto;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2px;
}

.message-timestamp {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-color-medium);
  text-align: center;
  width: 75px;
  white-space: nowrap;
}

/* Message Divider */
.message-divider {
  width: 100%;
  height: 0;
  border-top: 0.5px dashed var(--ion-color-step-200);
  margin: 0;
}

/* Events Content */
.events-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

/* Calendar Styles */
.calendar-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 114px;
  padding: 0 16px;
}

.nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  background: var(--ion-card-background);
  border: none;
  border-radius: 6px;
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button:hover {
  transform: translateY(-1px);
  box-shadow: 0px -2px 15px 2px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  width: 8.04px;
  height: 12.84px;
  color: var(--ion-color-primary);
}

.rotate-90 {
  transform: rotate(90deg);
}

.-rotate-90 {
  transform: rotate(-90deg);
}

.month-year-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 40px;
}

.month-text {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-text-color);
}

.year-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-color-medium);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 0;
  width: 340px;
  margin: 0 auto;
}

.day-header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 17px;
  margin-bottom: 31px;
}

.day-header-text {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.21;
  color: var(--ion-color-medium);
}

.calendar-day {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 52px;
  position: relative;
  padding: 0;
}

.day-number {
  font-weight: 500;
  font-size: 15px;
  line-height: 1.33;
  text-align: center;
  color: var(--ion-text-color);
  margin-bottom: 8px;
}

.day-number.other-month {
  color: #D9D9D9;
}

.event-indicators {
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.event-indicator {
  width: 30px;
  height: 5px;
  border-radius: 4px;
}

/* Event Search */
.event-search-container {
  width: 100%;
}

.event-searchbar .searchbar-input {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  padding: 4px 12px;
}




.filter-chip.active.tarea {
  --background: #FC71A9;
  --color: var(--ion-text-color);
}

.filter-chip.active.evaluacion {
  --background: #8324CC;
  --color: var(--ion-text-color);
}

.filter-chip.active.evento {
  --background: #245CCC;
  --color: var(--ion-text-color);
}


/* Ensure proper text styling for chips */
.filter-chip ion-text {
  --color: inherit;
  color: var(--color);
  font-size: 12px;
  font-weight: 500;
}

/* Events Content Layout */
.events-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

/* Event Cards */
.event-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-card {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: 0.5px solid rgba(217, 217, 217, 0.2);
  border-radius: 6px;
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  margin: 0;
}

.event-card-content {
  padding: 16px 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.event-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.event-type-chip {
  --background: #FC71A9;
  --color: var(--ion-text-color);
  border-radius: 24px;
  padding: 4px 12px;
  margin: 0;
  height: 16px;
  min-height: 16px;
}

.event-type-chip.evaluacion {
  --background: #8324CC;
}

.event-type-chip.evento {
  --background: #245CCC;
}

.event-type-text {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
}

.event-subject-date {
  display: flex;
  align-items: center;
  gap: 8px;
}

.event-subject {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
}

.event-divider {
  width: 0;
  height: 10px;
  border-left: 1px solid #45B2FF;
}

.event-date {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
}

.event-title {
  font-weight: 700;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

.event-description {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

.empty-state {
  text-align: center;
}

.empty-message {
  font-weight: 400;
  font-size: 14px;
  line-height: 1.4;
  color: var(--ion-color-medium);
  margin: 0;
}

/* Interactive Effects */
.message-card {
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 8px;
  margin: 0 -8px;
  padding: 8px;
}

.message-card:hover {
  background: rgba(69, 178, 255, 0.05);
  transform: translateY(-1px);
}

.message-card:active {
  transform: translateY(0);
  background: rgba(69, 178, 255, 0.1);
}

.tab-button {
  transition: all 0.2s ease-in-out;
}

.tab-button:hover {
  transform: translateY(-1px);
}

.tab-button:active {
  transform: translateY(0);
}

/* Focus states for accessibility */
.tab-button:focus {
  outline: 2px solid #45B2FF;
  outline-offset: 2px;
}

.message-card:focus {
  outline: 2px solid #45B2FF;
  outline-offset: 2px;
  border-radius: 8px;
}

/* Content containers for each tab */
.events-content {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.messages-content {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

/* Ensure proper display for tab content */
.main-content > div {
  width: 100%;
  display: block;
}

/* Responsive Design */
@media (max-width: 480px) {
  .main-content {
    max-width: 100%;
    padding: 0 12px;
  }

  .tab-button {
    width: 160px;
    font-size: 11px;
  }

  .message-card {
    margin: 0 -4px;
    padding: 6px 4px;
  }

  .teacher-info {
    max-width: 160px;
  }
}

@media (max-width: 360px) {
  .tab-button {
    width: 140px;
    --padding-start: 16px;
    --padding-end: 16px;
  }

  .teacher-info {
    max-width: 140px;
  }

  .message-content {
    max-width: calc(100% - 120px);
  }
}

/* Dark mode support is handled automatically through CSS variables */

/* Smooth transitions for better UX */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}
