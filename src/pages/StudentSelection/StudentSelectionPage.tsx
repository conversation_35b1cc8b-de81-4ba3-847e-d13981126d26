import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonButton,
  IonCard,
  IonCardContent,
  IonText,
  IonAvatar
} from '@ionic/react';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import { getAllStudents, setSelectedStudent } from '../../data/studentMockData';
import './StudentSelectionPage.css';

/**
 * StudentSelectionPage - Figma-based student selection screen
 * Replaces the intro slides with a functional student selector
 * Based on Figma design node-id=5020-17265
 */

const StudentSelectionPage: React.FC = () => {
  const history = useHistory();
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(null);

  // Get students from mock data
  const students = getAllStudents();

  const handleStudentSelect = (studentId: string) => {
    setSelectedStudentId(studentId);
  };

  const handleContinue = () => {
    if (selectedStudentId) {
      // Store selected student and navigate to main app with welcome parameter
      setSelectedStudent(selectedStudentId);
      history.push(`${ROUTES.HOME}?welcome=true`);
    }
  };

  return (
    <IonPage>
      <IonContent fullscreen className="student-selection-content figma-gradient-bg">
        {/* Figma Ellipses - Exact recreation */}
        <div className="figma-ellipse-1"></div>
        <div className="figma-ellipse-2"></div>
        {/* Logo Section - Following Figma Layout */}
        <div className="flex justify-center pt-16 pb-8">
          <div className="w-44 h-16 flex items-center justify-center">
            <img
              src="/assets/images/logoSumun.png"
              alt="Santillana Sumum"
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>

        {/* Welcome Section - Following Figma spacing */}
        <div className="text-center px-11 pb-12">
          <IonText>
            <h1 className="text-sm font-semibold text-[var(--ion-text-color)] mb-3">Bienvenidos</h1>
          </IonText>
          <IonText>
            <p className="text-xs text-[var(--ion-text-color)] leading-relaxed">
              Para empezar, selecciona una de las opciones de estudiantes
              relacionados para conocer su progreso.
            </p>
          </IonText>
        </div>

        {/* Student Cards - Following Figma compact design */}
        <div className="px-4 space-y-3">
          {students.map((student) => (
            <IonCard
              key={student.id}
              className={`
                cursor-pointer transition-all duration-300 ease-in-out rounded-md
                ${selectedStudentId === student.id
                  ? 'shadow-lg transform -translate-y-0.5 ring-2 ring-primary ring-opacity-50'
                  : 'shadow-sm hover:shadow-md hover:-translate-y-0.5'
                }
              `}
              onClick={() => handleStudentSelect(student.id)}
            >
              <IonCardContent className="p-0">
                <div className="flex items-center h-23">
                  {/* Student Info Section */}
                  <div className="flex items-center gap-2 flex-1 px-4">
                    <IonAvatar className="w-13 h-13 flex-shrink-0">
                      {student.profileImage ? (
                        <img
                          src={student.profileImage}
                          alt={student.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-[var(--ion-color-primary)] to-[var(--ion-color-secondary)] flex items-center justify-center text-white font-semibold text-sm">
                          {student.avatar}
                        </div>
                      )}
                    </IonAvatar>
                    <div className="flex-1 min-w-0">
                      <IonText>
                        <h3 className="text-sm font-semibold text-[var(--ion-text-color)] mb-2 truncate leading-tight">
                          {student.name}
                        </h3>
                      </IonText>
                      <div className="flex items-center gap-2 text-[var(--ion-color-medium)]">
                        <IonText>
                          <span className="text-xs">{student.age} años</span>
                        </IonText>
                        <div className="w-0 h-3 border-l border-[var(--ion-color-primary)]"></div>
                        <IonText>
                          <span className="text-xs">{student.grade}</span>
                        </IonText>
                      </div>
                    </div>
                  </div>
                  {/* Right Section - Following Figma design */}
                  <div className="w-24 h-full bg-[var(--ion-color-light)] rounded-r-md flex items-center justify-center">
                    <img
                      src="/assets/images/logoColegio.png"
                      alt="Logo Colegio"
                      className="w-15 h-8 object-contain"
                    />
                  </div>
                </div>
              </IonCardContent>
            </IonCard>
          ))}
        </div>

        {/* Continue button - only show when student is selected */}
        {selectedStudentId && (
          <div className="px-4 pt-8 pb-4">
            <IonButton
              expand="block"
              className="rounded-md h-12 text-sm font-medium"
              color="primary"
              onClick={handleContinue}
            >
              Continuar
            </IonButton>
          </div>
        )}

        {/* Footer - Following Figma positioning */}
        <div className="text-center pb-8 pt-16">
          <IonText>
            <p className="text-xs text-[var(--ion-color-medium)] mb-3">Family app</p>
          </IonText>
          <IonText>
            <p className="text-xs text-[var(--ion-color-medium)]">v1.0.0</p>
          </IonText>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default StudentSelectionPage;
