/* Student Selection Page - Ionic + Tailwind Implementation */
/* Based on Figma node-id=5020-17265 */

/* Essential Ionic variables */
.student-selection-content {
  --background: var(--ion-background-color);
  position: relative;
  overflow: hidden;
}

/* Figma Ellipses - Optimized with more subtle opacity for better visual balance */
.figma-ellipse-1 {
  position: fixed;
  width: 719.19px;
  height: 719.19px;
  left: -142px;
  top: -13px;
  background: rgba(53, 161, 146, 0.06);
  border-radius: 50%;
  filter: blur(150px);
  z-index: -1000;
  pointer-events: none;
}

.figma-ellipse-2 {
  position: fixed;
  width: 513px;
  height: 513px;
  left: -277px;
  top: 537px;
  background: rgba(73, 194, 235, 0.08);
  border-radius: 50%;
  filter: blur(150px);
  z-index: -999;
  pointer-events: none;
}

/* Compact card height following Figma design */
.h-23 {
  height: 5.75rem; /* 92px - matching Figma card height */
}

.w-13 {
  width: 3.25rem; /* 52px - matching Figma avatar size */
}

.h-13 {
  height: 3.25rem; /* 52px - matching Figma avatar size */
}

.w-15 {
  width: 3.75rem; /* 60px - matching Figma image placeholder */
}

/* Loading skeleton styles - Keep for loading states */
.skeleton-title, .skeleton-subtitle, .skeleton-card {
  background: var(--ion-color-light);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smart scrolling enhancements */
.student-selection-content {
  height: 100vh;
  max-height: 100vh;
}

/* Smooth scrolling for student cards */
.student-cards-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Fade effect for continue button area */
.continue-button-fade {
  background: linear-gradient(to top,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.95) 50%,
    rgba(255, 255, 255, 0.8) 80%,
    rgba(255, 255, 255, 0) 100%);
}

/* Ensure proper spacing for last card when scrolling */
.student-cards-scroll::after {
  content: '';
  display: block;
  height: 1rem;
  flex-shrink: 0;
}

/* Dark mode support is handled automatically through CSS variables */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .skeleton-title,
  .skeleton-subtitle,
  .skeleton-card {
    animation: none;
  }

  .student-cards-scroll {
    scroll-behavior: auto;
  }
}

/* Mobile viewport height fix */
@supports (-webkit-touch-callout: none) {
  .student-selection-content {
    height: -webkit-fill-available;
    max-height: -webkit-fill-available;
  }
}



