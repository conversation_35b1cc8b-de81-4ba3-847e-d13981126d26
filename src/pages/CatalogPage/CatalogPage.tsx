import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText
} from '@ionic/react';
import { libraryOutline } from 'ionicons/icons';
import TabLayout from '../../components/Layout/TabLayout';

const CatalogPage: React.FC = () => {
  return (
    <TabLayout>
      <IonCard className="catalog-card rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Catálogo de recursos">
        <IonCardHeader className="catalog-header text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-[var(--ion-color-primary)] bg-opacity-10 flex items-center justify-center">
              <IonIcon
                icon={libraryOutline}
                className="text-4xl text-[var(--ion-color-primary)]"
              />
            </div>
          </div>
          <IonCardTitle className="text-lg font-semibold text-[var(--ion-text-color)] m-0">
            Catálogo de Recursos
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="catalog-content text-center pt-0">
          <IonText>
            <p className="text-sm text-[var(--ion-color-medium)] leading-relaxed m-0">
              Explora el catálogo completo de recursos educativos y materiales disponibles.
            </p>
          </IonText>
        </IonCardContent>
      </IonCard>
    </TabLayout>
  );
};

export default CatalogPage;
