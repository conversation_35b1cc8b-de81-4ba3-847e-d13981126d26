import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText
} from '@ionic/react';
import { calendarOutline } from 'ionicons/icons';
import TabLayout from '../../components/Layout/TabLayout';

const SchedulePage: React.FC = () => {
  return (
    <TabLayout>
      <IonCard className="schedule-card rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Horarios académicos">
        <IonCardHeader className="schedule-header text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full bg-[var(--ion-color-primary)] bg-opacity-10 flex items-center justify-center">
              <IonIcon
                icon={calendarOutline}
                className="text-4xl text-[var(--ion-color-primary)]"
              />
            </div>
          </div>
          <IonCardTitle className="text-lg font-semibold text-[var(--ion-text-color)] m-0">
            Horarios Académicos
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="schedule-content text-center pt-0">
          <IonText>
            <p className="text-sm text-[var(--ion-color-medium)] leading-relaxed m-0">
              Consulta los horarios de clases y actividades académicas del estudiante.
            </p>
          </IonText>
        </IonCardContent>
      </IonCard>
    </TabLayout>
  );
};

export default SchedulePage;
