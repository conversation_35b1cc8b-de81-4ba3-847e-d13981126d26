import React from 'react';
import TabLayout from '../../components/Layout/TabLayout';
import WelcomeCard from '../../components/WelcomeCard/WelcomeCard';
import AttendanceCard from '../../components/AttendanceCard/AttendanceCard';
import CurricularProgressContainer from '../../components/CurricularProgressContainer/CurricularProgressContainer';
import { UpcomingEvents, WelcomePopover } from '../../components';
import { useUser } from '../../contexts/UserContext';
import { useState, useEffect } from 'react';
import { useHttpLoading } from '../../contexts/LoadingContext';
import { useComponentError } from '../../contexts/ErrorContext';
import { useLocation } from 'react-router-dom';

interface AttendanceData {
  date: string;
  dayName: string;
  dayNumber: number;
  amStatus: 'present' | 'late' | 'justified' | 'unjustified';
  pmStatus: 'present' | 'late' | 'justified' | 'unjustified';
}

interface ProgressData {
  subject: string;
  experience: string;
  sequence: string;
  completedPercentage: number;
  inProgressPercentage: number;
}

const Tab1: React.FC = () => {
  const { user } = useUser();
  const location = useLocation();
  const [attendanceData, setAttendanceData] = useState<AttendanceData[]>([]);
  const [progressData, setProgressData] = useState<ProgressData[]>([]);

  // Welcome popover state
  const [showWelcomePopover, setShowWelcomePopover] = useState(false);

  // HTTP loading monitoring and error handling
  const { isLoading } = useHttpLoading();
  const { error, handleError, clearError } = useComponentError('HomePage-Tab1');

  // Obtener nombre del usuario para mostrar
  const userName = user?.name || 'Usuario';

  // Load static data immediately - no skeleton loading needed for mock data
  // In a real app, this would be replaced with actual HTTP requests using useHttpOperation
  const loadStaticData = () => {
    // Mock data that would normally come from API
    const mockAttendanceData: AttendanceData[] = [
      { date: '2024-01-12', dayName: 'LUN', dayNumber: 12, amStatus: 'present', pmStatus: 'present' },
      { date: '2024-01-13', dayName: 'MAR', dayNumber: 13, amStatus: 'present', pmStatus: 'unjustified' },
      { date: '2024-01-14', dayName: 'MIE', dayNumber: 14, amStatus: 'present', pmStatus: 'present' },
      { date: '2024-01-15', dayName: 'JUE', dayNumber: 15, amStatus: 'justified', pmStatus: 'late' },
      { date: '2024-01-16', dayName: 'VIE', dayNumber: 16, amStatus: 'present', pmStatus: 'present' },
    ];

    const mockProgressData: ProgressData[] = [
      {
        subject: 'Matemáticas',
        experience: 'Números y operaciones',
        sequence: 'Secuencia 1: Suma y resta',
        completedPercentage: 75,
        inProgressPercentage: 25
      },
      {
        subject: 'Comunicación',
        experience: 'Comprensión lectora',
        sequence: 'Secuencia 2: Textos narrativos',
        completedPercentage: 60,
        inProgressPercentage: 40
      },
      {
        subject: 'Ciencias',
        experience: 'Seres vivos',
        sequence: 'Secuencia 1: Plantas y animales',
        completedPercentage: 90,
        inProgressPercentage: 10
      }
    ];

    return { attendanceData: mockAttendanceData, progressData: mockProgressData };
  };

  // Example of how real HTTP requests would work (commented out for demo)
  // const { execute: loadData, isLoading: dataLoading } = useHttpOperation(async () => {
  //   try {
  //     const attendanceResponse = await EnhancedHttpService.get('/api/attendance');
  //     const progressResponse = await EnhancedHttpService.get('/api/progress');
  //     return {
  //       attendanceData: attendanceResponse.data,
  //       progressData: progressResponse.data
  //     };
  //   } catch (error) {
  //     await handleError(error, {
  //       severity: 'medium',
  //       showToast: true,
  //       customMessage: 'No se pudieron cargar los datos del estudiante. Inténtalo de nuevo.'
  //     });
  //     throw error;
  //   }
  // }, 'load-homepage-data');

  useEffect(() => {
    // Load static data immediately - no loading state needed for mock data
    const result = loadStaticData();
    setAttendanceData(result.attendanceData);
    setProgressData(result.progressData);
  }, []);

  // Check if we should show welcome popover (from successful login)
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const showWelcome = urlParams.get('welcome');

    if (showWelcome === 'true' && user) {
      // Small delay to ensure the page is fully loaded
      const timer = setTimeout(() => {
        setShowWelcomePopover(true);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [location.search, user]);

  const handleWelcomePopoverDismiss = () => {
    setShowWelcomePopover(false);

    // Clean up URL parameter
    const url = new URL(window.location.href);
    url.searchParams.delete('welcome');
    window.history.replaceState({}, '', url.toString());
  };

  // No loading state needed for static mock data
  // In a real app with HTTP requests, you would use: const showLoadingState = dataLoading;

  return (
    <TabLayout>
      <WelcomeCard />

      {/* Welcome Popover - shows after successful login */}
      <WelcomePopover
        isOpen={showWelcomePopover}
        userName={userName}
        onDidDismiss={handleWelcomePopoverDismiss}
        autoHideDuration={3000}
      />

      {/* Attendance Card - no loading needed for static data */}
      <AttendanceCard
        data={attendanceData}
        month="Enero 2024"
        period="Últimos 5 días hábiles"
      />

      {/* Curricular Progress - no loading needed for static data */}
      <CurricularProgressContainer
        progressData={progressData}
      />

      {/* Upcoming Events - always show as it has its own loading */}
      <UpcomingEvents />
    </TabLayout>
  );
};

export default Tab1;
