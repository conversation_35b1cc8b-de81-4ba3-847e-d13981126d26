import React from 'react';
import TabLayout from '../../components/Layout/TabLayout';
import ReportsProgressCard from '../../components/ReportsProgressCard/ReportsProgressCard';
import PageHeader from '../../components/PageHeader/PageHeader';

const ReportsPage: React.FC = () => {
  return (
    <TabLayout>
      <PageHeader
        title="Progreso curricular"
        description="Se muestra el momento en que se encuentra el alumno. Junto con los resultados de las evaluaciones estándares de sumun."
      />

          {/* Matemáticas Card */}
          <ReportsProgressCard
            subject="Matemáticas"
            experience="Experiencia N°1: Representar e interpretar números racionales en sus expresiones fracción y decimal para resolver problemas de situaciones aditivas."
            sequenceNumber="Secuencia N°2"
            progressData={{
              completed: [60, 30, 80],
              inProgress: [],
              pending: [0, 0, 0]
            }}
          />

          {/* Ciencias naturales Card */}
          <ReportsProgressCard
            subject="Ciencias naturales"
            experience="Experiencia N°1: Entender qué son el universo y el sistema solar, sus orígenes y componentes."
            sequenceNumber="Secuencia N°2"
            progressData={{
              completed: [60, 30, 80],
              inProgress: [],
              pending: [0, 0, 0]
            }}
          />
    </TabLayout>
  );
};

export default ReportsPage;
