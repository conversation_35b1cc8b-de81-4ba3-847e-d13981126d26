import React, { useState } from 'react';
import {
  IonCard,
  IonCardContent,
  IonText,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonToggle,
  IonImg,
  IonAlert,
} from '@ionic/react';
import {
  checkmarkOutline,
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import { useUser } from '../../contexts/UserContext';
import { useTheme } from '../../contexts/ThemeContext';
import { TabLayout } from '../../components';
import { LoadingService } from '../../services/loading.service';

// Mock data for account page - matches Figma design
const mockUserData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+57 | 315 3059 323',
  role: 'Padre/Madre',
  avatar: '/assets/images/avatar.png',
  memberSince: '2023',
  children: []
};

const AccountPage: React.FC = () => {
  const history = useHistory();
  const { user, logout } = useUser();
  const { isDarkMode, toggleTheme } = useTheme();

  // Use user data from context, fallback to mock data if needed
  const userData = user || mockUserData;

  // Notification settings state
  const [notificationSettings, setNotificationSettings] = useState({
    general: true,
    email: true,
    push: true
  });

  // Logout confirmation dialog state
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
  };

  const handleLogoutConfirm = async () => {
    setShowLogoutConfirm(false);

    // Show professional loading indicator
    const loadingId = LoadingService.showLoading('logout', 'Cerrando sesión...');

    try {
      // Hide loading before logout to ensure it disappears before redirect
      LoadingService.hideLoading(loadingId);

      await logout();
      // The logout function will handle the redirect after a small delay

    } catch (error) {
      console.error('Error during logout:', error);
      // Hide loading on error before fallback redirect
      LoadingService.hideLoading(loadingId);
      // Fallback redirect if OIDC logout fails
      history.replace(ROUTES.AUTH);
    }
  };

  const handleLogoutCancel = () => {
    setShowLogoutConfirm(false);
  };

  const handleNotificationToggle = (type: 'general' | 'email' | 'push') => {
    setNotificationSettings(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const handleTermsAndConditions = () => {
    // TODO: Navigate to terms and conditions
    console.log('Terms and conditions');
  };

  const handlePrivacyNotice = () => {
    // TODO: Navigate to privacy notice
    console.log('Privacy notice');
  };

  const handlePrivacyPolicy = () => {
    // TODO: Navigate to privacy policy
    console.log('Privacy policy');
  };

  return (
    <TabLayout>
      <div className="space-y-6">
        {/* Page Title */}
        <div className="text-center">
          <IonText>
            <h1 className="text-2xl font-semibold text-[var(--ion-text-color)] mb-4">Mi cuenta</h1>
          </IonText>

          {/* Santillana Logo */}
          <div className="flex justify-center items-center mt-4">
            <IonImg
              src="/assets/images/santillana-avatar.svg"
              alt="Santillana Logo"
            />
          </div>
        </div>

        {/* Cards Container */}
        <div className="space-y-6">
          {/* Contact Information Card */}
          <IonCard className="modular-card">
            <IonCardContent className="card-content-unified">
              {/* Header */}
              <div className="mb-4">
                <IonText>
                  <h3 className="card-title-unified">Contacto principal</h3>
                </IonText>
              </div>

              {/* Contact Items List */}
              <IonList className="bg-transparent">
                {/* Phone Number */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">{userData.phone}</p>
                  </IonLabel>
                  <IonIcon
                    icon={checkmarkOutline}
                    slot="end"
                    className="w-4 h-4 text-[var(--ion-color-primary)]"
                  />
                </IonItem>

                {/* Email */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">{userData.email}</p>
                  </IonLabel>
                  <IonIcon
                    icon={checkmarkOutline}
                    slot="end"
                    className="w-4 h-4 text-[var(--ion-color-primary)]"
                  />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Notification Settings Card */}
          <IonCard className="modular-card">
            <IonCardContent className="card-content-unified">
              {/* Header */}
              <div className="mb-4">
                <IonText>
                  <h3 className="card-title-unified">Notificaciones</h3>
                </IonText>
              </div>

              {/* Notification Items List */}
              <IonList className="bg-transparent">
                {/* General Notifications */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Recibir notificaciones</p>
                  </IonLabel>
                  <IonToggle
                    slot="end"
                    checked={notificationSettings.general}
                    onIonChange={() => handleNotificationToggle('general')}
                    color="primary"
                  />
                </IonItem>

                {/* Email Notifications */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Recibir notificaciones</p>
                  </IonLabel>
                  <IonToggle
                    slot="end"
                    checked={notificationSettings.email}
                    onIonChange={() => handleNotificationToggle('email')}
                    color="primary"
                  />
                </IonItem>

                {/* Push Notifications */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Recibir notificaciones</p>
                  </IonLabel>
                  <IonToggle
                    slot="end"
                    checked={notificationSettings.push}
                    onIonChange={() => handleNotificationToggle('push')}
                    color="primary"
                  />
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>

          {/* Settings & Preferences Card */}
          <IonCard className="modular-card">
            <IonCardContent className="card-content-unified">
              {/* Header */}
              <div className="mb-4">
                <IonText>
                  <h3 className="card-title-unified">Configuración</h3>
                </IonText>
              </div>

              {/* Settings Items List */}
              <IonList className="bg-transparent">
                {/* Dark Theme Toggle - Simple and Native */}
                <IonItem lines="none">
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Modo oscuro</p>
                  </IonLabel>
                  <IonToggle
                    slot="end"
                    checked={isDarkMode}
                    onIonChange={toggleTheme}
                    color="primary"
                  />
                </IonItem>

                {/* Terms and Conditions */}
                <IonItem
                  lines="none"
                  button
                  onClick={handleTermsAndConditions}
                >
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Términos y condiciones</p>
                  </IonLabel>
                </IonItem>

                {/* Privacy Notice */}
                <IonItem
                  lines="none"
                  button
                  onClick={handlePrivacyNotice}
                >
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Aviso de privacidad</p>
                  </IonLabel>
                </IonItem>

                {/* Privacy Policy */}
                <IonItem
                  lines="none"
                  button
                  onClick={handlePrivacyPolicy}
                >
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-text-color)]">Políticas de privacidad</p>
                  </IonLabel>
                </IonItem>

                {/* Logout */}
                <IonItem
                  lines="none"
                  detail={false}
                  button
                  onClick={handleLogoutClick}
                >
                  <IonLabel>
                    <p className="text-sm text-[var(--ion-color-danger)]">Cerrar sesión</p>
                  </IonLabel>
                </IonItem>
              </IonList>
            </IonCardContent>
          </IonCard>
        </div>

        {/* Logout Confirmation Dialog */}
        <IonAlert
          isOpen={showLogoutConfirm}
          onDidDismiss={handleLogoutCancel}
          header="Cerrar sesión"
          message="¿Estás seguro de que quieres cerrar sesión?"
          buttons={[
            {
              text: 'Cancelar',
              role: 'cancel',
              handler: handleLogoutCancel
            },
            {
              text: 'Sí',
              role: 'confirm',
              handler: handleLogoutConfirm
            }
          ]}
          cssClass="logout-confirmation-alert"
        />
      </div>
    </TabLayout>
  );
};

export default AccountPage;