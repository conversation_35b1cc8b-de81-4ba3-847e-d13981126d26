import React, { useState } from 'react';
import {
  IonPage,
  IonHeader,
  IonToolbar,
  IonBackButton,
  IonButtons,
  IonContent,
  IonSearchbar,
  IonChip,
  IonText,
  IonIcon,
  IonCard,
  IonCardContent,
} from '@ionic/react';
import {
  chevronForwardOutline,
  timeOutline,
  schoolOutline,
  documentTextOutline,
} from 'ionicons/icons';
import './NotificationsPage.css';

interface Notification {
  id: string;
  type: 'asistencia' | 'tarea' | 'evaluacion';
  category: string;
  subject: string;
  title: string;
  description: string;
  time: string;
  isRead: boolean;
}

const NotificationsPage: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [notifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'asistencia',
      category: 'Informes',
      subject: 'Asistencia',
      title: 'Falla injustificada',
      description: 'A 23 de enero, el estudiante ha registrado una falla sin presentar justificación. Por favor, suba la justificación para solventar la ausencia.',
      time: 'Ahora',
      isRead: false,
    },
    {
      id: '2',
      type: 'tarea',
      category: 'Tareas',
      subject: 'Matemáticas',
      title: 'Desafío Matemático: Álgebra y Geometría',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
      time: 'Hace 1 hora',
      isRead: false,
    },
    {
      id: '3',
      type: 'tarea',
      category: 'Tareas',
      subject: 'Matemáticas',
      title: 'Desafío Matemático: Álgebra y Geometría',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
      time: 'Hace 1 hora',
      isRead: false,
    },
  ]);

  const yesterdayNotifications: Notification[] = [
    {
      id: '4',
      type: 'tarea',
      category: 'Tareas',
      subject: 'Matemáticas',
      title: 'Desafío Matemático: Álgebra y Geometría',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
      time: 'Ayer',
      isRead: true,
    },
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'asistencia':
        return timeOutline;
      case 'tarea':
        return documentTextOutline;
      case 'evaluacion':
        return schoolOutline;
      default:
        return documentTextOutline;
    }
  };

  const markAllAsRead = () => {
    // Implementar lógica para marcar todas como leídas
    console.log('Marcar todas como leídas');
  };

  return (
    <IonPage>
      {/* Header with Back Button */}
      <IonHeader translucent={true} className="notifications-header">
        <IonToolbar className="notifications-toolbar">
          <IonButtons slot="start">
            <IonBackButton text={''}/>
          </IonButtons>
          <IonText className="notifications-title">
            <h1>Central de Notificaciones</h1>
          </IonText>
        </IonToolbar>
      </IonHeader>

      {/* Content */}
      <IonContent fullscreen={true} className="notifications-content">
        {/* Background gradient ellipse */}
        <div className="notifications-gradient-bg"></div>

        <div className="notifications-container">
          {/* Search and Filter Section */}
          <div className="search-filter-section">
            <IonSearchbar
              value={searchText}
              onIonInput={(e) => setSearchText(e.detail.value!)}
              placeholder="Buscador"
              className="notifications-searchbar"
            />
            
            <div className="filter-actions">
              <div className="filter-chip-container">
                <IonChip className="filter-chip-all">
                  <IonText>Todos</IonText>
                  <IonIcon icon={chevronForwardOutline} />
                </IonChip>
              </div>
              <IonText className="mark-all-read" onClick={markAllAsRead}>
                Marcar todas como leidas
              </IonText>
            </div>
          </div>

          {/* Today's Notifications */}
          <div className="notifications-section">
            <div className="section-header">
              <IonText className="section-title">
                <h2>Hoy</h2>
              </IonText>
            </div>

            <div className="notifications-list">
              {notifications.map((notification, index) => (
                <div key={notification.id}>
                  <div className="notification-card">
                    <div className="notification-icon-container">
                      <div className="notification-icon">
                        <IonIcon icon={getNotificationIcon(notification.type)} />
                      </div>
                    </div>
                    
                    <div className="notification-content">
                      <div className="notification-header">
                        <div className="notification-meta">
                          <IonText className="notification-category">{notification.category}</IonText>
                          <span className="meta-separator">•</span>
                          <IonText className="notification-subject">{notification.subject}</IonText>
                        </div>
                        <IonText className="notification-title">
                          <h3>{notification.title}</h3>
                        </IonText>
                      </div>
                      
                      <div className="notification-description">
                        <IonText>{notification.description}</IonText>
                      </div>
                      
                      <div className="notification-time">
                        <IonIcon icon={timeOutline} className="time-icon" />
                        <IonText className="time-text">{notification.time}</IonText>
                      </div>
                    </div>
                  </div>
                  
                  {/* Separator line (except for last item) */}
                  {index < notifications.length - 1 && (
                    <div className="notification-separator"></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Yesterday's Notifications */}
          <div className="notifications-section">
            <div className="section-header">
              <IonText className="section-title">
                <h2>Ayer</h2>
              </IonText>
            </div>

            <div className="notifications-list">
              {yesterdayNotifications.map((notification) => (
                <div key={notification.id}>
                  <div className="notification-card">
                    <div className="notification-icon-container">
                      <div className="notification-icon">
                        <IonIcon icon={getNotificationIcon(notification.type)} />
                      </div>
                    </div>
                    
                    <div className="notification-content">
                      <div className="notification-header">
                        <div className="notification-meta">
                          <IonText className="notification-category">{notification.category}</IonText>
                          <span className="meta-separator">•</span>
                          <IonText className="notification-subject">{notification.subject}</IonText>
                        </div>
                        <IonText className="notification-title">
                          <h3>{notification.title}</h3>
                        </IonText>
                      </div>
                      
                      <div className="notification-description">
                        <IonText>{notification.description}</IonText>
                      </div>
                      
                      <div className="notification-time">
                        <IonIcon icon={timeOutline} className="time-icon" />
                        <IonText className="time-text">{notification.time}</IonText>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default NotificationsPage;
