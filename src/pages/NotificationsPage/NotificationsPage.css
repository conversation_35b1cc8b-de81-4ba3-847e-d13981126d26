/* Notifications Page Styles */
.notifications-header {
  --background: rgba(249, 249, 249, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.notifications-toolbar {
  --background: transparent;
  --border-color: transparent;
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 60px;
}

.notifications-title h1 {
  font-size: 18px;
  font-weight: 700;
  color: var(--ion-text-color);
  margin: 0;
  text-align: center;
  flex: 1;
}

.notifications-content {
  --background: var(--ion-color-background);
  position: relative;
}

/* Background gradient */
.notifications-gradient-bg {
  position: absolute;
  top: -414px;
  left: -154px;
  width: 719px;
  height: 719px;
  background: rgba(53, 161, 146, 0.1);
  border-radius: 50%;
  filter: blur(191px);
  z-index: 0;
}

.notifications-container {
  position: relative;
  z-index: 1;
  padding: 16px;
}

/* Search and Filter Section */
.search-filter-section {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notifications-searchbar {
  --background: var(--ion-color-background);
  --border-radius: 24px;
  --box-shadow: none;
  --border: 1px solid #D9D9D9;
  --placeholder-color: #A4A4A4;
  --color: var(--ion-text-color);
  height: 32px;
  font-size: 12px;
}

.filter-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 88px;
}

.filter-chip-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-chip-all {
  --background: #45B2FF;
  --color: white;
  border-radius: 24px;
  height: auto;
  padding: 4px 12px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-chip-all ion-text {
  color: white;
  font-size: 10px;
  font-weight: 400;
  width: 46px;
}

.filter-chip-all ion-icon {
  color: white;
  font-size: 16px;
}

.mark-all-read {
  font-size: 10px;
  color: #35A192;
  cursor: pointer;
  text-align: right;
}

/* Notifications Sections */
.notifications-section {
  margin-bottom: 48px;
}

.section-header {
  margin-bottom: 8px;
}

.section-title h2 {
  font-size: 12px;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Notification Card */
.notification-card {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.notification-icon-container {
  flex-shrink: 0;
}

.notification-icon {
  width: 38px;
  height: 38px;
  background: #35A192;
  border-radius: 19.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px;
}

.notification-icon ion-icon {
  color: white;
  font-size: 24px;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-category,
.notification-subject {
  font-size: 10px;
  color: var(--ion-text-color);
}

.meta-separator {
  color: #35A192;
  font-size: 10px;
  font-weight: bold;
}

.notification-title h3 {
  font-size: 12px;
  font-weight: 700;
  color: var(--ion-text-color);
  margin: 0;
}

.notification-description {
  margin-top: 4px;
}

.notification-description ion-text {
  font-size: 10px;
  color: var(--ion-text-color);
  line-height: 1.4;
  display: block;
}

.notification-time {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.time-icon {
  color: #35A192;
  font-size: 16px;
}

.time-text {
  font-size: 10px;
  color: #C0C0C0;
}

.notification-separator {
  height: 0.5px;
  background: #D9D9D9;
  margin: 8px 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .notifications-header {
    --background: rgba(0, 0, 0, 0.5);
  }

  .notifications-title h1,
  .section-title h2,
  .notification-title h3,
  .notification-category,
  .notification-subject,
  .notification-description ion-text {
    color: var(--ion-text-color);
  }

  .notifications-searchbar {
    --background: var(--ion-color-background);
    --border: 1px solid var(--ion-color-medium);
    --placeholder-color: var(--ion-color-medium);
  }

  .notification-separator {
    background: var(--ion-color-medium);
  }
}


/* Animation for notification cards */
.notification-card {
  transition: transform 0.2s ease-in-out;
}

.notification-card:active {
  transform: scale(0.98);
}
