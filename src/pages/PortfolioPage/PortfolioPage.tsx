import React, { useState } from 'react';
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText
} from '@ionic/react';
import { informationCircleOutline } from 'ionicons/icons';
import TabLayout from '../../components/Layout/TabLayout';
import PageHeader from '../../components/PageHeader/PageHeader';
import PortfolioSearchBar from '../../components/Portfolio/SearchBar/PortfolioSearchBar';
import PortfolioFilterSlider from '../../components/Portfolio/FilterSlider/PortfolioFilterSlider';
import ResourceCard from '../../components/Portfolio/ResourceCard/ResourceCard';

// Import CSS files
import '../../components/Portfolio/SearchBar/PortfolioSearchBar.css';
import '../../components/Portfolio/FilterSlider/PortfolioFilterSlider.css';
import '../../components/Portfolio/ResourceCard/ResourceCard.css';
import './PortfolioPage.css';

// Mock data for demonstration
const mockFilters = [
  {
    id: 'productos-sumun',
    label: 'Productos Sumun',
    icon: 'sumun-icon',
    isSelected: true
  },
  {
    id: 'apoyo-socioemocional',
    label: 'Apoyo socioemocional',
    icon: 'support-icon',
    isSelected: false
  },
  {
    id: 'recursos-adicionales',
    label: 'Recursos adicionales',
    icon: 'resources-icon',
    isSelected: false
  }
];

const mockResources = [
  {
    id: '1',
    title: 'Masterclass: El programa',
    description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
    imageUrl: '/assets/images/banner1.png',
    metadata: {
      date: '12 de enero',
      category: 'Tareas',
      subject: 'Matemáticas'
    }
  },
  {
    id: '2',
    title: 'Microhabilidades : Qué significan y por qué son importantes',
    description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
    imageUrl: '/assets/images/banner2.png',
    metadata: {
      date: '12 de enero',
      category: 'Tareas',
      subject: 'Matemáticas'
    }
  },
  {
    id: '3',
    title: 'Masterclass: El programa',
    description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
    imageUrl: '/assets/images/banner3.png',
    metadata: {
      date: '12 de enero',
      category: 'Tareas',
      subject: 'Matemáticas'
    }
  }
];

const PortfolioPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedFilters, setSelectedFilters] = useState<string[]>(['productos-sumun']);
  const [filteredResources, setFilteredResources] = useState(mockResources);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    filterResources(term, selectedFilters);
  };

  const handleFilterChange = (filters: string[]) => {
    setSelectedFilters(filters);
    filterResources(searchTerm, filters);
  };

  const filterResources = (term: string, filters: string[]) => {
    let filtered = mockResources;

    // Filter by search term
    if (term.trim()) {
      filtered = filtered.filter(resource =>
        resource.title.toLowerCase().includes(term.toLowerCase()) ||
        resource.description.toLowerCase().includes(term.toLowerCase())
      );
    }

    // Apply additional filtering logic based on selected filters if needed
    // For now, we show all resources regardless of filters

    setFilteredResources(filtered);
  };

  const handleResourceClick = (resourceId: string) => {
    console.log('Resource clicked:', resourceId);
    // Handle resource navigation here
  };

  return (
    <TabLayout>
      {/* Page Header */}
      <PageHeader title="Recursos" />

      {/* Search Bar */}
        <PortfolioSearchBar
          onSearch={handleSearch}
          placeholder="Buscador de recursos"
        />

      {/* Filter Slider */}
        <PortfolioFilterSlider
          filters={mockFilters}
          onFilterChange={handleFilterChange}
        />

      {/* Products Section */}
      <IonCard className="rounded-2xl shadow-sm">
        <IonCardHeader className="products-header pb-2">
          <div className="flex items-center justify-between">
            <IonCardTitle className="products-title text-xl font-bold text-[var(--ion-text-color)] m-0">
              Productos de la plataforma
            </IonCardTitle>
            <IonIcon
              icon={informationCircleOutline}
              className="text-xl text-[var(--ion-color-primary)]"
            />
          </div>
        </IonCardHeader>

        <IonCardContent className="products-content p-4 pt-2">
          {filteredResources.length > 0 ? (
            <div className="resources-grid">
              {filteredResources.map((resource) => (
                <ResourceCard
                  key={resource.id}
                  id={resource.id}
                  title={resource.title}
                  description={resource.description}
                  imageUrl={resource.imageUrl}
                  metadata={resource.metadata}
                  onCardClick={handleResourceClick}
                />
              ))}
            </div>
          ) : (
            <div className="no-resources-message text-center py-8">
              <IonText>
                <p className="text-sm text-[var(--ion-color-medium)] leading-relaxed m-0">
                  No se encontraron recursos que coincidan con tu búsqueda.
                </p>
              </IonText>
            </div>
          )}
        </IonCardContent>
      </IonCard>
    </TabLayout>
  );
};

export default PortfolioPage;
