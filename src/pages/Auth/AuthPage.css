/* Professional Auth Page Styles - Optimized */

/* Essential Ionic variables */
.auth-content {
  --background: var(--ion-background-color);
}

/* Complex gradient background that cannot be replicated with standard Tailwind */
.auth-background-image {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
}

.auth-background-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

/* Complex backdrop effects that cannot be replicated with standard Tailwind */
.auth-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Dark mode support is handled automatically through CSS variables */

.auth-card-content {
  background: transparent;
  color: var(--ion-text-color);
}

.auth-header {
  background: transparent;
}

/* Complex gradient that cannot be replicated with standard Tailwind */
.auth-icon-container {
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
}

.auth-title {
  color: var(--ion-text-color);
}

.auth-subtitle {
  color: var(--ion-color-medium);
}

/* Complex gradient button that cannot be replicated with standard Tailwind */
.auth-submit-button {
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  --background-hover: linear-gradient(135deg, var(--ion-color-primary-shade), var(--ion-color-secondary-shade));
  --color: white;
  --border-radius: 16px;
  --box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  text-transform: none;
}

.auth-submit-button:hover {
  --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
  transform: translateY(-2px);
}

.auth-submit-button:disabled {
  --background: var(--ion-color-medium);
  --color: var(--ion-color-medium-contrast);
  --box-shadow: none;
  transform: none;
}

/* Development bypass button styling */
.dev-bypass-button {
  --border-color: var(--ion-color-warning);
  --color: var(--ion-color-warning);
  --border-radius: 12px;
  --border-width: 2px;
  --border-style: dashed;
  --background: transparent;
  text-transform: none;
  font-weight: 500;
}

.dev-bypass-button:hover {
  --background: rgba(var(--ion-color-warning-rgb), 0.1);
  --border-style: solid;
}

.dev-bypass-button:disabled {
  --color: var(--ion-color-medium);
  --border-color: var(--ion-color-medium);
  --background: transparent;
}

/* Error Display */
.auth-error {
  margin-top: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(var(--ion-color-danger-rgb), 0.1);
  border: 1px solid rgba(var(--ion-color-danger-rgb), 0.2);
}

.auth-error p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Footer Text */
.auth-footer {
  margin-top: 1.5rem;
  text-align: center;
}

.auth-footer-text {
  color: var(--ion-color-medium);
  margin: 0;
}

.auth-footer-text p {
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}





/* Alert Styles */
.auth-alert.alert-success {
  --color: var(--ion-color-success);
}

.auth-alert.alert-error {
  --color: var(--ion-color-danger);
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-content {
    padding: 0;
  }

  .auth-container {
    padding: 1rem;
  }

  .auth-card {
    max-width: 100%;
    border-radius: 16px;
  }

  .auth-header {
    padding: 2rem 1.5rem 1.5rem 1.5rem;
  }

  .auth-card-content {
    padding: 1.5rem;
  }

  .auth-icon-container {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem auto;
  }

  .auth-main-icon {
    font-size: 1.5rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 360px) {
  .auth-container {
    padding: 0.5rem;
  }

  .auth-card {
    border-radius: 12px;
  }

  .auth-header {
    padding: 1.5rem 1rem 1rem 1rem;
  }

  .auth-card-content {
    padding: 1rem;
  }
}

/* Smooth Transitions for Theme Changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Focus Styles for Accessibility */
.auth-submit-button:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .auth-card {
    border: 1px solid var(--ion-color-medium);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }

  .auth-submit-button:hover {
    transform: none;
  }
}

