import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonText,
  IonIcon,
  IonSpinner,
  IonAlert,
} from '@ionic/react';
import {
  logInOutline,
  shieldCheckmarkOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from '../../services/capacitor-auth.service';
import { useUser } from '../../contexts/UserContext';

import { userManager } from '../../config/user-manager.config';
import './AuthPage.css';

const AuthPage: React.FC = () => {
  const history = useHistory();
  const { updateCapacitorAuthState } = useUser();

  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error'>('error');



  const handleLogin = async () => {
    setIsLoading(true);
    setShowAlert(false);

    try {
      if (Capacitor.isNativePlatform()) {
        const authResult = await CapacitorAuthService.signIn();

        // Update UserContext with authentication result
        if (updateCapacitorAuthState) {
          await updateCapacitorAuthState(authResult);
        }

        setIsLoading(false);
        history.replace(ROUTES.STUDENT_SELECTION);
      } else {
        // Flujo web con react-oidc-context
        // Check if there was a recent logout to force fresh authentication
        const logoutTimestamp = localStorage.getItem('logout_timestamp');
        const extraParams: any = {};

        if (logoutTimestamp) {
          // Force fresh authentication by adding prompt=login
          extraParams.prompt = 'login';
          // Clear the logout timestamp
          localStorage.removeItem('logout_timestamp');
        }

        await userManager.signinRedirect(extraParams);
        // No necesitamos setIsLoading(false) aquí porque la página se redirige
      }
    } catch (error) {
      console.error('❌ [UI] Error during authentication:', error);
      setIsLoading(false);

      // Handle different types of errors
      const errorMessage = error instanceof Error ? error.message : 'Error de autenticación';

      if (errorMessage.includes('cancelled by user')) {
        // User cancelled authentication - show a gentle message
        setAlertType('error');
        setAlertMessage('Autenticación cancelada. Puedes intentar de nuevo cuando quieras.');
        setShowAlert(true);
      } else if (errorMessage.includes('timeout')) {
        // Authentication timeout
        setAlertType('error');
        setAlertMessage('La autenticación tardó demasiado. Por favor, inténtalo de nuevo.');
        setShowAlert(true);
      } else {
        // Other authentication errors
        setAlertType('error');
        setAlertMessage(`Error de autenticación: ${errorMessage}`);
        setShowAlert(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <IonPage>
      <IonContent
        className="auth-content h-screen max-h-screen overflow-hidden flex flex-col items-center justify-center p-0 relative"
        scrollY={false}
        scrollX={false}
        forceOverscroll={false}
      >
        {/* Background Image */}
        <div className="auth-background-image absolute inset-0 z-0"></div>

        <div className="auth-container w-full h-screen max-h-screen m-0 flex items-center justify-center relative z-10 p-8 box-border">
          <IonCard className="auth-card rounded-3xl shadow-2xl overflow-hidden transition-all duration-300 ease-in-out w-full max-h-[90vh] flex flex-col justify-center max-w-[450px] mx-auto border border-white/20">
            <IonCardHeader className="auth-header text-center py-12 px-12 pb-8">
              <div className="auth-icon-container flex items-center justify-center w-20 h-20 rounded-full mx-auto mb-6 shadow-lg">
                <IonIcon
                  icon={shieldCheckmarkOutline}
                  className="auth-main-icon text-3xl text-white"
                />
              </div>
              <IonCardTitle className="auth-title text-3xl font-bold m-0 mb-2 leading-tight">
                Sumun Office
              </IonCardTitle>
              <IonText className="auth-subtitle text-[15px] m-0 leading-relaxed">
                <p>Accede de forma segura con tu cuenta de Santillana Connect</p>
              </IonText>
            </IonCardHeader>

            <IonCardContent className="auth-card-content p-8 flex-1 flex flex-col justify-center">
              <IonButton
                expand="block"
                size="large"
                onClick={handleLogin}
                disabled={isLoading}
                className="auth-submit-button text-base font-semibold mb-6 transition-all duration-300 ease-in-out text-white h-14"
              >
                {isLoading ? (
                  <>
                    <IonSpinner name="crescent" className="loading-spinner mr-2 w-4 h-4" />
                    Conectando...
                  </>
                ) : (
                  <>
                    <IonIcon icon={logInOutline} slot="start" />
                    Iniciar sesión
                  </>
                )}
              </IonButton>



              {showAlert && alertType === 'error' && (
                <div className="auth-error mt-4 p-3 rounded-lg">
                  <IonText color="danger">
                    <p className="m-0 text-sm leading-relaxed">{alertMessage}</p>
                  </IonText>
                </div>
              )}

              <div className="auth-footer mt-6 text-center">
                <IonText className="auth-footer-text m-0">
                  <p>
                    Al iniciar sesión, serás redirigido de forma segura a
                    Santillana Connect para autenticarte.
                  </p>
                </IonText>
              </div>


            </IonCardContent>
          </IonCard>
        </div>

        {/* Alert for success/error messages */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={alertType === 'success' ? 'Éxito' : 'Error'}
          message={alertMessage}
          buttons={['OK']}
          cssClass={`auth-alert ${alertType === 'success' ? 'alert-success' : 'alert-error'}`}
        />
      </IonContent>
    </IonPage>
  );
};

export default AuthPage; 