/* ProgressIndicator - Modern Progress Styles */

/* Linear Progress */
.progress-linear {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.progress-label-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ion-text-color);
}

.progress-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-medium);
}

.progress-track {
  position: relative;
  width: 100%;
  height: 4px;
  background: var(--ion-color-light);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Size variations for linear */
.progress-linear.progress-small .progress-track {
  height: 2px;
}

.progress-linear.progress-small .progress-label {
  font-size: 0.75rem;
}

.progress-linear.progress-small .progress-percentage {
  font-size: 0.6875rem;
}

.progress-linear.progress-large .progress-track {
  height: 6px;
  border-radius: 3px;
}

.progress-linear.progress-large .progress-label {
  font-size: 1rem;
}

.progress-linear.progress-large .progress-percentage {
  font-size: 0.875rem;
}

/* Circular Progress */
.progress-circular {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.progress-circular-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circular-svg {
  width: 48px;
  height: 48px;
  transform: rotate(-90deg);
}

.progress-circular-bg {
  stroke: var(--ion-color-light);
  opacity: 0.3;
}

.progress-circular-progress {
  stroke: var(--ion-color-primary);
  transition: stroke-dashoffset 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progress-circular-progress.indeterminate {
  animation: progress-circular-spin 1.5s linear infinite;
}

.progress-circular-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-circular-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-primary);
}

.progress-circular-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ion-text-color);
  text-align: center;
}

/* Size variations for circular */
.progress-circular.progress-small .progress-circular-svg {
  width: 32px;
  height: 32px;
}

.progress-circular.progress-small .progress-circular-percentage {
  font-size: 0.625rem;
}

.progress-circular.progress-small .progress-circular-label {
  font-size: 0.75rem;
}

.progress-circular.progress-large .progress-circular-svg {
  width: 64px;
  height: 64px;
}

.progress-circular.progress-large .progress-circular-percentage {
  font-size: 0.875rem;
}

.progress-circular.progress-large .progress-circular-label {
  font-size: 1rem;
}

/* Minimal Progress */
.progress-minimal {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-minimal-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--ion-text-color);
  white-space: nowrap;
}

.progress-minimal-track {
  flex: 1;
  height: 2px;
  background: var(--ion-color-light);
  border-radius: 1px;
  overflow: hidden;
  position: relative;
}

.progress-minimal-fill {
  height: 100%;
  background: var(--ion-color-primary);
  border-radius: 1px;
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.progress-minimal-fill.indeterminate {
  width: 30% !important;
  animation: progress-minimal-slide 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.progress-minimal-percentage {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--ion-color-medium);
  white-space: nowrap;
  min-width: 2.5rem;
  text-align: right;
}

/* Size variations for minimal */
.progress-minimal.progress-small .progress-minimal-track {
  height: 1px;
}

.progress-minimal.progress-small .progress-minimal-label {
  font-size: 0.75rem;
}

.progress-minimal.progress-small .progress-minimal-percentage {
  font-size: 0.6875rem;
}

.progress-minimal.progress-large .progress-minimal-track {
  height: 3px;
  border-radius: 1.5px;
}

.progress-minimal.progress-large .progress-minimal-label {
  font-size: 1rem;
}

.progress-minimal.progress-large .progress-minimal-percentage {
  font-size: 0.875rem;
}

/* Color variations */
.progress-secondary .progress-bar,
.progress-secondary .progress-circular-progress,
.progress-secondary .progress-minimal-fill {
  --color: var(--ion-color-secondary);
}

.progress-secondary .progress-circular-percentage {
  color: var(--ion-color-secondary);
}

.progress-tertiary .progress-bar,
.progress-tertiary .progress-circular-progress,
.progress-tertiary .progress-minimal-fill {
  --color: var(--ion-color-tertiary);
}

.progress-tertiary .progress-circular-percentage {
  color: var(--ion-color-tertiary);
}

.progress-success .progress-bar,
.progress-success .progress-circular-progress,
.progress-success .progress-minimal-fill {
  --color: var(--ion-color-success);
}

.progress-success .progress-circular-percentage {
  color: var(--ion-color-success);
}

.progress-warning .progress-bar,
.progress-warning .progress-circular-progress,
.progress-warning .progress-minimal-fill {
  --color: var(--ion-color-warning);
}

.progress-warning .progress-circular-percentage {
  color: var(--ion-color-warning);
}

.progress-danger .progress-bar,
.progress-danger .progress-circular-progress,
.progress-danger .progress-minimal-fill {
  --color: var(--ion-color-danger);
}

.progress-danger .progress-circular-percentage {
  color: var(--ion-color-danger);
}

/* Animations */
@keyframes progress-circular-spin {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124;
  }
}

@keyframes progress-minimal-slide {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* Dark mode adjustments */
.ion-palette-dark .progress-track {
  background: var(--ion-color-dark);
}

.ion-palette-dark .progress-minimal-track {
  background: var(--ion-color-dark);
}

.ion-palette-dark .progress-circular-bg {
  stroke: var(--ion-color-dark);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .progress-bar,
  .progress-circular-progress,
  .progress-minimal-fill {
    transition: none;
  }
  
  .progress-circular-progress.indeterminate {
    animation: none;
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35;
  }
  
  .progress-minimal-fill.indeterminate {
    animation: none;
    width: 50% !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .progress-track,
  .progress-minimal-track {
    background: var(--ion-text-color);
    opacity: 0.3;
  }
  
  .progress-circular-bg {
    stroke: var(--ion-text-color);
    opacity: 0.3;
  }
  
  .progress-bar,
  .progress-circular-progress,
  .progress-minimal-fill {
    --color: var(--ion-text-color);
  }
}

/* Performance optimizations */
.progress-bar,
.progress-circular-progress,
.progress-minimal-fill {
  will-change: transform, width, stroke-dashoffset;
}
