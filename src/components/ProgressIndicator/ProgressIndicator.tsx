/**
 * ProgressIndicator - Modern Progress Component
 * Subtle progress indicators with smooth animations and accessibility
 */

import React from 'react';
import { IonProgressBar, IonText } from '@ionic/react';
import './ProgressIndicator.css';

interface ProgressIndicatorProps {
  // Progress props
  value?: number; // 0-100
  buffer?: number; // 0-100
  indeterminate?: boolean;
  
  // Display props
  showPercentage?: boolean;
  showLabel?: boolean;
  label?: string;
  size?: 'small' | 'medium' | 'large';
  variant?: 'linear' | 'circular' | 'minimal';
  
  // Styling
  color?: 'primary' | 'secondary' | 'tertiary' | 'success' | 'warning' | 'danger';
  className?: string;
  
  // Accessibility
  ariaLabel?: string;
  ariaValueText?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  value = 0,
  buffer,
  indeterminate = false,
  showPercentage = false,
  showLabel = false,
  label,
  size = 'medium',
  variant = 'linear',
  color = 'primary',
  className = '',
  ariaLabel,
  ariaValueText
}) => {
  const normalizedValue = Math.max(0, Math.min(100, value));
  const normalizedBuffer = buffer ? Math.max(0, Math.min(100, buffer)) : undefined;

  const renderLinearProgress = () => (
    <div className={`progress-linear progress-${size} progress-${color} ${className}`}>
      {showLabel && label && (
        <div className="progress-label-container">
          <IonText className="progress-label">{label}</IonText>
          {showPercentage && !indeterminate && (
            <IonText className="progress-percentage">{Math.round(normalizedValue)}%</IonText>
          )}
        </div>
      )}
      
      <div className="progress-track">
        <IonProgressBar
          value={indeterminate ? undefined : normalizedValue / 100}
          buffer={normalizedBuffer ? normalizedBuffer / 100 : undefined}
          color={color}
          className="progress-bar"
          aria-label={ariaLabel || label}
          aria-valuenow={indeterminate ? undefined : normalizedValue}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-valuetext={ariaValueText || (indeterminate ? 'Loading...' : `${Math.round(normalizedValue)}%`)}
        />
      </div>
    </div>
  );

  const renderCircularProgress = () => {
    const radius = size === 'small' ? 16 : size === 'large' ? 24 : 20;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = circumference;
    const strokeDashoffset = indeterminate ? 0 : circumference - (normalizedValue / 100) * circumference;

    return (
      <div className={`progress-circular progress-${size} progress-${color} ${className}`}>
        <div className="progress-circular-container">
          <svg 
            className="progress-circular-svg" 
            viewBox={`0 0 ${(radius + 4) * 2} ${(radius + 4) * 2}`}
            aria-label={ariaLabel || label}
            role="progressbar"
            aria-valuenow={indeterminate ? undefined : normalizedValue}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-valuetext={ariaValueText || (indeterminate ? 'Loading...' : `${Math.round(normalizedValue)}%`)}
          >
            {/* Background circle */}
            <circle
              className="progress-circular-bg"
              cx={radius + 4}
              cy={radius + 4}
              r={radius}
              fill="none"
              strokeWidth="2"
            />
            
            {/* Progress circle */}
            <circle
              className={`progress-circular-progress ${indeterminate ? 'indeterminate' : ''}`}
              cx={radius + 4}
              cy={radius + 4}
              r={radius}
              fill="none"
              strokeWidth="2"
              strokeLinecap="round"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              transform={`rotate(-90 ${radius + 4} ${radius + 4})`}
            />
          </svg>
          
          {/* Center content */}
          {showPercentage && !indeterminate && (
            <div className="progress-circular-center">
              <IonText className="progress-circular-percentage">
                {Math.round(normalizedValue)}%
              </IonText>
            </div>
          )}
        </div>
        
        {showLabel && label && (
          <IonText className="progress-circular-label">{label}</IonText>
        )}
      </div>
    );
  };

  const renderMinimalProgress = () => (
    <div className={`progress-minimal progress-${size} progress-${color} ${className}`}>
      {showLabel && label && (
        <IonText className="progress-minimal-label">{label}</IonText>
      )}
      
      <div className="progress-minimal-track">
        <div 
          className={`progress-minimal-fill ${indeterminate ? 'indeterminate' : ''}`}
          style={{ 
            width: indeterminate ? '100%' : `${normalizedValue}%` 
          }}
          aria-label={ariaLabel || label}
          role="progressbar"
          aria-valuenow={indeterminate ? undefined : normalizedValue}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-valuetext={ariaValueText || (indeterminate ? 'Loading...' : `${Math.round(normalizedValue)}%`)}
        />
      </div>
      
      {showPercentage && !indeterminate && (
        <IonText className="progress-minimal-percentage">
          {Math.round(normalizedValue)}%
        </IonText>
      )}
    </div>
  );

  switch (variant) {
    case 'circular':
      return renderCircularProgress();
    case 'minimal':
      return renderMinimalProgress();
    case 'linear':
    default:
      return renderLinearProgress();
  }
};

export default ProgressIndicator;
