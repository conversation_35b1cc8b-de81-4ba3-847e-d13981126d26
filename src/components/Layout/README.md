# TabLayout Component - Optimized Architecture

## Overview

The TabLayout component provides a reusable, optimized layout wrapper for all tab pages in the Ionic React application. It features full-screen display optimization, header scroll-behind behavior, and modular configuration options.

## Key Features

### 🚀 Full-Screen Display Optimization
- Maximizes viewport utilization by removing unnecessary margins and padding
- Proper safe area handling for iOS and Android platforms
- Responsive design that adapts to different screen sizes

### 📱 Header Scroll-Behind Behavior
- Fixed header positioning with translucent blur effect
- Content scrolls behind the header for immersive experience
- Maintains header visibility and functionality during scroll

### 🎨 Subtle Gradient Background
- Optimized gradient ellipses with reduced opacity (0.06/0.08)
- Conditionally rendered based on `showGradient` prop
- Consistent with Figma design specifications

### 🔧 Reusable Architecture
- Modular component with TypeScript interfaces
- Pre-configured variants for common use cases
- Easy integration without code duplication

## Usage

### Basic Usage

```tsx
import TabLayout from '../../components/Layout/TabLayout';

const MyTabPage: React.FC = () => {
  return (
    <TabLayout>
      <div>Your content here</div>
    </TabLayout>
  );
};
```

### Advanced Configuration

```tsx
import TabLayout from '../../components/Layout/TabLayout';

const MyTabPage: React.FC = () => {
  return (
    <TabLayout
      className="custom-tab-page"
      showGradient={false}
      includePadding={false}
      customPadding="2rem"
    >
      <div>Your content here</div>
    </TabLayout>
  );
};
```

### Using Pre-configured Variants

```tsx
import { TabLayoutVariants } from '../../components/Layout/TabLayout';

const MyTabPage: React.FC = () => {
  return TabLayoutVariants.fullWidth(
    <div>Full-width content without horizontal padding</div>,
    'custom-class'
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `React.ReactNode` | - | Content to be rendered within the tab layout |
| `className` | `string` | `''` | Additional CSS classes to apply to the root IonPage |
| `showGradient` | `boolean` | `true` | Whether to show the gradient background ellipses |
| `includePadding` | `boolean` | `true` | Whether to include horizontal padding for content |
| `customPadding` | `string` | `undefined` | Custom padding value to override default horizontal padding |

## Variants

### Standard
Default configuration with gradient and padding.

### Full Width
Layout without horizontal padding for edge-to-edge content.

### Clean
Layout without gradient background for minimal appearance.

### Minimal
Layout without gradient or padding for maximum customization.

## CSS Classes

### Layout Classes
- `.tab-layout-page` - Root page container
- `.tab-layout-content` - Content container
- `.tab-content-wrapper` - Content wrapper with proper spacing

### Utility Classes
- `.tab-layout-spacing-sm` - Small vertical spacing (space-y-4)
- `.tab-layout-spacing-md` - Medium vertical spacing (space-y-6)
- `.tab-layout-spacing-lg` - Large vertical spacing (space-y-8)
- `.tab-layout-card` - Card styling for content sections
- `.tab-layout-section` - Section spacing

### Modifier Classes
- `.no-horizontal-padding` - Removes horizontal padding
- `.custom-padding` - Enables custom padding via inline styles

## Architecture Benefits

1. **Consistency**: Ensures uniform layout across all tab pages
2. **Performance**: Optimized for full-screen display and smooth scrolling
3. **Maintainability**: Centralized layout logic with clear interfaces
4. **Flexibility**: Multiple configuration options without code duplication
5. **Accessibility**: Proper safe area handling and responsive design

## Integration with Existing Components

The optimized TabLayout maintains compatibility with:
- ✅ Floating TabBar with rounded borders
- ✅ Fixed header with translucent effect
- ✅ 6-tab structure (Inicio, Informes, Conexion, Portfolio, Horarios, Catalogo)
- ✅ Figma design specifications
- ✅ iOS and Android platform consistency

## Migration Guide

### From Old TabLayout

```tsx
// Before
<TabLayout className="my-class">
  <div className="ion-padding">Content</div>
</TabLayout>

// After
<TabLayout className="my-class">
  <div>Content</div> {/* Padding handled automatically */}
</TabLayout>
```

### Custom Padding Requirements

```tsx
// For edge-to-edge content
<TabLayout includePadding={false}>
  <div className="full-width-content">Content</div>
</TabLayout>

// For custom padding
<TabLayout customPadding="1.5rem">
  <div>Content with custom padding</div>
</TabLayout>
```

## Best Practices

1. Use the standard variant for most tab pages
2. Use fullWidth variant for image galleries or maps
3. Use clean variant for pages with custom backgrounds
4. Use minimal variant when implementing custom layout logic
5. Apply utility classes for consistent spacing
6. Test on both iOS and Android devices
7. Verify safe area handling on devices with notches

## Performance Considerations

- Gradient ellipses use CSS transforms and are GPU-accelerated
- Fixed header uses backdrop-filter for optimal performance
- Content wrapper uses efficient CSS Grid/Flexbox layouts
- Conditional rendering reduces DOM nodes when gradient is disabled
