import React from 'react';
import { IonContent, IonPage } from '@ionic/react';
import AppHeader from '../Header/AppHeader';
import './TabLayout.css';

/**
 * TabLayout - Reusable Layout wrapper for all tab pages
 * Provides optimized full-screen display with header scroll-behind behavior
 * Includes consistent gradient background and proper spacing for floating tab bar
 */

export interface TabLayoutProps {
  /** Content to be rendered within the tab Layout */
  children: React.ReactNode;
  /** Additional CSS classes to apply to the root IonPage */
  className?: string;
  /** Whether to show the gradient background ellipses (default: true) */
  showGradient?: boolean;
  /** Whether to include horizontal padding for content (default: true) */
  includePadding?: boolean;
  /** Custom padding value to override default horizontal padding */
  customPadding?: string;
}

export interface TabLayoutConfig {
  /** Global configuration for all TabLayout instances */
  defaultShowGradient: boolean;
  defaultIncludePadding: boolean;
  defaultPadding: string;
}

const TabLayout: React.FC<TabLayoutProps> = ({
  children,
  className = '',
  showGradient = true,
  includePadding = true,
  customPadding
}) => {
  // Build dynamic CSS classes based on props
  const contentClasses = [
    'tab-Layout-content',
    'transparent-tabbar-content',
    'tab-gradient-content'
  ].join(' ');

  const wrapperClasses = [
    'tab-content-wrapper',
    !includePadding && 'no-horizontal-padding',
    customPadding && 'custom-padding'
  ].filter(Boolean).join(' ');

  const wrapperStyle = customPadding ? {
    '--custom-padding-left': customPadding,
    '--custom-padding-right': customPadding
  } as React.CSSProperties : undefined;

  return (
    <IonPage className={`tab-layout-page ${className}`}>
      <AppHeader />
      <IonContent
        fullscreen={true}
        className={contentClasses}
        scrollY={true}
      >
        {/* Figma Ellipses - Conditionally rendered based on showGradient prop */}
        {showGradient && (
          <>
            <div className="tab-ellipse-1"></div>
            <div className="tab-ellipse-2"></div>
          </>
        )}

        <div className={wrapperClasses} style={wrapperStyle}>
          {children}
        </div>
      </IonContent>
    </IonPage>
  );
};

/**
 * Utility function to create consistent TabLayout configurations
 * Helps maintain consistency across different pages while allowing customization
 */
export const createTabLayoutConfig = (overrides: Partial<TabLayoutProps> = {}): TabLayoutProps => {
  const defaults: Omit<TabLayoutProps, 'children'> = {
    className: '',
    showGradient: true,
    includePadding: true,
    customPadding: undefined
  };

  return {
    ...defaults,
    ...overrides,
    children: overrides.children || null
  };
};

/**
 * Pre-configured TabLayout variants for common use cases
 */
export const TabLayoutVariants = {
  /** Standard tab Layout with gradient and padding */
  standard: (children: React.ReactNode, className?: string) => (
    <TabLayout {...createTabLayoutConfig({ children, className })} />
  ),

  /** Full-width Layout without horizontal padding */
  fullWidth: (children: React.ReactNode, className?: string) => (
    <TabLayout {...createTabLayoutConfig({
      children,
      className,
      includePadding: false
    })} />
  ),

  /** Clean Layout without gradient background */
  clean: (children: React.ReactNode, className?: string) => (
    <TabLayout {...createTabLayoutConfig({
      children,
      className,
      showGradient: false
    })} />
  ),

  /** Minimal Layout without gradient or padding */
  minimal: (children: React.ReactNode, className?: string) => (
    <TabLayout {...createTabLayoutConfig({
      children,
      className,
      showGradient: false,
      includePadding: false
    })} />
  )
};

export default TabLayout;
