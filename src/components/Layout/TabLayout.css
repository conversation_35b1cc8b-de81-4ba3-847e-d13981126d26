/* TabLayout - Figma gradient background for all tab pages */
/* Based on StudentSelection implementation but more subtle */

/* Full-screen TabLayout page optimization */
.tab-layout-page {
  --background: var(--ion-background-color);
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  max-width: 100vw;
  max-height: 100vh;
}

/* Essential Ionic variables for full-screen content */
.tab-layout-content {
  --background: var(--ion-background-color);
  --padding-start: 0px;
  --padding-end: 0px;
  --padding-top: 0px;
  --padding-bottom: 0px;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  /* Ensure content scrolls behind fixed header */
  --offset-top: 60px;
}

/* Legacy class for backward compatibility */
.tab-gradient-content {
  --background: var(--ion-background-color);
  position: relative;
  overflow: hidden;
}

/* Figma Ellipses - Optimized for fixed header and backdrop-filter compatibility */
.tab-ellipse-1 {
  position: fixed;
  width: 719.19px;
  height: 719.19px;
  left: -142px;
  top: -13px;
  background: rgba(53, 161, 146, 0.06);
  border-radius: 50%;
  filter: blur(150px);
  z-index: -1000;
  pointer-events: none;
  /* Ensure ellipse doesn't interfere with header backdrop-filter */
  will-change: transform;
  transform: translateZ(0);
}

.tab-ellipse-2 {
  position: fixed;
  width: 513px;
  height: 513px;
  left: -277px;
  top: 537px;
  background: rgba(73, 194, 235, 0.08);
  border-radius: 50%;
  filter: blur(150px);
  z-index: -999;
  pointer-events: none;
  /* Ensure ellipse doesn't interfere with header backdrop-filter */
  will-change: transform;
  transform: translateZ(0);
}

/* Content wrapper for full-screen optimization with header scroll-behind */
.tab-content-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  width: 100%;
  padding: 0 1rem; /* Horizontal padding for content */
  padding-top: var(--app-header-content-offset); /* Dynamic padding based on actual header height + spacing */
  padding-bottom: calc(100px + env(safe-area-inset-bottom)); /* Space for floating tab bar */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.tab-content-wrapper ion-card {
  margin-left: 0;
  margin-right: 0;
}


/* Modular padding options for reusable architecture */
.tab-content-wrapper.no-horizontal-padding {
  padding-left: 0;
  padding-right: 0;
}

.tab-content-wrapper.custom-padding {
  /* Custom padding will be applied via inline styles - placeholder for dynamic padding */
  padding-left: var(--custom-padding-left, 1rem);
  padding-right: var(--custom-padding-right, 1rem);
}

/* Ensure content appears above the gradient - Legacy support */
.tab-gradient-content .min-h-full {
  position: relative;
  z-index: 10;
}

/* Utility classes for common TabLayout patterns - Use Tailwind classes instead:
   .tab-Layout-spacing-sm -> space-y-4
   .tab-Layout-spacing-md -> space-y-6
   .tab-Layout-spacing-lg -> space-y-8
   .tab-Layout-section -> mb-6
*/

.tab-layout-card {
  background-color: var(--ion-card-background);
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 var(--ion-color-step-100);
  border: 1px solid var(--ion-color-step-200);
}

/* Dark mode support is handled automatically through CSS variables */
