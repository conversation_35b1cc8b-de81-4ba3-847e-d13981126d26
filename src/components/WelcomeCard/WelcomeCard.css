/* Welcome Card - Optimized CSS + Tailwind */

/* Essential Ionic variables that must stay as CSS custom properties */
.welcome-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
}

.welcome-card-header {
  --background: transparent;
  --color: inherit;
}

/* Complex gradient background that cannot be replicated with standard Tailwind */
.welcome-header-section {
  background: linear-gradient(135deg,
    color-mix(in srgb, var(--ion-color-primary) 10%, transparent) 0%,
    color-mix(in srgb, var(--ion-color-secondary) 10%, transparent) 100%);
}

/* Complex background with animation that cannot be replicated with standard Tailwind */
.welcome-icon-container {
  background: color-mix(in srgb, var(--ion-color-primary) 20%, transparent);
  animation: gentle-bounce 3s ease-in-out infinite;
}

.welcome-icon {
  color: var(--ion-color-primary);
}

.welcome-title {
  --color: var(--ion-text-color);
}

.welcome-description {
  color: var(--ion-color-medium);
}

/* Animation that cannot be replicated with standard Tailwind */
@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .welcome-icon-container {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .welcome-card {
    border: 2px solid var(--ion-text-color);
  }

  .welcome-title,
  .welcome-description {
    color: var(--ion-text-color);
  }
}
