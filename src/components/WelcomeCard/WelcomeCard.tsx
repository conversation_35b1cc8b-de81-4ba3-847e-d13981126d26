import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon
} from '@ionic/react';
import { peopleOutline } from 'ionicons/icons';
import './WelcomeCard.css';

const WelcomeCard: React.FC = () => {
  return (
    <IonCard className="welcome-card rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="banner" aria-label="Tarjeta de bienvenida">
      <IonCardHeader className="welcome-card-header p-0">
        <div className="welcome-header-section card-header-gradient p-6">
          <div className="welcome-content-wrapper flex items-start gap-4">
            <div className="welcome-icon-container flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center" aria-hidden="true">
              <IonIcon
                icon={peopleOutline}
                className="welcome-icon text-2xl"
              />
            </div>

            <div className="welcome-content flex-1">
              <IonCardTitle className="welcome-title font-semibold text-base leading-tight mb-2 m-0">
                Hola, bienvenido.
              </IonCardTitle>
              <p className="welcome-description text-sm leading-relaxed m-0">
                En este lugar encontrarás los avances más significativos del estudiante durante su periodo académico.
              </p>
            </div>
          </div>
        </div>
      </IonCardHeader>
    </IonCard>
  );
};

export default WelcomeCard;