/* Welcome Popover - Glass-like translucent appearance with professional styling */

/* Popover positioning and backdrop */
.welcome-popover {
  --backdrop-opacity: 0;
  --width: auto;
  --min-width: 280px;
  --max-width: 400px;
  --height: auto;
  --offset-y: 20px;
  --offset-x: 0px;
}

/* Content container with glass effect */
.welcome-popover-content {
  --background: transparent;
  --color: var(--ion-text-color);
  --padding-top: 0;
  --padding-bottom: 0;
  --padding-start: 0;
  --padding-end: 0;
}

/* Main container with glass-like appearance */
.welcome-popover-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 16px 20px;
  margin: 8px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  
  display: flex;
  align-items: center;
  gap: 12px;
  
  /* Smooth entrance animation */
  animation: welcome-popover-enter 0.3s ease-out forwards;
  transform: translateY(-10px);
  opacity: 0;
}

/* Dark mode styling */
.dark .welcome-popover-container {
  background: rgba(28, 28, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Icon container with subtle background */
.welcome-popover-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: color-mix(in srgb, var(--ion-color-success) 15%, transparent);
  flex-shrink: 0;
}

/* Success icon styling */
.welcome-popover-icon {
  font-size: 24px;
  color: var(--ion-color-success);
}

/* Text container */
.welcome-popover-text-container {
  flex: 1;
  min-width: 0; /* Allows text to truncate if needed */
}

/* Title styling */
.welcome-popover-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-text-color);
  margin: 0 0 2px 0;
  line-height: 1.2;
}

/* Message styling */
.welcome-popover-message {
  font-size: 14px;
  color: var(--ion-color-medium);
  margin: 0;
  line-height: 1.3;
  font-weight: 400;
}

/* Entrance animation */
@keyframes welcome-popover-enter {
  0% {
    transform: translateY(-10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Exit animation (triggered by component state) */
.welcome-popover-container.exiting {
  animation: welcome-popover-exit 0.3s ease-in forwards;
}

@keyframes welcome-popover-exit {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: translateY(-10px);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .welcome-popover {
    --min-width: 260px;
    --max-width: calc(100vw - 32px);
  }
  
  .welcome-popover-container {
    margin: 4px;
    padding: 14px 16px;
  }
  
  .welcome-popover-title {
    font-size: 15px;
  }
  
  .welcome-popover-message {
    font-size: 13px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .welcome-popover-container {
    animation: none;
    transform: none;
    opacity: 1;
  }
  
  .welcome-popover-container.exiting {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .welcome-popover-container {
    background: var(--ion-background-color);
    border: 2px solid var(--ion-color-primary);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .welcome-popover-icon-container {
    background: var(--ion-color-success);
  }
  
  .welcome-popover-icon {
    color: var(--ion-color-success-contrast);
  }
}
