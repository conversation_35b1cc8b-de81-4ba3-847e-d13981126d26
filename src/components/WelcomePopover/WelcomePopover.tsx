import React, { useEffect, useState } from 'react';
import {
  IonPopover,
  IonContent,
  IonText,
  IonIcon,
} from '@ionic/react';
import {
  checkmarkCircleOutline,
} from 'ionicons/icons';
import './WelcomePopover.css';

interface WelcomePopoverProps {
  isOpen: boolean;
  userName: string;
  onDidDismiss: () => void;
  autoHideDuration?: number; // Duration in milliseconds, default 3000
}

const WelcomePopover: React.FC<WelcomePopoverProps> = ({
  isOpen,
  userName,
  onDidDismiss,
  autoHideDuration = 3000
}) => {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Small delay to ensure smooth animation
      const showTimer = setTimeout(() => {
        setShouldShow(true);
      }, 100);

      // Auto-hide after specified duration
      const hideTimer = setTimeout(() => {
        setShouldShow(false);
        // Allow animation to complete before calling onDidDismiss
        setTimeout(onDidDismiss, 300);
      }, autoHideDuration);

      return () => {
        clearTimeout(showTimer);
        clearTimeout(hideTimer);
      };
    } else {
      setShouldShow(false);
    }
  }, [isOpen, autoHideDuration, onDidDismiss]);

  return (
    <IonPopover
      isOpen={shouldShow}
      onDidDismiss={onDidDismiss}
      showBackdrop={false}
      dismissOnSelect={false}
      side="top"
      alignment="center"
      className="welcome-popover"
    >
      <IonContent className="welcome-popover-content">
        <div className="welcome-popover-container">
          <div className="welcome-popover-icon-container">
            <IonIcon
              icon={checkmarkCircleOutline}
              className="welcome-popover-icon"
            />
          </div>
          
          <div className="welcome-popover-text-container">
            <IonText>
              <h3 className="welcome-popover-title">
                ¡Bienvenido!
              </h3>
            </IonText>
            <IonText>
              <p className="welcome-popover-message">
                Hola, {userName}
              </p>
            </IonText>
          </div>
        </div>
      </IonContent>
    </IonPopover>
  );
};

export default WelcomePopover;
