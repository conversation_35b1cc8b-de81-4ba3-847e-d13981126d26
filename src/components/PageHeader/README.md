# PageHeader Component

Componente reutilizable para mostrar títulos y descripciones de página siguiendo el diseño de Figma.

## Uso

```tsx
import PageHeader from '../../components/PageHeader/PageHeader';

// Uso básico con descripción
<PageHeader
  title="Título de la página"
  description="Descripción explicativa de la página o sección."
/>

// Solo título (sin descripción)
<PageHeader
  title="Recursos"
/>

// Con clase CSS personalizada
<PageHeader
  title="Título personalizado"
  description="Descripción con estilos adicionales."
  className="mi-clase-personalizada"
/>
```

## Props

| Prop | Tipo | Requerido | Descripción |
|------|------|-----------|-------------|
| `title` | `string` | ✅ | Título principal que se muestra en la parte superior |
| `description` | `string` | ❌ | Texto descriptivo que aparece debajo del título (opcional) |
| `className` | `string` | ❌ | Clases CSS adicionales para personalización |

## Ejemplos de uso

### Página de Informes
```tsx
<PageHeader 
  title="Progreso curricular"
  description="Se muestra el momento en que se encuentra el alumno. Junto con los resultados de las evaluaciones estándares de sumun."
/>
```

### Página de Portfolio (solo título)
```tsx
<PageHeader
  title="Recursos"
/>
```

### Página de Portfolio (con descripción)
```tsx
<PageHeader
  title="Mi Portfolio"
  description="Aquí encontrarás todos tus trabajos y proyectos realizados durante el curso académico."
/>
```

### Página de Horarios
```tsx
<PageHeader 
  title="Horarios Académicos"
  description="Consulta tus horarios de clases, actividades extracurriculares y eventos importantes."
/>
```

## Características

- ✅ Diseño responsive
- ✅ Soporte para modo oscuro
- ✅ Tipografía Inter siguiendo el sistema de diseño
- ✅ Centrado automático con ancho máximo de 329px
- ✅ Gap de 16px entre elementos (siguiendo Figma)
- ✅ Línea divisoria de 0.5px
- ✅ Clases CSS personalizables

## Estructura Visual

```
┌─────────────────────────┐
│       TÍTULO            │ ← 18px, bold, centrado
├─────────────────────────┤ ← Línea divisoria 0.5px
│     Descripción         │ ← 12px, normal, centrado
│   explicativa del       │
│     contenido           │
└─────────────────────────┘
```
