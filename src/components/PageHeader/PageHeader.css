/* PageHeader.css - Use Tailwind classes instead:
   .page-header-container -> flex flex-col items-center gap-4 w-full max-w-[329px] mx-auto px-4
*/

.page-header-title {
  font-weight: 700;
  font-size: 18px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-text-color, #4E4E4E);
  margin: 0;
  width: 100%;
}

.page-header-divider {
  width: 100%;
  height: 0.5px;
  background-color: var(--ion-color-light-shade, #DFDFDF);
  border: none;
}

.page-header-description {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-text-color, #4E4E4E);
  margin: 0;
  width: 100%;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .page-header-title,
  .page-header-description {
    color: var(--ion-text-color);
  }
  
  .page-header-divider {
    background-color: var(--ion-color-light-shade);
    opacity: 0.3;
  }
}


