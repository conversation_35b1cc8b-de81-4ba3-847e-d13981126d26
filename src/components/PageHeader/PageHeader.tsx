import React from 'react';
import './PageHeader.css';

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  className = ''
}) => {
  return (
    <div className={`page-header-container p-4 gap-2 text flex flex-col ${className}`}>
      <h2 className="page-header-title">
        {title}
      </h2>
      {description && (
        <>
          <div className="page-header-divider"></div>
          <p className="page-header-description text-left">
            {description}
          </p>
        </>
      )}
    </div>
  );
};

export default PageHeader;
