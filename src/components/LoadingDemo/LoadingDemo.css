/* LoadingDemo - Component Styles */

.loading-demo-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.demo-section {
  border-bottom: 1px solid var(--ion-color-light-shade);
  padding-bottom: 1.5rem;
}

.demo-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.demo-status {
  background: var(--ion-color-light);
  border-radius: 0.5rem;
  border: 1px solid var(--ion-color-light-shade);
}

/* Dark mode support */
.ion-palette-dark .demo-status {
  background: var(--ion-color-dark);
  border-color: var(--ion-color-dark-shade);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .loading-demo-container {
    padding: 0.5rem;
  }
  
  .demo-section {
    padding-bottom: 1rem;
  }
}
