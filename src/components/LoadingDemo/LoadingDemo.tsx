/**
 * LoadingDemo - HTTP Loading System Example
 * Demonstrates how to use the loading system specifically for HTTP requests
 * Shows integration with SkeletonLoader, EnhancedHttpService for HTTP operations only
 * Note: Loading should only be used for HTTP requests, not for UI operations
 */

import React, { useState } from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonItem,
  IonLabel,
  IonList,
  IonIcon,
  IonText,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import {
  refreshOutline,
  cloudDownloadOutline,
  alertCircleOutline,
  checkmarkCircleOutline,
  timeOutline
} from 'ionicons/icons';
import { SkeletonLoader } from '../';
import { useHttpLoading, useHttpOperation } from '../../contexts/LoadingContext';
import { useError, useComponentError, useAsyncWithError } from '../../contexts/ErrorContext';
import LoadingButton from '../LoadingButton/LoadingButton';
import ProgressIndicator from '../ProgressIndicator/ProgressIndicator';
import { EnhancedHttpService } from '../../services/enhanced-http.service';
import './LoadingDemo.css';

interface DemoData {
  id: number;
  title: string;
  description: string;
  timestamp: string;
}

const LoadingDemo: React.FC = () => {
  const [data, setData] = useState<DemoData[]>([]);
  const [showProgress, setShowProgress] = useState(false);
  const [progress, setProgress] = useState(0);

  // HTTP loading monitoring
  const { isLoading: httpLoading, loadingMessage } = useHttpLoading();

  // Error handling
  const { handleError, clearError, lastError } = useError();
  const { 
    error: componentError, 
    handleError: handleComponentError, 
    clearError: clearComponentError 
  } = useComponentError('LoadingDemo');

  // HTTP operation with automatic loading (managed by EnhancedHttpService)
  const {
    execute: fetchData,
    isLoading: fetchLoading,
    error: fetchError
  } = useHttpOperation(async () => {
    // Simulate HTTP API call - loading overlay will be automatically shown
    // In real usage, this would be: await EnhancedHttpService.get('/api/data')
    await new Promise(resolve => setTimeout(resolve, 2000));

    const mockData: DemoData[] = [
      {
        id: 1,
        title: 'Datos de ejemplo 1',
        description: 'Descripción de los datos cargados desde la API',
        timestamp: new Date().toISOString()
      },
      {
        id: 2,
        title: 'Datos de ejemplo 2',
        description: 'Más información cargada desde el servidor',
        timestamp: new Date().toISOString()
      }
    ];

    return mockData;
  }, 'fetch-demo-data');

  // Async operation with error handling
  const { 
    execute: fetchWithError, 
    isLoading: errorAsyncLoading, 
    error: errorAsyncError,
    retry: retryFetch
  } = useAsyncWithError(async () => {
    // Simulate API call that fails
    await new Promise(resolve => setTimeout(resolve, 1500));
    throw new Error('Error simulado para demostración');
  }, {
    severity: 'medium',
    showToast: true,
    retryable: true
  });

  // HTTP request demonstration
  const handleHttpRequest = async () => {
    try {
      // This will automatically show loading overlay during the HTTP request
      const response = await EnhancedHttpService.get('https://jsonplaceholder.typicode.com/posts/1', {
        loadingMessage: 'Cargando datos desde API...',
        operation: 'fetch-post-data'
      });

      console.log('HTTP Response:', response);
      setData([{
        id: response.data.id,
        title: response.data.title,
        description: response.data.body,
        timestamp: new Date().toISOString()
      }]);
    } catch (error) {
      console.error('HTTP Request failed:', error);
    }
  };

  // Skeleton demonstration with actual HTTP data loading
  const handleSkeletonDemo = async () => {
    setData([]); // Clear existing data

    try {
      // Use the actual HTTP operation which will show skeleton loading automatically
      const result = await fetchData();
      setData(result);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  // Error simulation
  const handleErrorSimulation = async () => {
    try {
      await handleError(new Error('Error de demostración'), {
        severity: 'high',
        showAlert: true,
        customMessage: 'Este es un error de demostración para mostrar el sistema de manejo de errores.'
      });
    } catch (error) {
      console.error('Error handling demo:', error);
    }
  };

  // Progress demonstration
  const handleProgressDemo = () => {
    setShowProgress(true);
    setProgress(0);

    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => setShowProgress(false), 1000);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 200);
  };

  return (
    <div className="loading-demo-container">
      <IonCard className="card-base">
        <IonCardHeader className="card-header-unified">
          <IonCardTitle className="card-title-unified">
            Sistema de Carga y Manejo de Errores
          </IonCardTitle>
        </IonCardHeader>

        <IonCardContent className="card-content-unified">
          <IonText color="medium" className="block mb-4">
            Demostración del sistema de carga para peticiones HTTP. El loading se activa automáticamente solo durante las peticiones HTTP.
          </IonText>

          {/* HTTP Service Demo */}
          <div className="demo-section mb-6">
            <h3 className="text-lg font-semibold mb-3">Peticiones HTTP con Loading Automático</h3>
            <IonGrid>
              <IonRow>
                <IonCol size="6">
                  <IonButton
                    expand="block"
                    onClick={handleHttpRequest}
                    color="primary"
                    disabled={httpLoading}
                  >
                    <IonIcon icon={cloudDownloadOutline} slot="start" />
                    API Real
                  </IonButton>
                </IonCol>
                <IonCol size="6">
                  <IonButton
                    expand="block"
                    onClick={() => fetchData().then(setData)}
                    disabled={fetchLoading}
                    color="secondary"
                  >
                    <IonIcon icon={checkmarkCircleOutline} slot="start" />
                    API Simulada
                  </IonButton>
                </IonCol>
              </IonRow>
            </IonGrid>
            <IonText color="medium" className="block text-sm mt-2">
              El loading se muestra automáticamente durante las peticiones HTTP
            </IonText>
          </div>

          {/* Error Handling Demo */}
          <div className="demo-section mb-6">
            <h3 className="text-lg font-semibold mb-3">Manejo de Errores</h3>
            <IonButton 
              expand="block" 
              onClick={handleErrorSimulation}
              color="warning"
            >
              <IonIcon icon={alertCircleOutline} slot="start" />
              Simular Error
            </IonButton>
          </div>

          {/* Skeleton Demo */}
          <div className="demo-section mb-6">
            <h3 className="text-lg font-semibold mb-3">Skeleton Loader (HTTP)</h3>
            <IonButton
              expand="block"
              onClick={handleSkeletonDemo}
              disabled={fetchLoading}
              color="tertiary"
            >
              <IonIcon icon={refreshOutline} slot="start" />
              Cargar Datos HTTP
            </IonButton>
          </div>

          {/* Modern Loading Button Demo */}
          <div className="demo-section mb-6">
            <h3 className="text-lg font-semibold mb-3">Loading Button</h3>
            <IonGrid>
              <IonRow>
                <IonCol size="6">
                  <LoadingButton
                    onClick={async () => {
                      await new Promise(resolve => setTimeout(resolve, 2000));
                    }}
                    color="primary"
                    expand="block"
                    loadingText="Cargando..."
                  >
                    Botón Automático
                  </LoadingButton>
                </IonCol>
                <IonCol size="6">
                  <LoadingButton
                    onClick={handleProgressDemo}
                    color="secondary"
                    expand="block"
                    showProgress={showProgress}
                    progress={progress}
                    loadingText="Progreso..."
                  >
                    Con Progreso
                  </LoadingButton>
                </IonCol>
              </IonRow>
            </IonGrid>
          </div>

          {/* Progress Indicators Demo */}
          <div className="demo-section mb-6">
            <h3 className="text-lg font-semibold mb-3">Indicadores de Progreso</h3>

            {showProgress && (
              <div className="space-y-4">
                <ProgressIndicator
                  value={progress}
                  variant="linear"
                  showPercentage={true}
                  showLabel={true}
                  label="Progreso Lineal"
                  color="primary"
                />

                <div className="flex justify-center">
                  <ProgressIndicator
                    value={progress}
                    variant="circular"
                    showPercentage={true}
                    showLabel={true}
                    label="Progreso Circular"
                    color="secondary"
                    size="large"
                  />
                </div>

                <ProgressIndicator
                  value={progress}
                  variant="minimal"
                  showPercentage={true}
                  showLabel={true}
                  label="Progreso Minimal"
                  color="tertiary"
                />
              </div>
            )}

            {!showProgress && (
              <IonText color="medium" className="block text-center py-4">
                Usa el botón "Con Progreso" para ver los indicadores
              </IonText>
            )}
          </div>

          {/* Data Display */}
          <div className="demo-section">
            <h3 className="text-lg font-semibold mb-3">Datos Cargados</h3>
            
            {fetchLoading ? (
              <SkeletonLoader
                variant="list"
                listConfig={{
                  items: 3,
                  hasAvatar: false,
                  hasSecondaryText: true
                }}
              />
            ) : data.length > 0 ? (
              <IonList>
                {data.map((item) => (
                  <IonItem key={item.id}>
                    <IonLabel>
                      <h3>{item.title}</h3>
                      <p>{item.description}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(item.timestamp).toLocaleString()}
                      </p>
                    </IonLabel>
                  </IonItem>
                ))}
              </IonList>
            ) : (
              <IonText color="medium" className="block text-center py-8">
                No hay datos para mostrar. Usa los botones de arriba para cargar datos.
              </IonText>
            )}
          </div>

          {/* Status Information */}
          <div className="demo-status mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-semibold mb-2">Estado del Sistema HTTP</h4>
            <div className="text-sm space-y-1">
              <div>Loading HTTP: {httpLoading ? '✅ Activo' : '❌ Inactivo'}</div>
              <div>Mensaje: {loadingMessage || 'Ninguno'}</div>
              <div>Operación Fetch: {fetchLoading ? '✅ Activa' : '❌ Inactiva'}</div>
              <div>Último Error: {lastError ? `⚠️ ${lastError.message}` : '✅ Ninguno'}</div>
            </div>
            <IonText color="medium" className="block text-xs mt-3">
              ℹ️ El loading solo se activa durante peticiones HTTP
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    </div>
  );
};

export default LoadingDemo;
