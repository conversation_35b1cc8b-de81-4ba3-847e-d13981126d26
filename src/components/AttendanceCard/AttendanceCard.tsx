import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon
} from '@ionic/react';
import { informationCircleOutline } from 'ionicons/icons';
import './AttendanceCard.css';

interface AttendanceData {
  date: string;
  dayName: string;
  dayNumber: number;
  amStatus: 'present' | 'late' | 'justified' | 'unjustified';
  pmStatus: 'present' | 'late' | 'justified' | 'unjustified';
}

interface AttendanceCardProps {
  data: AttendanceData[];
  month: string;
  period: string;
}

const getStatusColorClass = (status: 'present' | 'late' | 'justified' | 'unjustified'): string => {
  switch (status) {
    case 'present': return 'bg-[var(--ion-color-primary)]'; // Azul para asistencia
    case 'late': return 'bg-[var(--ion-color-success)]'; // Verde para retrasos
    case 'justified': return 'bg-[var(--ion-color-warning)]'; // Naranja para justificadas
    case 'unjustified': return 'bg-[var(--ion-color-danger)]'; // Rojo para injustificadas
    default: return 'bg-[var(--ion-color-light)]';
  }
};

const getStatusLabel = (status: 'present' | 'late' | 'justified' | 'unjustified'): string => {
  switch (status) {
    case 'present': return 'Asistencia';
    case 'late': return 'Retrasos';
    case 'justified': return 'Justificadas';
    case 'unjustified': return 'Injustificadas';
    default: return '';
  }
};

const AttendanceCard: React.FC<AttendanceCardProps> = ({ data, month, period }) => {

  // Calculate attendance statistics
  const stats = data.reduce((acc, day) => {
    [day.amStatus, day.pmStatus].forEach(status => {
      acc[status] = (acc[status] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);

  return (
    <IonCard className="attendance-card mb-6 rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Tarjeta de asistencia del alumno">
      <IonCardHeader className="attendance-header pb-0">
        <div className="flex items-center justify-between mb-4">
          <IonCardTitle className="attendance-title text-lg font-semibold m-0">
            Asistencia del alumno
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="info-button w-8 h-8"
            aria-label="Información sobre asistencia"
          >
            <IonIcon
              icon={informationCircleOutline}
              slot="icon-only"
            />
          </IonButton>
        </div>

        <div className="flex items-center gap-4 mb-4">
          <div className="period-badge py-1 px-3 rounded-xl text-xs font-medium">
            {period}
          </div>
          <span className="month-text text-sm font-medium">
            {month}
          </span>
        </div>
      </IonCardHeader>

      <IonCardContent className="attendance-content pt-0">
        {/* Attendance Grid */}
        <div className="attendance-grid relative mb-6">
          {/* AM/PM Labels */}
          <div className="period-labels absolute left-0 top-0 flex flex-col justify-between z-10">
            <div className="text-figma-small">A.M</div>
            <div className="text-figma-small">P.M</div>
          </div>

          {/* Days Grid */}
          <div className="days-container flex justify-between relative">
            {data.map((day, index) => (
              <div key={index} className="attendance-day flex flex-col items-center relative">
                {/* Day Info */}
                <div className="absolute bottom-0 text-center w-full">
                  <div className="text-figma-small mb-0.5">{day.dayName}</div>
                  <div className="text-figma-small">{day.dayNumber}</div>
                </div>

                {/* Status Bar */}
                <div className="status-bar-container flex flex-col mx-auto relative">
                  <div
                    className={`rounded-[10px] transition-all duration-200 ease-in-out am-bar ${getStatusColorClass(day.amStatus)}`}
                    role="img"
                    aria-label={`Mañana: ${getStatusLabel(day.amStatus)}`}
                  ></div>
                  <div
                    className={`rounded-[10px] transition-all duration-200 ease-in-out pm-bar ${getStatusColorClass(day.pmStatus)}`}
                    role="img"
                    aria-label={`Tarde: ${getStatusLabel(day.pmStatus)}`}
                  ></div>
                </div>
              </div>
            ))}
          </div>

          {/* Divider Line */}
          <div className="divider-line absolute z-0"></div>
        </div>

        {/* Legend */}
        <div className="attendance-legend pt-4">
          <div className="flex flex-wrap gap-3 justify-center">
            {Object.entries(stats).map(([status, count]) => (
              <div key={status} className="flex items-center gap-2 text-xs">
                <div
                  className={`w-3 h-3 rounded-full shadow-sm ${getStatusColorClass(status as any)}`}
                ></div>
                <span className="legend-text text-[var(--ion-text-color)]">
                  {getStatusLabel(status as any)} ({count})
                </span>
              </div>
            ))}
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default AttendanceCard;
