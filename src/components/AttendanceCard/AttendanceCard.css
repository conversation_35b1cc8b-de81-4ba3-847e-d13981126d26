/* Attendance Card - Optimized CSS + Tailwind */

/* Essential Ionic variables that must stay as CSS custom properties */
.attendance-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
}

.attendance-header {
  --background: transparent;
  --color: inherit;
}

.attendance-title {
  --color: var(--ion-text-color);
}

.info-button {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
}

.period-badge {
  background: var(--ion-color-light);
  color: var(--ion-color-dark);
  border: 1px solid var(--ion-color-light-shade);
}

.month-text {
  color: var(--ion-color-medium);
}

.attendance-content {
  --background: transparent;
  --color: inherit;
}

/* Custom attendance grid dimensions - Specific measurements from Figma design */
.attendance-grid {
  height: 120px;
}

.period-labels {
  height: 84px;
  width: 16px;
}

.days-container {
  margin-left: 32px;
  height: 120px;
}

.attendance-day {
  width: 32px;
}

.status-bar-container {
  height: 84px;
  width: 12.7px;
}

.am-bar, .pm-bar {
  height: 42px;
}

.divider-line {
  top: 42px;
  left: 32px;
  right: 0;
  height: 0;
  border-top: 1px dashed var(--ion-border-color);
}

/* Legend styling - Keep border and text color variables */
.attendance-legend {
  border-top: 1px solid var(--ion-color-light-shade);
}

.legend-text {
  color: var(--ion-color-medium);
}

/* Loading skeleton styles - Keep background variables and animation */
.skeleton-title, .skeleton-subtitle, .skeleton-day {
  background: var(--ion-color-light);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive design - Custom sizing that can't be done with Tailwind */
@media (max-width: 640px) {
  .attendance-grid { height: 100px; }
  .status-bar-container { height: 70px; width: 10px; }
  .am-bar, .pm-bar { height: 35px; }
  .divider-line { top: 35px; }
  .period-labels { height: 70px; }
  .days-container { height: 100px; }
}

@media (max-width: 480px) {
  .attendance-grid { height: 90px; }
  .status-bar-container { height: 60px; width: 8px; }
  .am-bar, .pm-bar { height: 30px; }
  .divider-line { top: 30px; }
  .period-labels { height: 60px; }
  .days-container { height: 90px; margin-left: 24px; }
  .attendance-day { width: 24px; }
}

/* Dark mode support is handled automatically through CSS variables */

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .skeleton-title, .skeleton-subtitle, .skeleton-day {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  .attendance-card {
    border: 2px solid var(--ion-text-color);
  }
  .status-bar {
    border: 1px solid var(--ion-text-color);
  }
  .divider-line {
    border-width: 2px;
    background: var(--ion-text-color);
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
