/* Portfolio SearchBar - Following Figma design specifications */

.portfolio-search-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin: 0;
  padding: 4px 12px;
  border: 1px solid var(--ion-color-light-shade);
}

.portfolio-searchbar {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  --placeholder-color: #A4A4A4;
  --placeholder-opacity: 1;
  --icon-color: var(--ion-color-medium);
  --clear-button-color: var(--ion-color-medium);
  --border-radius: 24px;
  --box-shadow: none;
}

.portfolio-searchbar .searchbar-input {
  font-size: 12px;
  font-weight: 400;
  color: var(--ion-text-color);
}
