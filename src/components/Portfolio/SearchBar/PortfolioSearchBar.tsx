import React, { useState } from 'react';
import {
  IonSearchbar,
  IonCard
} from '@ionic/react';

interface PortfolioSearchBarProps {
  onSearch?: (searchTerm: string) => void;
  placeholder?: string;
  className?: string;
}

const PortfolioSearchBar: React.FC<PortfolioSearchBarProps> = ({
  onSearch,
  placeholder = "Buscador de recursos",
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState<string>('');

  const handleSearch = (e: CustomEvent) => {
    const value = e.detail.value;
    setSearchTerm(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  const handleClear = () => {
    setSearchTerm('');
    if (onSearch) {
      onSearch('');
    }
  };

  return (
      <IonSearchbar
        value={searchTerm}
        placeholder={placeholder}
        onIonInput={handleSearch}
        onIonClear={handleClear}
        showClearButton="focus"
        className="portfolio-searchbar p-0"
        searchIcon={undefined}
        clearIcon={undefined}
        debounce={300}
      />
  );
};

export default PortfolioSearchBar;
