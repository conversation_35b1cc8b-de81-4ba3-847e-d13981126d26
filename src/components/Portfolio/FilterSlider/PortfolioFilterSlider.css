/* Portfolio FilterSlider - Following Figma design specifications */

.portfolio-filter-slider {
  width: 100%;
  overflow: hidden;
}

.filter-chips-container {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0 4px 8px 4px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.filter-chips-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.filter-chip {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  flex-shrink: 0;
  width: 144px;
  height: 79px;
  border-radius: 6px;
  border: 1px solid transparent;
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 7px 16px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  margin: 0;
}

.filter-chip.selected {
  border-color: var(--ion-color-primary);
  --background: var(--ion-card-background);
}

.filter-chip.unselected {
  border-color: transparent;
  --background: var(--ion-card-background);
}

.filter-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0px -2px 15px 2px rgba(0, 0, 0, 0.08);
}

.filter-chip:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

.filter-chip:focus:not(:focus-visible) {
  outline: none;
}

.filter-icon-container {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* SVG Icon Styles */
.filter-icon-svg {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.filter-icon-svg.selected {
  color: #35A192; /* Green color for selected state */
}

.filter-icon-svg.unselected {
  color: #A4A4A4; /* Gray color for unselected state */
}

.filter-icon-svg svg {
  width: 100%;
  height: 100%;
}

.filter-label {
  font-size: 12px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-text-color);
  margin: 0;
  width: 100%;
}

.filter-chip.selected .filter-label {
  font-weight: 600;
  color: var(--ion-color-primary);
}

.filter-chip.unselected .filter-label {
  font-weight: 400;
  color: #A4A4A4;
}

/* Dark mode support is handled automatically through CSS variables *