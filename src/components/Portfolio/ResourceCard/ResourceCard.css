/* ResourceCard - Following Figma design specifications and AttendanceCard standards */

.resource-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin: 0 0 16px 0;
  border: 0.5px solid rgba(217, 217, 217, 0.2);
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.resource-card-content {
  padding: 0;
  margin: 0;
}

/* Image Section */
.resource-image-container {
  width: 100%;
  height: 120px;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

.resource-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px 4px 0 0;
}

.resource-image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #D9D9D9;
  border-radius: 4px 4px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Content Section - Use Tailwind classes instead:
   .resource-content-section -> p-2 flex flex-col gap-2
   .resource-header -> flex items-center gap-0.5 w-full
   .resource-text-content -> flex-1 flex flex-col gap-0.5 min-w-0
*/

.resource-title {
  font-size: 12px;
  font-weight: 700;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Metadata */
.resource-metadata {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.metadata-item {
  font-size: 10px;
  font-weight: 400;
  line-height: 1.21;
  color: var(--ion-color-medium);
  white-space: nowrap;
}

.metadata-separator {
  width: 0;
  height: 10px;
  border-left: 1px solid #45B2FF;
  flex-shrink: 0;
}

/* Chevron Button */
.resource-chevron-button {
  --color: #45B2FF;
  --background: transparent;
  --background-hover: rgba(69, 178, 255, 0.1);
  --background-focused: rgba(69, 178, 255, 0.1);
  --padding-start: 4px;
  --padding-end: 4px;
  --padding-top: 4px;
  --padding-bottom: 4px;
  width: 16px;
  height: 16px;
  margin: 0;
  flex-shrink: 0;
}

.resource-chevron-icon {
  font-size: 12px;
  color: #45B2FF;
}

/* Description */
.resource-description {
  width: 100%;
}

.description-text {
  font-size: 10px;
  font-weight: 400;
  line-height: 1.21;
  color: var(--ion-color-medium);
  margin: 0;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* Hover and Focus Effects */
.resource-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px -2px 15px 2px rgba(0, 0, 0, 0.08);
}

.resource-card:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

.resource-card:focus:not(:focus-visible) {
  outline: none;
}

.resource-card:hover .resource-chevron-button {
  --background: rgba(69, 178, 255, 0.1);
}

/* Dark mode support is handled automatically through CSS variables */

/* Responsive Design */
@media (max-width: 768px) {
  .resource-card {
    margin: 0 0 12px 0;
  }
  
  .resource-content-section {
    padding: 6px;
  }
  
  .resource-title {
    font-size: 11px;
  }
  
  .metadata-item {
    font-size: 9px;
  }
  
  .description-text {
    font-size: 9px;
  }
}
