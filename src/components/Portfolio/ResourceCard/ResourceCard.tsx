import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonIcon,
  IonText,
  IonButton
} from '@ionic/react';
import { chevronForward } from 'ionicons/icons';
import './ResourceCard.css';

interface ResourceMetadata {
  date?: string;
  category?: string;
  subject?: string;
}

interface ResourceCardProps {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  metadata: ResourceMetadata;
  onCardClick?: (id: string) => void;
  className?: string;
}

const ResourceCard: React.FC<ResourceCardProps> = ({
  id,
  title,
  description,
  imageUrl,
  metadata,
  onCardClick,
  className = ""
}) => {
  const handleCardClick = () => {
    if (onCardClick) {
      onCardClick(id);
    }
  };

  const renderMetadata = () => {
    const metadataItems = [];
    
    if (metadata.date) {
      metadataItems.push(metadata.date);
    }
    
    if (metadata.category) {
      metadataItems.push(metadata.category);
    }
    
    if (metadata.subject) {
      metadataItems.push(metadata.subject);
    }
    
    return metadataItems.map((item, index) => (
      <React.Fragment key={index}>
        <IonText className="metadata-item">
          {item}
        </IonText>
        {index < metadataItems.length - 1 && (
          <div className="metadata-separator"></div>
        )}
      </React.Fragment>
    ));
  };

  return (
    <IonCard
      className={`resource-card rounded-md shadow-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-md ${className}`}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick();
        }
      }}
      aria-label={`Recurso: ${title}`}
    >
      <IonCardContent className="resource-card-content p-0">
        {/* Image Section */}
        <div className="resource-image-container">
          {imageUrl ? (
            <img 
              src={imageUrl} 
              alt={title}
              className="resource-image"
            />
          ) : (
            <div className="resource-image-placeholder"></div>
          )}
        </div>
        
        {/* Content Section */}
        <div className="resource-content-section">
          <div className="resource-header">
            <div className="resource-text-content">
              <h3 className="resource-title">
                {title}
              </h3>
              
              {/* Metadata */}
              <div className="resource-metadata">
                {renderMetadata()}
              </div>
            </div>
            
            {/* Chevron Icon */}
            <IonButton
              fill="clear"
              size="small"
              className="resource-chevron-button"
              onClick={handleCardClick}
            >
              <IonIcon
                icon={chevronForward}
                slot="icon-only"
                className="resource-chevron-icon"
              />
            </IonButton>
          </div>
          
          {/* Description */}
          <div className="resource-description">
            <IonText>
              <p className="description-text">
                {description}
              </p>
            </IonText>
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default ResourceCard;
