/* SkeletonLoader - Modern CSS with Shimmer Effects following AttendanceCard standards */

/* Base skeleton wrapper with fade-in animation */
.skeleton-loader-wrapper {
  width: 100%;
  animation: skeleton-fade-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Skeleton Card - Following AttendanceCard design standards */
.skeleton-card {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: none;
  margin: 0;
  border-radius: 16px;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.skeleton-card .card-header-unified {
  --background: transparent;
  --color: inherit;
}

.skeleton-card .card-content-unified {
  --background: transparent;
  --color: inherit;
}

/* Modern skeleton elements with shimmer effect */
.skeleton-title,
.skeleton-subtitle,
.skeleton-content,
.skeleton-button,
.skeleton-icon,
.skeleton-image,
.skeleton-primary-text,
.skeleton-secondary-text,
.skeleton-grid-image,
.skeleton-grid-title,
.skeleton-grid-subtitle,
.skeleton-custom-element {
  background: linear-gradient(
    90deg,
    var(--ion-color-light) 0%,
    var(--ion-color-light-shade) 50%,
    var(--ion-color-light) 100%
  );
  background-size: 200% 100%;
  position: relative;
  overflow: hidden;
  animation: skeleton-shimmer 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Shimmer overlay effect */
.skeleton-title::before,
.skeleton-subtitle::before,
.skeleton-content::before,
.skeleton-button::before,
.skeleton-icon::before,
.skeleton-image::before,
.skeleton-primary-text::before,
.skeleton-secondary-text::before,
.skeleton-grid-image::before,
.skeleton-grid-title::before,
.skeleton-grid-subtitle::before,
.skeleton-custom-element::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: skeleton-shimmer-wave 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Dark mode shimmer adjustments */
.ion-palette-dark .skeleton-title::before,
.ion-palette-dark .skeleton-subtitle::before,
.ion-palette-dark .skeleton-content::before,
.ion-palette-dark .skeleton-button::before,
.ion-palette-dark .skeleton-icon::before,
.ion-palette-dark .skeleton-image::before,
.ion-palette-dark .skeleton-primary-text::before,
.ion-palette-dark .skeleton-secondary-text::before,
.ion-palette-dark .skeleton-grid-image::before,
.ion-palette-dark .skeleton-grid-title::before,
.ion-palette-dark .skeleton-grid-subtitle::before,
.ion-palette-dark .skeleton-custom-element::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
}

/* Specific skeleton element styling with modern design */
.skeleton-title {
  height: 1.5rem;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.skeleton-subtitle {
  height: 1rem;
  border-radius: 6px;
  margin-bottom: 0.375rem;
}

.skeleton-content {
  height: 1rem;
  border-radius: 6px;
  margin-bottom: 0.375rem;
}

.skeleton-button {
  height: 2.5rem;
  border-radius: 12px;
  margin: 0.25rem;
}

.skeleton-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.skeleton-image {
  height: 8rem;
  border-radius: 16px;
  margin-bottom: 0.75rem;
}

/* List skeleton styling */
.skeleton-list-container {
  background: transparent;
}

.skeleton-list-item {
  --background: transparent;
  --border-color: var(--ion-color-light-shade);
  margin-bottom: 0.5rem;
}

.skeleton-avatar {
  width: 3rem;
  height: 3rem;
}

.skeleton-primary-text {
  height: 1.25rem;
  border-radius: 0.25rem;
}

.skeleton-secondary-text {
  height: 1rem;
  border-radius: 0.25rem;
}

/* Grid skeleton styling */
.skeleton-grid-container {
  width: 100%;
}

.skeleton-grid-item {
  display: flex;
  flex-direction: column;
}

.skeleton-grid-image {
  height: 6rem;
  border-radius: 0.5rem;
}

.skeleton-grid-title {
  height: 1rem;
  border-radius: 0.25rem;
}

.skeleton-grid-subtitle {
  height: 0.75rem;
  border-radius: 0.25rem;
}

/* Text skeleton container */
.skeleton-text-container {
  width: 100%;
}

.skeleton-line {
  height: 1rem;
  border-radius: 0.25rem;
  margin-bottom: 0.75rem;
}

.skeleton-line:last-child {
  margin-bottom: 0;
}

/* Custom skeleton container */
.skeleton-custom-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Modern skeleton animations */
@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeleton-shimmer-wave {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Fade-out animation for content loading */
.skeleton-loader-wrapper.fade-out {
  animation: skeleton-fade-out 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes skeleton-fade-out {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-8px);
  }
}

/* Dark mode support - handled automatically through CSS variables */
/* The skeleton elements use --ion-color-light which adapts to dark mode */

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .skeleton-loader-wrapper {
    animation: none;
  }

  .skeleton-loader-wrapper.fade-out {
    animation: none;
    opacity: 0;
  }

  .skeleton-title,
  .skeleton-subtitle,
  .skeleton-content,
  .skeleton-button,
  .skeleton-icon,
  .skeleton-image,
  .skeleton-primary-text,
  .skeleton-secondary-text,
  .skeleton-grid-image,
  .skeleton-grid-title,
  .skeleton-grid-subtitle,
  .skeleton-custom-element {
    animation: none;
    background: var(--ion-color-light);
  }

  .skeleton-title::before,
  .skeleton-subtitle::before,
  .skeleton-content::before,
  .skeleton-button::before,
  .skeleton-icon::before,
  .skeleton-image::before,
  .skeleton-primary-text::before,
  .skeleton-secondary-text::before,
  .skeleton-grid-image::before,
  .skeleton-grid-title::before,
  .skeleton-grid-subtitle::before,
  .skeleton-custom-element::before {
    display: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .skeleton-title,
  .skeleton-subtitle,
  .skeleton-content,
  .skeleton-button,
  .skeleton-icon,
  .skeleton-image,
  .skeleton-primary-text,
  .skeleton-secondary-text,
  .skeleton-grid-image,
  .skeleton-grid-title,
  .skeleton-grid-subtitle,
  .skeleton-custom-element {
    border: 1px solid var(--ion-text-color);
    background: var(--ion-color-light-shade);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .skeleton-grid-container {
    grid-template-columns: 1fr !important;
  }
  
  .skeleton-grid-image {
    height: 4rem;
  }
  
  .skeleton-avatar {
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 480px) {
  .skeleton-title {
    height: 1.25rem;
  }
  
  .skeleton-content {
    height: 0.875rem;
  }
  
  .skeleton-button {
    height: 2rem;
  }
  
  .skeleton-image {
    height: 6rem;
  }
}

/* Loading state transitions */
.skeleton-loader-wrapper {
  transition: opacity 0.3s ease-in-out;
}

.skeleton-loader-wrapper.fade-out {
  opacity: 0;
}

/* Focus management for accessibility */
.skeleton-loader-wrapper:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Screen reader support */
.skeleton-loader-wrapper[aria-hidden="true"] {
  pointer-events: none;
}

/* Skeleton shimmer effect (optional enhanced animation) */
.skeleton-shimmer {
  position: relative;
  overflow: hidden;
}

.skeleton-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: skeleton-shimmer 2s infinite;
}

@keyframes skeleton-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Dark mode shimmer adjustment */
.ion-palette-dark .skeleton-shimmer::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}
