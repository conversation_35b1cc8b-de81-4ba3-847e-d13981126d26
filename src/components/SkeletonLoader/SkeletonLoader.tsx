/**
 * SkeletonLoader - Reusable Skeleton Loading Component
 * Flexible skeleton component using IonSkeletonText that adapts to any page Layout
 * Follows AttendanceCard styling standards and supports dark mode
 */

import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonSkeletonText,
  IonItem,
  IonAvatar,
  IonLabel
} from '@ionic/react';
import { SkeletonLoaderProps, SkeletonElement } from '../../types/loading.types';
import './SkeletonLoader.css';

const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  lines = 3,
  width = '100%',
  height = 'auto',
  animated = true,
  className = '',
  style = {},
  variant = 'text',
  cardConfig = {},
  listConfig = {},
  gridConfig = {},
  customPattern = []
}) => {
  const [isVisible, setIsVisible] = React.useState(true);
  const [shouldRender, setShouldRender] = React.useState(true);

  // Handle fade-out transition
  const handleFadeOut = React.useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      setShouldRender(false);
    }, 300); // Match CSS animation duration
  }, []);

  const componentRef = React.useRef<{ fadeOut: () => void }>(null);

  React.useImperativeHandle(componentRef, () => ({
    fadeOut: handleFadeOut
  }), [handleFadeOut]);

  if (!shouldRender) return null;
  /**
   * Render text skeleton pattern
   */
  const renderTextSkeleton = () => (
    <div className={`skeleton-text-container ${className}`} style={style}>
      {Array.from({ length: lines }).map((_, index) => (
        <IonSkeletonText
          key={index}
          animated={animated}
          className={`skeleton-line mb-3 ${index === lines - 1 ? 'w-3/4' : 'w-full'}`}
          style={{
            width: index === lines - 1 ? '75%' : width,
            height: typeof height === 'number' ? `${height}px` : height
          }}
        />
      ))}
    </div>
  );

  /**
   * Render card skeleton pattern following AttendanceCard standards
   */
  const renderCardSkeleton = () => {
    const {
      hasHeader = true,
      hasImage = false,
      hasContent = true,
      hasActions = false
    } = cardConfig;

    return (
      <IonCard className={`card-base skeleton-card ${className}`} style={style}>
        {hasHeader && (
          <IonCardHeader className="card-header-unified">
            <div className="flex items-center justify-between mb-4">
              <IonSkeletonText
                animated={animated}
                className="skeleton-title h-6 w-48 rounded"
              />
              <IonSkeletonText
                animated={animated}
                className="skeleton-icon h-8 w-8 rounded-full"
              />
            </div>
            {hasImage && (
              <IonSkeletonText
                animated={animated}
                className="skeleton-image h-32 w-full rounded-xl mb-4"
              />
            )}
          </IonCardHeader>
        )}

        {hasContent && (
          <IonCardContent className="card-content-unified">
            <div className="space-y-3">
              <IonSkeletonText
                animated={animated}
                className="skeleton-content h-4 w-full rounded"
              />
              <IonSkeletonText
                animated={animated}
                className="skeleton-content h-4 w-5/6 rounded"
              />
              <IonSkeletonText
                animated={animated}
                className="skeleton-content h-4 w-3/4 rounded"
              />
            </div>

            {hasActions && (
              <div className="flex gap-3 mt-6">
                <IonSkeletonText
                  animated={animated}
                  className="skeleton-button h-10 w-24 rounded-lg"
                />
                <IonSkeletonText
                  animated={animated}
                  className="skeleton-button h-10 w-20 rounded-lg"
                />
              </div>
            )}
          </IonCardContent>
        )}
      </IonCard>
    );
  };

  /**
   * Render list skeleton pattern
   */
  const renderListSkeleton = () => {
    const {
      items = 3,
      hasAvatar = true,
      hasSecondaryText = true
    } = listConfig;

    return (
      <div className={`skeleton-list-container ${className}`} style={style}>
        {Array.from({ length: items }).map((_, index) => (
          <IonItem key={index} className="skeleton-list-item">
            {hasAvatar && (
              <IonAvatar slot="start" className="skeleton-avatar">
                <IonSkeletonText animated={animated} className="h-full w-full" />
              </IonAvatar>
            )}
            <IonLabel>
              <IonSkeletonText
                animated={animated}
                className="skeleton-primary-text h-5 w-3/4 rounded mb-2"
              />
              {hasSecondaryText && (
                <IonSkeletonText
                  animated={animated}
                  className="skeleton-secondary-text h-4 w-1/2 rounded"
                />
              )}
            </IonLabel>
          </IonItem>
        ))}
      </div>
    );
  };

  /**
   * Render grid skeleton pattern
   */
  const renderGridSkeleton = () => {
    const {
      columns = 2,
      rows = 2,
      gap = '1rem'
    } = gridConfig;

    const totalItems = columns * rows;

    return (
      <div
        className={`skeleton-grid-container ${className}`}
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${columns}, 1fr)`,
          gap,
          ...style
        }}
      >
        {Array.from({ length: totalItems }).map((_, index) => (
          <div key={index} className="skeleton-grid-item">
            <IonSkeletonText
              animated={animated}
              className="skeleton-grid-image h-24 w-full rounded-lg mb-2"
            />
            <IonSkeletonText
              animated={animated}
              className="skeleton-grid-title h-4 w-full rounded mb-1"
            />
            <IonSkeletonText
              animated={animated}
              className="skeleton-grid-subtitle h-3 w-3/4 rounded"
            />
          </div>
        ))}
      </div>
    );
  };

  /**
   * Render custom skeleton pattern
   */
  const renderCustomSkeleton = () => (
    <div className={`skeleton-custom-container ${className}`} style={style}>
      {customPattern.map((element, index) => (
        <IonSkeletonText
          key={index}
          animated={animated}
          className={`skeleton-custom-element ${element.className || ''}`}
          style={{
            width: element.width,
            height: element.height,
            borderRadius: element.type === 'circle' ? '50%' : 
                          element.type === 'rect' ? '0.375rem' : '0.25rem',
            ...element.style
          }}
        />
      ))}
    </div>
  );

  /**
   * Render skeleton based on variant
   */
  const renderSkeleton = () => {
    switch (variant) {
      case 'card':
        return renderCardSkeleton();
      case 'list':
        return renderListSkeleton();
      case 'grid':
        return renderGridSkeleton();
      case 'custom':
        return renderCustomSkeleton();
      case 'text':
      default:
        return renderTextSkeleton();
    }
  };

  return (
    <div
      className={`skeleton-loader-wrapper ${!isVisible ? 'fade-out' : ''} ${className}`}
      style={style}
      role="status"
      aria-label="Cargando contenido"
      aria-live="polite"
    >
      {renderSkeleton()}
    </div>
  );
};

export default SkeletonLoader;
