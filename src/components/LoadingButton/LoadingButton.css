/* LoadingButton - Professional Loading <PERSON><PERSON> Styles */

/* Base loading button */
.loading-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Loading state */
.loading-button.is-loading {
  pointer-events: none;
}

/* Button content container */
.loading-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-button-content.loading {
  opacity: 0.8;
}

/* Loading spinner */
.loading-button-spinner {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  animation: loading-button-spin 1s linear infinite;
}

/* Button text */
.loading-button-text {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-button.is-loading .loading-button-text {
  opacity: 0.7;
}

/* Button icon */
.loading-button-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-button-icon-only {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Progress ring container */
.loading-button-progress-ring {
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  pointer-events: none;
  border-radius: inherit;
  overflow: hidden;
}

/* Progress ring SVG */
.progress-ring {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

/* Progress ring background */
.progress-ring-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.2);
  stroke-width: 2;
}

/* Progress ring progress */
.progress-ring-progress {
  fill: none;
  stroke: rgba(255, 255, 255, 0.8);
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Progress text */
.loading-button-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  pointer-events: none;
  z-index: 1;
}

/* Size variations */
.loading-button[size="small"] .loading-button-spinner {
  width: 14px;
  height: 14px;
}

.loading-button[size="small"] .loading-button-icon {
  width: 16px;
  height: 16px;
}

.loading-button[size="large"] .loading-button-spinner {
  width: 20px;
  height: 20px;
}

.loading-button[size="large"] .loading-button-icon {
  width: 22px;
  height: 22px;
}

/* Fill variations */
.loading-button[fill="outline"] .progress-ring-bg {
  stroke: var(--ion-color-primary-tint);
}

.loading-button[fill="outline"] .progress-ring-progress {
  stroke: var(--ion-color-primary);
}

.loading-button[fill="outline"] .loading-button-progress-text {
  color: var(--ion-color-primary);
}

.loading-button[fill="clear"] .progress-ring-bg {
  stroke: var(--ion-color-primary-tint);
}

.loading-button[fill="clear"] .progress-ring-progress {
  stroke: var(--ion-color-primary);
}

.loading-button[fill="clear"] .loading-button-progress-text {
  color: var(--ion-color-primary);
}

/* Color variations */
.loading-button[color="secondary"] .progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
}

.loading-button[color="tertiary"] .progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
}

.loading-button[color="success"] .progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
}

.loading-button[color="warning"] .progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
}

.loading-button[color="danger"] .progress-ring-progress {
  stroke: rgba(255, 255, 255, 0.8);
}

/* Dark mode adjustments */
.ion-palette-dark .loading-button[fill="outline"] .loading-button-progress-text {
  color: var(--ion-color-primary-tint);
}

.ion-palette-dark .loading-button[fill="clear"] .loading-button-progress-text {
  color: var(--ion-color-primary-tint);
}

/* Animations */
@keyframes loading-button-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects (when not loading) */
.loading-button:not(.is-loading):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.loading-button:not(.is-loading):active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Focus states */
.loading-button:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Disabled state */
.loading-button:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .loading-button {
    transition: none;
  }
  
  .loading-button-content {
    transition: none;
  }
  
  .loading-button-spinner {
    animation-duration: 2s;
  }
  
  .loading-button:not(.is-loading):hover {
    transform: none;
  }
  
  .loading-button:not(.is-loading):active {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .progress-ring-bg {
    stroke: var(--ion-text-color);
    opacity: 0.3;
  }
  
  .progress-ring-progress {
    stroke: var(--ion-color-primary);
  }
  
  .loading-button-progress-text {
    color: var(--ion-text-color);
    font-weight: 700;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .loading-button-progress-text {
    font-size: 0.6875rem;
  }
}

/* Performance optimizations */
.loading-button {
  will-change: transform, box-shadow;
}

.loading-button-spinner {
  will-change: transform;
}

.progress-ring-progress {
  will-change: stroke-dasharray;
}
