/**
 * Loading<PERSON>utton - Enhanced <PERSON><PERSON> with Loading States
 * Professional loading button with smooth transitions and accessibility
 */

import React from 'react';
import { IonButton, IonSpinner, IonIcon } from '@ionic/react';
import { LoadingAnimation } from '../../types/loading.types';
import './LoadingButton.css';

interface LoadingButtonProps {
  // Button props
  children: React.ReactNode;
  onClick?: (event: React.MouseEvent) => void | Promise<void>;
  disabled?: boolean;
  fill?: 'clear' | 'outline' | 'solid';
  size?: 'small' | 'default' | 'large';
  expand?: 'full' | 'block';
  color?: string;
  className?: string;
  
  // Loading props
  loading?: boolean;
  loadingText?: string;
  spinner?: LoadingAnimation;
  showProgress?: boolean;
  progress?: number;
  
  // Icon props
  icon?: string;
  iconSlot?: 'start' | 'end' | 'icon-only';
  
  // Accessibility
  ariaLabel?: string;
  ariaDescribedBy?: string;
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  children,
  onClick,
  disabled = false,
  fill = 'solid',
  size = 'default',
  expand,
  color = 'primary',
  className = '',
  loading = false,
  loadingText,
  spinner = 'crescent',
  showProgress = false,
  progress = 0,
  icon,
  iconSlot = 'start',
  ariaLabel,
  ariaDescribedBy
}) => {
  const [internalLoading, setInternalLoading] = React.useState(false);
  const [asyncProgress, setAsyncProgress] = React.useState(0);

  const isLoading = loading || internalLoading;
  const isDisabled = disabled || isLoading;

  const handleClick = async (event: React.MouseEvent) => {
    if (isDisabled || !onClick) return;

    // If onClick returns a promise, show loading state
    try {
      const result = onClick(event);
      
      if (result instanceof Promise) {
        setInternalLoading(true);
        setAsyncProgress(0);
        
        // Simulate progress for better UX
        const progressInterval = setInterval(() => {
          setAsyncProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + Math.random() * 10;
          });
        }, 100);
        
        await result;
        
        clearInterval(progressInterval);
        setAsyncProgress(100);
        
        // Brief delay to show completion
        setTimeout(() => {
          setInternalLoading(false);
          setAsyncProgress(0);
        }, 300);
      }
    } catch (error) {
      setInternalLoading(false);
      setAsyncProgress(0);
      throw error;
    }
  };

  const currentProgress = showProgress ? progress : asyncProgress;
  const displayText = isLoading && loadingText ? loadingText : children;

  return (
    <IonButton
      fill={fill}
      size={size}
      expand={expand}
      color={color}
      disabled={isDisabled}
      onClick={handleClick}
      className={`loading-button ${isLoading ? 'is-loading' : ''} ${className}`}
      aria-label={ariaLabel || (typeof children === 'string' ? children : undefined)}
      aria-describedby={ariaDescribedBy}
      aria-busy={isLoading}
    >
      {/* Progress ring background */}
      {isLoading && showProgress && (
        <div className="loading-button-progress-ring">
          <svg viewBox="0 0 36 36" className="progress-ring">
            <path
              className="progress-ring-bg"
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
            <path
              className="progress-ring-progress"
              strokeDasharray={`${currentProgress}, 100`}
              d="M18 2.0845
                a 15.9155 15.9155 0 0 1 0 31.831
                a 15.9155 15.9155 0 0 1 0 -31.831"
            />
          </svg>
        </div>
      )}

      {/* Button content */}
      <div className={`loading-button-content ${isLoading ? 'loading' : ''}`}>
        {/* Loading spinner */}
        {isLoading && (
          <IonSpinner 
            name={spinner} 
            className="loading-button-spinner"
            color={color}
          />
        )}
        
        {/* Icon (when not loading) */}
        {!isLoading && icon && iconSlot !== 'icon-only' && (
          <IonIcon 
            icon={icon} 
            slot={iconSlot}
            className="loading-button-icon"
          />
        )}
        
        {/* Icon only mode */}
        {!isLoading && icon && iconSlot === 'icon-only' && (
          <IonIcon 
            icon={icon} 
            className="loading-button-icon-only"
          />
        )}
        
        {/* Button text */}
        {iconSlot !== 'icon-only' && (
          <span className="loading-button-text">
            {displayText}
          </span>
        )}
      </div>

      {/* Progress percentage */}
      {isLoading && showProgress && currentProgress > 0 && (
        <span className="loading-button-progress-text">
          {Math.round(currentProgress)}%
        </span>
      )}
    </IonButton>
  );
};

export default LoadingButton;
