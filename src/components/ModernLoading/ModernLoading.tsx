/**
 * ModernLoading - Glass-like Loading Overlay Component
 * Modern loading overlay with glass-like translucent appearance
 * Matches the app's established design patterns with backdrop-filter blur effects
 */

import React from 'react';
import { IonSpinner, IonText } from '@ionic/react';
import { LoadingAnimation } from '../../types/loading.types';
import './ModernLoading.css';

interface ModernLoadingProps {
  isOpen: boolean;
  message?: string;
  spinner?: LoadingAnimation;
  showBackdrop?: boolean;
  backdropDismiss?: boolean;
  duration?: number;
  onDidDismiss?: () => void;
  className?: string;
  showProgress?: boolean;
  progress?: number;
}

const ModernLoading: React.FC<ModernLoadingProps> = ({
  isOpen,
  message = 'Cargando...',
  spinner = 'crescent',
  showBackdrop = true,
  backdropDismiss = false,
  duration = 0,
  onDidDismiss,
  className = '',
  showProgress = false,
  progress = 0
}) => {
  React.useEffect(() => {
    if (isOpen && duration > 0) {
      const timer = setTimeout(() => {
        onDidDismiss?.();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [isOpen, duration, onDidDismiss]);

  React.useEffect(() => {
    // Prevent body scroll when loading is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleBackdropClick = () => {
    if (backdropDismiss) {
      onDidDismiss?.();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className={`modern-loading-overlay ${className}`}
      role="dialog"
      aria-modal="true"
      aria-labelledby="loading-message"
      aria-live="polite"
    >
      {/* Glass-like backdrop */}
      {showBackdrop && (
        <div 
          className="modern-loading-backdrop"
          onClick={handleBackdropClick}
          aria-hidden="true"
        />
      )}
      
      {/* Loading content container */}
      <div className="modern-loading-container">
        <div className="modern-loading-content">
          {/* Spinner */}
          <div className="modern-loading-spinner-container">
            <IonSpinner 
              name={spinner} 
              className="modern-loading-spinner"
              color="primary"
            />
            
            {/* Progress ring overlay */}
            {showProgress && (
              <div className="modern-loading-progress-ring">
                <svg className="progress-ring-svg" viewBox="0 0 36 36">
                  <path
                    className="progress-ring-bg"
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="progress-ring-progress"
                    strokeDasharray={`${progress}, 100`}
                    d="M18 2.0845
                      a 15.9155 15.9155 0 0 1 0 31.831
                      a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
              </div>
            )}
          </div>
          
          {/* Loading message */}
          {message && (
            <IonText 
              id="loading-message"
              className="modern-loading-message"
            >
              {message}
            </IonText>
          )}
          
          {/* Progress percentage */}
          {showProgress && (
            <IonText className="modern-loading-progress-text">
              {Math.round(progress)}%
            </IonText>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernLoading;
