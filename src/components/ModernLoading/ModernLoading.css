/* ModernLoading - Glass-like Loading Overlay Styles */

/* Main overlay container */
.modern-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 20000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  animation: modern-loading-fade-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Glass-like backdrop with blur effect */
.modern-loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Dark mode backdrop adjustments */
.ion-palette-dark .modern-loading-backdrop {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(24px) saturate(160%);
  -webkit-backdrop-filter: blur(24px) saturate(160%);
}

/* Loading content container */
.modern-loading-container {
  position: relative;
  z-index: 1;
  pointer-events: none;
}

/* Glass-like loading content */
.modern-loading-content {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  
  padding: 2rem 1.5rem;
  min-width: 140px;
  max-width: 280px;
  
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  
  transform: scale(0.9);
  animation: modern-loading-scale-in 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

/* Dark mode content adjustments */
.ion-palette-dark .modern-loading-content {
  background: rgba(28, 28, 30, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Spinner container with enhanced styling */
.modern-loading-spinner-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

/* Modern spinner styling */
.modern-loading-spinner {
  width: 32px;
  height: 32px;
  --color: var(--ion-color-primary);
  animation: modern-spinner-rotate 1.2s linear infinite;
}

/* Progress ring container */
.modern-loading-progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 48px;
  height: 48px;
  transform: rotate(-90deg);
}

/* Progress ring SVG */
.progress-ring-svg {
  width: 100%;
  height: 100%;
}

/* Progress ring background */
.progress-ring-bg {
  fill: none;
  stroke: var(--ion-color-light);
  stroke-width: 2;
  opacity: 0.3;
}

/* Progress ring progress */
.progress-ring-progress {
  fill: none;
  stroke: var(--ion-color-primary);
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Loading message styling */
.modern-loading-message {
  color: var(--ion-text-color);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.4;
  margin: 0;
  opacity: 0.9;
}

/* Progress text styling */
.modern-loading-progress-text {
  color: var(--ion-color-primary);
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  margin: 0;
}

/* Animations */
@keyframes modern-loading-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modern-loading-scale-in {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes modern-spinner-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modern-loading-content {
    padding: 1.5rem 1.25rem;
    min-width: 120px;
    max-width: 240px;
  }
  
  .modern-loading-spinner-container {
    width: 40px;
    height: 40px;
  }
  
  .modern-loading-spinner {
    width: 28px;
    height: 28px;
  }
  
  .modern-loading-progress-ring {
    width: 40px;
    height: 40px;
  }
  
  .modern-loading-message {
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .modern-loading-content {
    padding: 1.25rem 1rem;
    min-width: 100px;
    max-width: 200px;
  }
  
  .modern-loading-backdrop {
    backdrop-filter: blur(16px) saturate(160%);
    -webkit-backdrop-filter: blur(16px) saturate(160%);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .modern-loading-overlay {
    animation: none;
  }
  
  .modern-loading-content {
    animation: none;
    transform: scale(1);
  }
  
  .modern-loading-spinner {
    animation: modern-spinner-rotate 2s linear infinite;
  }
  
  .modern-loading-backdrop {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-loading-content {
    background: var(--ion-background-color);
    border: 2px solid var(--ion-text-color);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
  
  .modern-loading-backdrop {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .progress-ring-bg {
    stroke: var(--ion-text-color);
    opacity: 0.5;
  }
}

/* Focus management for accessibility */
.modern-loading-overlay:focus {
  outline: none;
}

.modern-loading-content:focus-within {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Performance optimizations */
.modern-loading-overlay {
  will-change: opacity;
}

.modern-loading-content {
  will-change: transform, opacity;
}

.modern-loading-spinner {
  will-change: transform;
}
