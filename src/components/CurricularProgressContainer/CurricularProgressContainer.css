/* Curricular Progress Container - Unified Design System Following AttendanceCard */

/* Essential Ionic variables that must stay as CSS custom properties */
.curricular-progress-container {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: none;
}

.container-header {
  padding: 1rem 1rem 0 1rem;
  --background: transparent;
  --color: inherit;
}

.container-content {
  padding: 0 1rem 1rem 1rem;
  --background: transparent;
  --color: inherit;
}

.container-title {
  color: var(--ion-text-color);
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.21;
  margin: 0;
}

.info-button {
  --color: var(--ion-color-medium);
  --color-hover: var(--ion-color-primary);
  margin: 0;
  padding: 0;
}

.cards-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Loading skeleton styles - Keep for loading states */
.skeleton-title, .skeleton-icon, .skeleton-card {
  background: var(--ion-color-light);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .skeleton-title,
  .skeleton-icon,
  .skeleton-card {
    animation: none;
  }
}
