import React from 'react';
import { IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonButton, IonIcon } from '@ionic/react';
import { informationCircleOutline } from 'ionicons/icons';
import CurricularProgressCard from '../CurricularProgressCard/CurricularProgressCard';
import './CurricularProgressContainer.css';

interface ProgressData {
  subject: string;
  experience: string;
  sequence: string;
  completedPercentage: number;
  inProgressPercentage: number;
}

interface CurricularProgressContainerProps {
  progressData: ProgressData[];
}

const CurricularProgressContainer: React.FC<CurricularProgressContainerProps> = ({
  progressData
}) => {

  return (
    <IonCard className="curricular-progress-container rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Contenedor de progreso curricular">
      <IonCardHeader className="container-header pb-0">
        <div className="flex items-center justify-between mb-4">
          <IonCardTitle className="container-title text-lg font-semibold m-0">
            Progreso curricular
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="info-button w-8 h-8"
            aria-label="Información sobre progreso curricular"
          >
            <IonIcon
              icon={informationCircleOutline}
              slot="icon-only"
            />
          </IonButton>
        </div>
      </IonCardHeader>

      <IonCardContent className="container-content pt-0">
        {/* Progress Cards */}
        <div className="cards-container space-y-6">
          {progressData.map((data, index) => (
            <CurricularProgressCard
              key={index}
              data={data}
              isLoading={false}
            />
          ))}
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default CurricularProgressContainer;
