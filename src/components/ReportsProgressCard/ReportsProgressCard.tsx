import React from 'react';
import { IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonButton, IonIcon } from '@ionic/react';
import { chevronForward, arrowForwardOutline, checkmarkCircleOutline, documentTextOutline } from 'ionicons/icons';
import './ReportsProgressCard.css';

interface ReportsProgressCardProps {
  subject: string;
  experience: string;
  sequenceNumber: string;
  progressData: {
    completed: number[];
    inProgress: number[];
    pending: number[];
  };
}

const ReportsProgressCard: React.FC<ReportsProgressCardProps> = ({
  subject,
  experience,
  sequenceNumber,
  progressData
}) => {
  return (
    <IonCard className="reports-progress-card mb-6 rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Tarjeta de progreso de informes">
      <IonCardHeader className="reports-header pb-0">
        <div className="flex items-center justify-between mb-4">
          <IonCardTitle className="reports-title text-sm font-bold m-0">
            {subject}
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="chevron-button w-8 h-8"
            aria-label="Ver detalles del progreso"
          >
            <IonIcon
              icon={chevronForward}
              slot="icon-only"
            />
          </IonButton>
        </div>

        {/* Experience Description */}
        <div className="mb-4">
          <p className="experience-description font-normal text-[10px] leading-[1.21] m-0">
            {experience}
          </p>
        </div>
      </IonCardHeader>

      <IonCardContent className="reports-content pt-0">
        {/* Progress Sequence */}
        <div className="progress-sequence flex flex-col gap-px">
          {/* Sequence Header */}
          <div className="sequence-header flex flex-col items-center gap-0.5 w-[266px] mx-auto">
            <span className="sequence-title font-normal text-[10px] leading-[1.21] text-center">{sequenceNumber}</span>
            <div className="sequence-divider w-0 h-[9px] border-l border-dashed"></div>
          </div>

          {/* Progress Visualization */}
          <div className="progress-visualization">
            {/* Progress Line Container */}
            <div className="progress-line-container">
              {/* Background Line */}
              <div className="progress-line-bg"></div>
              {/* Active Progress Line */}
              <div className="progress-line-active"></div>
              
              {/* Progress Points */}
              <div className="progress-points">
                {/* Current/Active Point */}
                <div className="progress-point active">
                  <div className="point-icon-container">
                    <IonIcon icon={arrowForwardOutline} />
                  </div>
                </div>

                {/* Completed Point */}
                <div className="progress-point completed">
                  <div className="point-icon-container">
                    <IonIcon icon={checkmarkCircleOutline} />
                  </div>
                </div>

                {/* Pending Points */}
                <div className="progress-point pending">
                  <div className="point-dot"></div>
                </div>
                <div className="progress-point pending">
                  <div className="point-dot"></div>
                </div>
                <div className="progress-point pending">
                  <div className="point-dot"></div>
                </div>
                <div className="progress-point pending">
                  <div className="point-dot"></div>
                </div>
              </div>
            </div>

            {/* Progress Statistics */}
            <div className="progress-stats">
              {/* Completed Stats */}
              <div className="stat-item">
                <span className="stat-percentage">60%</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
              <div className="stat-item">
                <span className="stat-percentage">30%</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
              <div className="stat-item">
                <span className="stat-percentage">80%</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
              
              {/* Pending Stats */}
              <div className="stat-item pending">
                <span className="stat-percentage">-</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
              <div className="stat-item pending">
                <span className="stat-percentage">-</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
              <div className="stat-item pending">
                <span className="stat-percentage">-</span>
                <IonIcon icon={documentTextOutline} className="stat-icon" />
              </div>
            </div>
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default ReportsProgressCard;
