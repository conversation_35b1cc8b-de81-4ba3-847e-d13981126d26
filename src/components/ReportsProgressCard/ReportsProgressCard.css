/* ReportsProgressCard.css - Unified Design System Following AttendanceCard */
.reports-progress-card {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: none;
  margin: 0;
}

.reports-header {
  padding: 1rem 1rem 0 1rem;
}

.reports-content {
  padding: 0 1rem 1rem 1rem;
}

.reports-title {
  color: var(--ion-text-color);
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 1.21;
}

.experience-description {
  color: var(--ion-color-medium);
  font-size: 0.625rem;
  line-height: 1.21;
}

.sequence-title {
  color: var(--ion-color-medium);
  font-size: 0.625rem;
  line-height: 1.21;
}

.sequence-divider {
  border-color: var(--ion-color-primary);
  opacity: 0.5;
}

.chevron-button {
  --color: var(--ion-color-primary);
  --color-hover: var(--ion-color-primary-shade);
  margin: 0;
  padding: 0;
}

.chevron-button ion-icon {
  font-size: 1rem;
}

/* Progress Visualization - Exact Figma Implementation */
.progress-visualization {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 313px;
  margin: 0 auto;
}

.progress-line-container {
  position: relative;
  height: 60px;
  display: flex;
  align-items: center;
}

/* Background progress lines - Exact Figma positioning */
.progress-line-bg {
  position: absolute;
  top: 50%;
  left: 124.99px;
  width: 179.74px;
  height: 1px;
  background: #D9D9D9;
  transform: translateY(-50%);
}

.progress-line-active {
  position: absolute;
  top: 50%;
  left: 9.3px;
  width: 115.7px;
  height: 3px;
  background: rgba(69, 178, 255, 0.5);
  transform: translateY(-50%);
}

.progress-points {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 1;
  gap: 45px;
  justify-content: flex-start;
}

.progress-point {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* Active/Current Point with Arrow - Exact Figma positioning */
.progress-point.active {
  position: relative;
}

.progress-point.active .point-icon-container {
  width: 24px;
  height: 24px;
  background: #FFFFFF;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  padding: 4px;
  position: relative;
  z-index: 3;
}



/* Blue indicator dots - Exact Figma positioning */
.progress-point.active::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: #45B2FF;
  border-radius: 50%;
  top: -12px;
  left: 8px;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.progress-point.active::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 14px;
  background: #45B2FF;
  border-radius: 50%;
  top: -18px;
  right: -8px;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* Completed Point with Checkmark */
.progress-point.completed .point-icon-container {
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
  padding: 4px;
}

.progress-point.completed .point-icon-container ion-icon {
  color: #D9D9D9;
  font-size: 16px;
}

/* Pending points - small dots */
.progress-point.pending .point-dot {
  width: 8px;
  height: 8px;
  background: #D9D9D9;
  border-radius: 50%;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
}

/* Progress Statistics - Exact Figma Implementation */
.progress-stats {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 41px;
  margin-top: 8px;
  padding-left: 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  width: 17.39px;
  min-width: 17.39px;
  position: relative;
}

.stat-percentage {
  font-weight: 400;
  font-size: 8px;
  line-height: 1.21;
  text-align: center;
  color: #4E4E4E;
  white-space: nowrap;
  margin-bottom: 2px;
}

.stat-item.pending .stat-percentage {
  color: #D9D9D9;
}

.stat-icon {
  width: 17.39px;
  height: 16px;
  color: #4E4E4E;
  flex-shrink: 0;
}

.stat-item.pending .stat-icon {
  color: #D9D9D9;
}

/* Exact positioning to align with progress points */
.progress-stats .stat-item:first-child {
  margin-left: 3px; /* Align with first progress point */
}

.progress-stats .stat-item:nth-child(2) {
  margin-left: -3px; /* Fine-tune alignment */
}

.progress-stats .stat-item:nth-child(3) {
  margin-left: -3px; /* Fine-tune alignment */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .progress-visualization {
    width: 100%;
    max-width: 313px;
  }

  .sequence-header {
    width: 100%;
    max-width: 266px;
  }

  .progress-stats {
    gap: 20px;
  }

  .card-content {
    padding: 12px;
  }

  .subject-title {
    font-size: 13px;
  }

  .experience-description {
    font-size: 9px;
  }
}

/* Mobile-first responsive design */
@media (max-width: 480px) {
  .card-content {
    padding: 10px;
    gap: 12px;
  }

  .subject-header {
    gap: 10px;
  }

  .subject-title {
    font-size: 12px;
  }

  .experience-description {
    font-size: 8px;
    line-height: 1.3;
  }

  .progress-visualization {
    width: 100%;
    max-width: 280px;
  }

  .sequence-header {
    width: 100%;
    max-width: 240px;
  }

  .progress-stats {
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .stat-item {
    width: 15px;
    min-width: 15px;
  }

  .stat-percentage {
    font-size: 7px;
  }

  .stat-icon {
    width: 15px;
    height: 14px;
  }

  .progress-points {
    gap: 30px;
  }

  .progress-point.active .point-icon-container,
  .progress-point.completed .point-icon-container {
    width: 20px;
    height: 20px;
  }

  .progress-point.active .point-icon-container ion-icon,
  .progress-point.completed .point-icon-container ion-icon {
    font-size: 14px;
  }
}

/* Large screens optimization */
@media (min-width: 1024px) {
  .card-content {
    padding: 20px;
    gap: 18px;
  }

  .subject-title {
    font-size: 16px;
  }

  .experience-description {
    font-size: 11px;
  }

  .progress-stats {
    gap: 50px;
  }
}

/* iOS and Android specific optimizations */
@supports (-webkit-touch-callout: none) {
  /* iOS specific styles */
  .chevron-button {
    -webkit-tap-highlight-color: transparent;
  }

  .reports-progress-card {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }
}

/* Android specific optimizations */
@media (pointer: coarse) {
  .chevron-button {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .reports-progress-card {
    border: 2px solid var(--ion-text-color);
  }

  .progress-line-bg,
  .progress-line-active {
    border: 1px solid currentColor;
  }

  .progress-point.active .point-icon-container,
  .progress-point.completed .point-icon-container {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .reports-progress-card {
    transition: none;
  }

  .progress-point.active::before,
  .progress-point.active::after {
    animation: none;
  }
}

/* Dark mode support - Automatic adaptation through CSS variables */
/* No explicit dark mode rules needed - CSS variables handle adaptation automatically */

/* Progress visualization styling */
.stat-percentage {
  color: var(--ion-text-color);
  font-weight: 600;
}

.stat-icon {
  color: var(--ion-text-color);
}

.stat-item.pending .stat-percentage,
.stat-item.pending .stat-icon {
  color: var(--ion-color-medium);
  opacity: 0.6;
}

.progress-line-bg {
  background: var(--ion-color-step-200);
}

.progress-point.completed .point-icon-container {
  background: var(--ion-color-success);
  color: white;
}

.progress-point.active .point-icon-container {
  background: var(--ion-color-primary);
  color: white;
}

.progress-point.pending .point-dot {
  background: var(--ion-color-medium);
  opacity: 0.4;
}
