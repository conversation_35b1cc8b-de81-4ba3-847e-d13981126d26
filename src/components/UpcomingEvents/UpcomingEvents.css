/* Upcoming Events Card */
.upcoming-events-card {
  margin: 0;
  --background: var(--ion-card-background);
  border-radius: 16px;
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  border: 0.5px solid rgba(217, 217, 217, 0.2);
  background: var(--ion-card-background);
}

.upcoming-events-content {
  padding: 16px;
  --padding-start: 0;
  --padding-end: 0;
  --padding-top: 0;
  --padding-bottom: 0;
}

/* Header Section */
.upcoming-events-header {
  margin-bottom: 16px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.period-chip {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: space-between;
}

.period-chip-inner {
  --background: transparent;
  --color: var(--ion-color-primary);
  border: 1px solid var(--ion-color-primary);
  border-radius: 24px;
  height: auto;
  padding: 4px 12px;
  margin: 0;
}

.period-text {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-color-primary);
}

.month-text {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
  text-align: right;
}

.section-title h2 {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 21px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

/* Summary Section */
.events-summary {
  margin-bottom: 16px;
  position: relative;
}

.events-summary::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  right: 0;
  height: 0.5px;
  background: var(--ion-color-light-shade);
}

.summary-chips {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.summary-chip {
  --background: #FC71A9;
  --color: white;
  border-radius: 24px;
  height: auto;
  padding: 4px 12px;
  margin: 0;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
}

.summary-chip ion-text {
  color: white;
  font-size: 10px;
  font-weight: 400;
}

.tareas-chip {
  --background: #FC71A9;
}

.evaluaciones-chip {
  --background: #8324CC;
}

.eventos-chip {
  --background: #245CCC;
}

/* Events List */
.events-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.event-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.event-avatar {
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--ion-color-medium);
}

.avatar-icon {
  color: white;
  font-size: 14px;
}

.event-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.event-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.event-title h3 {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

.event-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.event-date,
.event-type,
.event-subject {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
}

.meta-separator {
  color: var(--ion-color-primary);
  font-size: 10px;
  font-weight: bold;
}

.event-description {
  margin-top: 4px;
}

.description-text {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-text-color);
  display: block;
}

.event-separator {
  height: 0.5px;
  background: var(--ion-color-light-shade);
  margin: 8px 0;
  border-style: dashed;
  border-width: 0.5px 0 0 0;
  border-color: var(--ion-color-light-shade);
  background: none;
}

