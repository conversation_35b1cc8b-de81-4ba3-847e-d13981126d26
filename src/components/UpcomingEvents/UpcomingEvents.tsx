import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonText,
  IonChip,
  IonIcon,
  IonAvatar,
} from '@ionic/react';
import {
  calendarOutline,
  bookOutline,
  schoolOutline,
} from 'ionicons/icons';
import './UpcomingEvents.css';

interface Event {
  id: string;
  title: string;
  date: string;
  type: 'tarea' | 'evaluacion' | 'evento';
  subject: string;
  description: string;
  color: string;
  icon: string;
}

const UpcomingEvents: React.FC = () => {
  // Mock data basado en el diseño de Figma
  const events: Event[] = [
    {
      id: '1',
      title: 'Desafío <PERSON>emá<PERSON>: Álgebra y Geometría',
      date: '12 de enero',
      type: 'tarea',
      subject: 'Matemáticas',
      description: 'Recuerda entregar tu tarea de matemáticas antes del [fecha]. Incluye ejercicios de álgebra, geometría y resolución de problemas. Asegúrate de mostrar todos los pasos de cada ejercicio.',
      color: '#FC71A9',
      icon: bookOutline
    },
    {
      id: '2',
      title: 'Explorando la Geometría: Figuras y Cálculos',
      date: '06 de enero',
      type: 'tarea',
      subject: 'Matemáticas',
      description: 'Resuelve problemas sobre figuras geométricas, áreas, volúmenes y propiedades.',
      color: '#FC71A9',
      icon: bookOutline
    },
    {
      id: '3',
      title: 'Evaluación: Los Ciclos de la Tierra',
      date: '04 de enero',
      type: 'evaluacion',
      subject: 'Ciencias naturales',
      description: 'Deberás demostrar tu comprensión de los ciclos naturales de la Tierra, incluyendo el ciclo del agua, el ciclo del carbono y los procesos de cambio estacional.',
      color: '#8324CC',
      icon: schoolOutline
    }
  ];

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'tarea':
        return 'Tareas';
      case 'evaluacion':
        return 'Evaluaciones';
      case 'evento':
        return 'Eventos escolares';
      default:
        return 'Evento';
    }
  };

  const getTypeCount = (type: string) => {
    const count = events.filter(event => event.type === type).length;
    return count > 0 ? `(${count})` : '';
  };

  return (
    <IonCard className="upcoming-events-card ">
      <IonCardContent className="upcoming-events-content">
        {/* Header Section */}
        <div className="upcoming-events-header">
          <div className="header-content">
            <div className="header-top">
              <div className="period-chip">
                <IonChip className="period-chip-inner">
                  <IonText className="period-text">Próximos 7 días hábiles</IonText>
                </IonChip>
                <IonText className="month-text">Enero 2024</IonText>
              </div>
            </div>
            <IonText className="section-title">
              <h2>Eventos próximos</h2>
            </IonText>
          </div>
        </div>

        {/* Summary Section */}
        <div className="events-summary">
          <div className="summary-chips">
            <IonChip className="summary-chip tareas-chip">
              <IonText>Tareas {getTypeCount('tarea')}</IonText>
            </IonChip>
            <IonChip className="summary-chip evaluaciones-chip">
              <IonText>Evaluaciones {getTypeCount('evaluacion')}</IonText>
            </IonChip>
            <IonChip className="summary-chip eventos-chip">
              <IonText>Eventos escolares {getTypeCount('evento')}</IonText>
            </IonChip>
          </div>
        </div>

        {/* Events List */}
        <div className="events-list">
          {events.map((event, index) => (
            <div key={event.id}>
              <div className="event-item">
                <IonAvatar className="event-avatar">
                  <div 
                    className="avatar-placeholder"
                    style={{ backgroundColor: event.color }}
                  >
                    <IonIcon icon={event.icon} className="avatar-icon" />
                  </div>
                </IonAvatar>
                
                <div className="event-content">
                  <div className="event-header">
                    <IonText className="event-title">
                      <h3>{event.title}</h3>
                    </IonText>
                    <div className="event-meta">
                      <IonText className="event-date">{event.date}</IonText>
                      <span className="meta-separator">•</span>
                      <IonText className="event-type">{getTypeLabel(event.type)}</IonText>
                      <span className="meta-separator">•</span>
                      <IonText className="event-subject">{event.subject}</IonText>
                    </div>
                  </div>
                  <div className="event-description">
                    <IonText className="description-text">
                      {event.description}
                    </IonText>
                  </div>
                </div>
              </div>
              
              {/* Separator line (except for last item) */}
              {index < events.length - 1 && (
                <div className="event-separator"></div>
              )}
            </div>
          ))}
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default UpcomingEvents;
