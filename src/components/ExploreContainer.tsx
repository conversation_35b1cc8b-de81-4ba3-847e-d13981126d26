interface ContainerProps {
  name: string;
}

const ExploreContainer: React.FC<ContainerProps> = ({ name }) => {
  return (
    <div className="text-center absolute inset-x-0 top-1/2 -translate-y-1/2">
      <strong className="text-xl leading-[26px] text-[var(--ion-text-color)]">{name}</strong>
      <p className="text-base leading-[22px] text-[var(--ion-color-medium)] m-0">
        Explore
        <a
          target="_blank"
          rel="noopener noreferrer"
          href="https://ionicframework.com/docs/components"
          className="no-underline text-[var(--ion-color-primary)]"
        >
          UI Components
        </a>
      </p>
      <div className='text-xl text-[var(--ion-color-danger)] font-bold'>TEXTO DE PRUEBA</div>
    </div>
  );
};

export default ExploreContainer;
