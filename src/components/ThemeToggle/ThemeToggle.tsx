import React from 'react';
import {
  IonItem,
  IonLabel,
  IonToggle,
  IonIcon,
  ToggleCustomEvent,
} from '@ionic/react';
import { moonOutline, sunnyOutline, contrastOutline } from 'ionicons/icons';
import { useTheme } from '../../contexts/ThemeContext';
import './ThemeToggle.css';

interface ThemeToggleProps {
  className?: string;
}

/**
 * Native Ionic Theme Toggle Component
 * Redesigned for seamless integration with Account page and modern Ionic patterns
 * Maintains all accessibility features and responsive design
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  const handleToggleChange = (event: ToggleCustomEvent) => {
    toggleTheme();
  };

  return (
    <IonItem
      className={`theme-toggle-item mb-2 rounded-xl transition-all duration-200 ease-in-out hover:translate-x-1 ${className}`}
      lines="none"
      button={false}
    >
      {/* Main Icon */}
      <IonIcon
        icon={contrastOutline}
        slot="start"
        className="theme-main-icon text-xl mr-4 transition-all duration-300 ease-in-out"
        aria-hidden="true"
      />

      {/* Label Content */}
      <IonLabel className="theme-label flex-1 min-w-0">
        <h3 className="theme-title text-base font-semibold m-0 mb-1 leading-tight">Tema de la aplicación</h3>
        <p className="theme-description text-sm m-0 leading-snug transition-colors duration-300 ease-in-out">
          {isDarkMode ? 'Modo oscuro activado' : 'Modo claro activado'}
        </p>
      </IonLabel>

      {/* Toggle Control with State Icons */}
      <div className="theme-toggle-control flex items-center flex-shrink-0" slot="end">
        <div className="theme-state-indicators flex items-center gap-2 py-1.5 px-3 rounded-full transition-all duration-300 ease-in-out border">
          <IonIcon
            icon={sunnyOutline}
            className={`theme-state-icon light text-base transition-all duration-300 ease-in-out opacity-60 ${!isDarkMode ? 'active opacity-100 scale-110' : ''}`}
            aria-hidden="true"
          />
          <IonToggle
            checked={isDarkMode}
            onIonChange={handleToggleChange}
            className="theme-toggle-switch m-0 transition-all duration-300 ease-in-out"
            aria-label="Alternar entre modo claro y oscuro"
            color="primary"
          />
          <IonIcon
            icon={moonOutline}
            className={`theme-state-icon dark text-base transition-all duration-300 ease-in-out opacity-60 ${isDarkMode ? 'active opacity-100 scale-110' : ''}`}
            aria-hidden="true"
          />
        </div>
      </div>
    </IonItem>
  );
};

export default ThemeToggle;
