/* Native Ionic Theme Toggle Component - Optimized */

/* Essential Ionic variables that must stay as CSS custom properties */
.theme-toggle-item {
  --background: transparent;
  --color: inherit;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  --border-style: none;
}

.theme-toggle-item:hover {
  --background: var(--ion-color-light);
}

/* Dark mode hover is handled automatically through CSS variables */

.theme-main-icon {
  color: var(--ion-color-primary);
}

.theme-toggle-item:hover .theme-main-icon {
  color: var(--ion-color-primary-shade);
  transform: scale(1.1);
}

.theme-label {
  --color: inherit;
}

.theme-title {
  color: var(--ion-text-color);
}

.theme-description {
  color: var(--ion-color-medium);
}

.theme-state-indicators {
  background: var(--ion-color-light);
  border: 1px solid var(--ion-color-light-shade);
}

/* Dark mode theme state indicators handled automatically through CSS variables */

.theme-state-icon {
  color: var(--ion-color-medium);
}

.theme-state-icon.light.active {
  color: #ff9800;
}

.theme-state-icon.dark.active {
  color: var(--ion-color-primary);
}

/* Dark mode theme state icons handled automatically through CSS variables */

/* Native Ionic Toggle - Essential variables only */
.theme-toggle-switch {
  --background: var(--ion-color-medium-tint);
  --background-checked: var(--ion-color-primary);
  --handle-background: white;
  --handle-background-checked: white;
  --handle-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  --handle-width: 20px;
  --handle-height: 20px;
  --track-width: 36px;
  --track-height: 22px;
}

/* Dark mode toggle switch is handled automatically through CSS variables */

/* Responsive Design */
@media (max-width: 768px) {
  .theme-main-icon {
    font-size: 1.125rem;
  }

  .theme-title {
    font-size: 0.9375rem;
  }

  .theme-description {
    font-size: 0.8125rem;
  }

  .theme-state-indicators {
    gap: 0.375rem;
    padding: 0.3125rem 0.625rem;
  }

  .theme-state-icon {
    font-size: 0.9375rem;
  }
}

@media (max-width: 480px) {
  .theme-toggle-item {
    --inner-padding-start: 0.75rem;
    --inner-padding-end: 0.75rem;
  }

  .theme-main-icon {
    font-size: 1rem;
    margin-right: 0.75rem;
  }

  .theme-title {
    font-size: 0.875rem;
  }

  .theme-description {
    font-size: 0.75rem;
  }

  .theme-state-indicators {
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
  }

  .theme-state-icon {
    font-size: 0.875rem;
  }

  .theme-toggle-switch {
    --handle-width: 18px;
    --handle-height: 18px;
    --track-width: 32px;
    --track-height: 20px;
  }
}

/* Accessibility & Focus Styles */
.theme-toggle-switch:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
  border-radius: 50px;
}

.theme-toggle-item:focus-within {
  --background: var(--ion-color-light-tint);
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Dark mode focus states handled automatically through CSS variables */

/* Touch Target Optimization */
.theme-toggle-item {
  min-height: 48px;
  padding: 0.75rem 0;
}

.theme-state-indicators {
  min-height: 32px;
  min-width: 80px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .theme-toggle-item {
    border: 1px solid var(--ion-color-medium);
  }

  .theme-toggle-item:hover,
  .theme-toggle-item:focus-within {
    border-color: var(--ion-color-primary);
    border-width: 2px;
  }

  .theme-state-indicators {
    border: 2px solid var(--ion-color-medium);
  }

  .theme-toggle-switch {
    --handle-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-item,
  .theme-main-icon,
  .theme-state-indicators,
  .theme-state-icon,
  .theme-toggle-switch,
  .theme-description {
    transition: none !important;
  }

  .theme-toggle-item:hover {
    transform: none;
  }

  .theme-toggle-item:hover .theme-main-icon,
  .theme-state-icon.active {
    transform: none;
  }
}

/* Smooth Theme Transition Animation */
@keyframes themeTransition {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.theme-main-icon {
  animation: themeTransition 0.3s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .theme-main-icon {
    animation: none;
  }
}

/* Integration with Account Page Settings */
.settings-list .theme-toggle-item {
  margin-bottom: 0.5rem;
}

.settings-card .theme-toggle-item {
  border-radius: 12px;
}

/* Dark mode enhancements handled automatically through CSS variables */
