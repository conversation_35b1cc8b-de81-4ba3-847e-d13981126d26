import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon
} from '@ionic/react';
import { chevronForward } from 'ionicons/icons';
import './CurricularProgressCard.css';

interface ProgressData {
  subject: string;
  experience: string;
  sequence: string;
  completedPercentage: number;
  inProgressPercentage: number;
}

interface CurricularProgressCardProps {
  data: ProgressData;
  isLoading: boolean;
}

const CurricularProgressCard: React.FC<CurricularProgressCardProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <IonCard className="curricular-progress-card mb-6 rounded-2xl shadow-lg transition-all duration-300 ease-in-out">
        <IonCardContent className="p-4">
          <div className="skeleton-title h-6 rounded mb-4 bg-[var(--ion-color-light)] animate-pulse"></div>
          <div className="skeleton-subtitle h-4 rounded mb-6 w-3/4 bg-[var(--ion-color-light)] animate-pulse"></div>
          <div className="skeleton-content h-20 rounded-xl mb-4 bg-[var(--ion-color-light)] animate-pulse"></div>
          <div className="flex gap-4">
            <div className="skeleton-stat h-8 rounded flex-1 bg-[var(--ion-color-light)] animate-pulse"></div>
            <div className="skeleton-stat h-8 rounded flex-1 bg-[var(--ion-color-light)] animate-pulse"></div>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  return (
    <IonCard className="curricular-progress-card mb-6 rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl" role="region" aria-label="Tarjeta de progreso curricular">
      <IonCardHeader className="curricular-header pb-0">
        <div className="flex items-center justify-between mb-4">
          <IonCardTitle className="curricular-title text-sm font-bold m-0">
            {data.subject}
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="chevron-button w-8 h-8"
            aria-label="Ver detalles del progreso curricular"
          >
            <IonIcon
              icon={chevronForward}
              slot="icon-only"
            />
          </IonButton>
        </div>

        {/* Experience Description */}
        <div className="mb-4">
          <p className="experience-description font-normal text-[10px] leading-[1.21] m-0">
            <span className="experience-label font-medium">Experiencia N°1:</span> {data.experience.replace(/^Experiencia N°1:\s*/, '')}
          </p>
        </div>
      </IonCardHeader>

      <IonCardContent className="curricular-content pt-0">

        {/* Sequence Section - Lineas de secuencia */}
        <div className="figma-sequence-section">
          {/* Progress Visualization - Group 3 */}
          <div className="figma-progress-visualization">
            {/* Sequence Label positioned over last active point */}
            <div className="figma-sequence-label">
              <span className="figma-sequence-title">{data.sequence}</span>
              <div className="figma-sequence-divider"></div>
            </div>

            {/* Background Lines */}
            <div className="figma-progress-lines">
              <div className="figma-line-bg"></div>
              <div className="figma-line-active"></div>
            </div>

            {/* Progress Points and Navigation */}
            <div className="figma-progress-points">
              {/* Navigation Arrows and Points */}
              <div className="figma-points-row">
                {/* Left Arrow */}
                <div className="figma-nav-arrow figma-arrow-left">
                  <div className="figma-arrow-container">
                    <img src="/assets/images/arrow-icon.svg" alt="Arrow" className="figma-arrow-icon" />
                  </div>
                </div>

                {/* Progress Points Container */}
                <div className="figma-points-container">
                  <div className="figma-point figma-point-completed"></div>
                  <div className="figma-point figma-point-completed-large"></div>
                  <div className="figma-point figma-point-pending"></div>
                  <div className="figma-point figma-point-pending"></div>
                </div>

                {/* Right Arrow with Check */}
                <div className="figma-nav-arrow figma-arrow-right">
                  <div className="figma-check-container">
                    <img src="/assets/images/check-icon.svg" alt="Check" className="figma-check-icon" />
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Row - Frame 49 */}
            <div className="figma-stats-container">
              <div className="figma-stats-row">
                {/* Stat aligned with left arrow */}
                <div className="figma-stat-item">
                  <span className="figma-stat-percentage">{data.completedPercentage}%</span>
                  <div className="figma-stat-icon">
                    <img src="/assets/images/checklist-icon.svg" alt="Checklist" className="figma-checklist-icon" />
                  </div>
                </div>

                {/* Stats aligned with points container */}
                <div className="figma-stats-points-container">
                  <div className="figma-stat-item">
                    <span className="figma-stat-percentage">{data.inProgressPercentage}%</span>
                    <div className="figma-stat-icon">
                      <svg width="17.39" height="16" viewBox="0 0 18 16" fill="none">
                        <path d="M3.62 2H14.49V14H3.62V2Z" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.87 2.02L14.48 2L14.5 5.34" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.15 10.33H11.6" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M6.52 10.1H7.97" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.15 7.67H11.6" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M6.52 7.43H7.97" stroke="#4E4E4E" strokeWidth="1"/>
                      </svg>
                    </div>
                  </div>

                  <div className="figma-stat-item">
                    <span className="figma-stat-percentage">80%</span>
                    <div className="figma-stat-icon">
                      <svg width="17.39" height="16" viewBox="0 0 18 16" fill="none">
                        <path d="M3.62 2H14.49V14H3.62V2Z" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.87 2.02L14.48 2L14.5 5.34" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.15 10.33H11.6" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M6.52 10.1H7.97" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M10.15 7.67H11.6" stroke="#4E4E4E" strokeWidth="1"/>
                        <path d="M6.52 7.43H7.97" stroke="#4E4E4E" strokeWidth="1"/>
                      </svg>
                    </div>
                  </div>

                  <div className="figma-stat-item">
                    <span className="figma-stat-percentage figma-stat-pending">-</span>
                    <div className="figma-stat-icon">
                      <svg width="17.39" height="16" viewBox="0 0 18 16" fill="none">
                        <path d="M3.62 2H14.49V14H3.62V2Z" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.87 2.02L14.48 2L14.5 5.34" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.15 10.33H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M6.52 10.1H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.15 7.67H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M6.52 7.43H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                      </svg>
                    </div>
                  </div>

                  <div className="figma-stat-item">
                    <span className="figma-stat-percentage figma-stat-pending">-</span>
                    <div className="figma-stat-icon">
                      <svg width="17.39" height="16" viewBox="0 0 18 16" fill="none">
                        <path d="M3.62 2H14.49V14H3.62V2Z" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.87 2.02L14.48 2L14.5 5.34" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.15 10.33H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M6.52 10.1H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M10.15 7.67H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                        <path d="M6.52 7.43H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Stat aligned with right arrow */}
                <div className="figma-stat-item">
                  <span className="figma-stat-percentage figma-stat-pending">-</span>
                  <div className="figma-stat-icon">
                    <svg width="17.39" height="16" viewBox="0 0 18 16" fill="none">
                      <path d="M3.62 2H14.49V14H3.62V2Z" stroke="#D9D9D9" strokeWidth="1"/>
                      <path d="M10.87 2.02L14.48 2L14.5 5.34" stroke="#D9D9D9" strokeWidth="1"/>
                      <path d="M10.15 10.33H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                      <path d="M6.52 10.1H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                      <path d="M10.15 7.67H11.6" stroke="#D9D9D9" strokeWidth="1"/>
                      <path d="M6.52 7.43H7.97" stroke="#D9D9D9" strokeWidth="1"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default CurricularProgressCard;
