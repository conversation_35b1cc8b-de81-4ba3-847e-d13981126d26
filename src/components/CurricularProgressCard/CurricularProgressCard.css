/* Curricular Progress Card - Unified Design System Following AttendanceCard */

/* Essential Ionic variables that must stay as CSS custom properties */
.curricular-progress-card {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border: none;
  margin: 0;
}

.curricular-header {
  padding: 1rem 1rem 0 1rem;
}

.curricular-content {
  padding: 0 1rem 1rem 1rem;
}

.curricular-title {
  color: var(--ion-text-color);
  font-size: 0.875rem;
  font-weight: 700;
  line-height: 1.3;
}

.experience-description {
  color: var(--ion-color-medium);
  font-size: 0.75rem;
  line-height: 1.4;
}

.experience-label {
  color: var(--ion-text-color);
  font-weight: 600;
}

.chevron-button {
  --color: var(--ion-color-primary);
  --color-hover: var(--ion-color-primary-shade);
  margin: 0;
  padding: 0;
}

.chevron-button ion-icon {
  font-size: 1rem;
}

/* Legacy classes - keeping for compatibility but updating colors */
.figma-chevron-icon {
  font-size: 16px;
  color: var(--ion-color-primary);
}

.figma-experience-text {
  font-weight: 400;
  font-size: 12px;
  line-height: 1.4;
  color: var(--ion-color-medium);
  margin: 0;
}

.figma-experience-label {
  font-weight: 600;
  color: var(--ion-text-color);
}

/* Sequence Section - Lineas de secuencia */
.figma-sequence-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 1px;
  width: 100%;
  max-width: 313px;
  margin: 0 auto;
}

/* Progress Visualization - Group 3 */
.figma-progress-visualization {
  position: relative;
  width: 100%;
  max-width: 313px;
  height: 80px;
  margin: 0 auto;
}

/* Sequence Label positioned over last active point */
.figma-sequence-label {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  z-index: 10;
}

.figma-sequence-title {
  font-weight: 500;
  font-size: 11px;
  line-height: 1.3;
  text-align: center;
  color: var(--ion-color-medium);
  margin: 0;
  white-space: nowrap;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.figma-sequence-divider {
  width: 0;
  height: 9px;
  border-left: 1px dashed rgba(69, 178, 255, 0.5);
}

/* Background Lines */
.figma-progress-lines {
  position: absolute;
  top: 30px;
  left: 0;
  right: 0;
  height: 0;
}

.figma-line-bg {
  position: absolute;
  left: 124.99px;
  width: 179.74px;
  height: 0;
  border-top: 1px solid var(--ion-color-step-200);
}

.figma-line-active {
  position: absolute;
  left: 9.3px;
  width: 115.7px;
  height: 0;
  border-top: 3px solid rgba(69, 178, 255, 0.5);
}

/* Progress Points */
.figma-progress-points {
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  height: 60px;
}

.figma-points-row {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  justify-content: space-between;
  padding: 0;
}

/* Container for progress points */
.figma-points-container {
  display: flex;
  align-items: center;
  gap: 45px;
  flex: 1;
  justify-content: center;
}

/* Navigation Arrows */
.figma-nav-arrow {
  background: var(--ion-card-background);
  border-radius: 12px;
  box-shadow: 0px -1px 10px 1px var(--ion-color-step-100);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

/* Icon containers */
.figma-arrow-container,
.figma-check-container {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.figma-arrow-icon,
.figma-check-icon,
.figma-checklist-icon {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Progress Points */
.figma-point {
  border-radius: 50%;
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.figma-point-completed {
  width: 8px;
  height: 8px;
  background: var(--ion-color-primary);
}

.figma-point-completed-large {
  width: 14px;
  height: 14px;
  background: var(--ion-color-primary);
}

.figma-point-pending {
  width: 8px;
  height: 8px;
  background: var(--ion-color-step-200);
}

/* Statistics Container - Frame 49 */
.figma-stats-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
}

.figma-stats-row {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  position: relative;
  justify-content: space-between;
  padding: 0;
}

/* Stats points container to match points Layout */
.figma-stats-points-container {
  display: flex;
  align-items: center;
  gap: 45px;
  flex: 1;
  justify-content: center;
}

/* Adjust stats to align with points and arrows */
.figma-stats-row .figma-stat-item:first-child {
  margin-left: 4px; /* Align with left arrow center */
}

.figma-stats-row .figma-stat-item:last-child {
  margin-right: 4px; /* Align with right arrow center */
}

.figma-stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 17.39px;
  align-items: center;
  flex-shrink: 0;
}

.figma-stat-percentage {
  font-weight: 400;
  font-size: 8px;
  line-height: 1.21;
  text-align: center;
  color: var(--ion-text-color);
}

.figma-stat-pending {
  color: var(--ion-color-medium);
  opacity: 0.6;
}

.figma-stat-icon {
  width: 17.39px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.figma-stat-completed svg {
  width: 17.39px;
  height: 16px;
}

.figma-stat-pending-icon svg {
  width: 17.39px;
  height: 16px;
}

/* Loading skeleton styles - Keep background variables and animation */
.skeleton-title, .skeleton-subtitle, .skeleton-content, .skeleton-stat {
  background: var(--ion-color-light);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}