import React, { useEffect } from 'react';
import { IonTabBar, IonTabButton, IonLabel } from '@ionic/react';
import { useActiveTab } from '../../hooks/useActiveTab';
import TabBarIcon from './TabBarIcon';

const AnimatedTabBar: React.FC = () => {
  const { activeTab } = useActiveTab();

  useEffect(() => {
    // Update the data attribute on the tab bar for CSS animations
    const tabBar = document.querySelector('.figma-tabbar');
    if (tabBar) {
      tabBar.setAttribute('data-active-tab', activeTab);
    }
  }, [activeTab]);

  return (
    <IonTabBar slot="bottom" className="figma-tabbar" data-active-tab={activeTab}>
      <IonTabButton tab="inicio" href="/tabs/inicio">
        <TabBarIcon iconType="home" />
        <IonLabel>Inicio</IonLabel>
      </IonTabButton>

      <IonTabButton tab="informes" href="/tabs/informes">
        <TabBarIcon iconType="reports" />
        <IonLabel>Informes</IonLabel>
      </IonTabButton>

      <IonTabButton tab="conexion" href="/tabs/conexion">
        <TabBarIcon iconType="connection" />
        <IonLabel>Conexión</IonLabel>
      </IonTabButton>

      <IonTabButton tab="portfolio" href="/tabs/portfolio">
        <TabBarIcon iconType="portfolio" />
        <IonLabel>Portfolio</IonLabel>
      </IonTabButton>

      <IonTabButton tab="horarios" href="/tabs/horarios">
        <TabBarIcon iconType="schedule" />
        <IonLabel>Horarios</IonLabel>
      </IonTabButton>

      <IonTabButton tab="catalogo" href="/tabs/catalogo">
        <TabBarIcon iconType="catalog" />
        <IonLabel>Catálogo</IonLabel>
      </IonTabButton>
    </IonTabBar>
  );
};

export default AnimatedTabBar;
