# Figma TabBar Implementation

Este directorio contiene la implementación del TabBar basado en el diseño de Figma.

## Archivos

### `TabBarIcon.tsx`
Componente React que renderiza los iconos SVG personalizados para cada pestaña del TabBar. Los iconos son copias exactas de los diseños de Figma.

**Props:**
- `iconType`: Tipo de icono ('home' | 'reports' | 'connection' | 'resources' | 'account')
- `className`: Clases CSS adicionales (opcional)

### `FigmaTabBar.css`
Estilos CSS que implementan el diseño exacto del TabBar según las especificaciones de Figma:

**Características del diseño:**
- Fondo blanco (#FFFFFF)
- Sombra: `0px -1px 10px 1px rgba(0, 0, 0, 0.05)`
- Border radius: 78px
- Padding: 24px 29px
- Altura: 82px
- Ancho: 360px (máximo)
- Gap entre elementos: 28px
- Fuente: Inter, 400, 10px
- Color de texto e iconos: #4E4E4E

**Responsive:**
- Ajustes para pantallas menores a 380px
- Soporte para safe area en iOS
- Posicionamiento fijo en la parte inferior

## Uso

```tsx
import TabBarIcon from './TabBarIcon';
import './FigmaTabBar.css';

// En el componente
<IonTabBar slot="bottom" className="figma-tabbar">
  <IonTabButton tab="inicio" href="/tabs/inicio">
    <TabBarIcon iconType="home" />
    <IonLabel>Inicio</IonLabel>
  </IonTabButton>
  // ... más botones
</IonTabBar>
```

## Iconos incluidos

1. **Home** - Icono de casa para la pestaña de inicio
2. **Reports** - Icono de gráficos para informes
3. **Connection** - Icono de chat/conexión
4. **Resources** - Icono de libro/recursos educativos
5. **Account** - Icono de usuario/cuenta

Todos los iconos mantienen las especificaciones exactas de Figma:
- Tamaño: 24x24px
- Stroke width: 1.5px
- Colores según diseño original
