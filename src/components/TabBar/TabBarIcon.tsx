import React from 'react';

interface TabBarIconProps {
  iconType: 'home' | 'reports' | 'connection' | 'portfolio' | 'schedule' | 'catalog';
  className?: string;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({ iconType, className = '' }) => {
  const getIconSVG = (type: string) => {
    switch (type) {
      case 'home':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M9.5 20.5002V16.0002C9.5 14.6192 10.619 13.5002 12 13.5002V13.5002C13.381 13.5002 14.5 14.6192 14.5 16.0002V20.5002H20V11.9143C20 11.3843 19.789 10.8752 19.414 10.5002L12.707 3.79325C12.316 3.40225 11.683 3.40225 11.293 3.79325L4.586 10.5002C4.211 10.8752 4 11.3843 4 11.9143V20.5002H9.5Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'reports':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16.0001 12.1573V8.99902H12.8418" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M19.002 4.99749L20.0024 3.99707" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M20.002 7.99826H21.0024" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M16.0002 3.99651V2.99609" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M21.0036 11.9998V16.0015C21.0036 18.7641 18.7641 21.0036 16.0015 21.0036H7.99818C5.2356 21.0036 2.99609 18.7641 2.99609 16.0015V7.99818C2.99609 5.2356 5.2356 2.99609 7.99818 2.99609H11.9998" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2.99609 12.0003V12.0003C6.48628 14.3271 11.1336 13.8669 14.0997 10.9008L16.0015 8.99902" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'connection':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.5 13H17" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M13.5 16H17" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M3 8C3 5.23858 5.23858 3 8 3H16C18.7614 3 21 5.23858 21 8V14C21 16.7614 18.7614 19 16 19H6L3 21.5V8Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <rect x="7" y="12.75" width="3.5" height="3.5" rx="1.16667" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M17 9.5H13.5" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M17 6.5H13.5" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <rect x="7" y="6.25" width="3.5" height="3.5" rx="1.16667" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'portfolio':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 6H16V4C16 2.9 15.1 2 14 2H10C8.9 2 8 2.9 8 4V6H4C2.9 6 2 6.9 2 8V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V8C22 6.9 21.1 6 20 6Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10 4H14V6H10V4Z" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M8 10H16" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'schedule':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="9" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M12 7V12L15 15" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      case 'catalog':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 19.995V4.00599C4 2.75982 5.01959 1.74023 6.2657 1.74023H20.0001C20.0001 9.99771 20.0001 14.0033 20.0001 22.2608H6.2657C5.01981 22.2608 4 21.2411 4 19.995ZM4 19.995C4 18.7489 5.01965 17.7293 6.2657 17.7293H19.7483" strokeWidth="1.5" strokeMiterlimit="22.9256" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M6.76953 17.4777V1.99219" strokeWidth="1.5" strokeMiterlimit="22.9256" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M10 20L20 20" strokeMiterlimit="22.9256" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M17.9511 2.00044V5.20575C17.9511 5.53046 17.5942 5.7286 17.3186 5.55685L16.8366 5.25647C16.6156 5.11875 16.3355 5.11875 16.1145 5.25647L15.6325 5.55685C15.357 5.7286 15 5.53046 15 5.20575V2.00044" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`custom-tab-icon ${className}`}>
      {getIconSVG(iconType)}
    </div>
  );
};

export default TabBarIcon;
