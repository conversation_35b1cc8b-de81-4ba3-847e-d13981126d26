/* Figma Prototype Tab Bar - Optimized Floating Design */

/* Complex backdrop effects and positioning that cannot be replicated with standard Tailwind */
.figma-tabbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.326);
  border-radius: 40px;
  padding: 12px;
  margin: 12px 16px;
  margin-bottom: max(0px, calc(env(safe-area-inset-bottom)));
  position: relative;
}

/* Essential Ionic tab button variables */
.figma-tabbar ion-tab-button {
  --background: transparent;
  --background-focused: var(--ion-color-step-100);
  --background-focused-opacity: 0.1;
  --color: var(--ion-color-medium);
  --color-focused: var(--ion-color-primary);
  --color-selected: var(--ion-color-primary);
  --ripple-color: var(--ion-color-primary);
  --padding-top: 8px;
  --padding-bottom: 8px;
  --padding-start: 4px;
  --padding-end: 4px;

  /* Professional animations */
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: scale(1);
  opacity: 0.7;
}

/* Tab button labels with specific Figma typography */
.figma-tabbar ion-tab-button ion-label {
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  color: var(--ion-color-medium);
}

.figma-tabbar ion-tab-button.tab-selected ion-label {
  font-weight: 600;
  color: var(--ion-color-primary);
}

/* Professional animations for selected state */
.figma-tabbar ion-tab-button.tab-selected,
.figma-tabbar ion-tab-button[aria-selected="true"] {
  transform: scale(1.05);
  opacity: 1;
}

/* Icon animations */
.figma-tabbar ion-tab-button .custom-tab-icon {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: scale(1);
}

.figma-tabbar ion-tab-button.tab-selected .custom-tab-icon,
.figma-tabbar ion-tab-button[aria-selected="true"] .custom-tab-icon {
  transform: scale(1.1);
  animation: tabIconPulse 0.4s ease-out;
}

/* Label animations */
.figma-tabbar ion-tab-button ion-label {
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: translateY(0);
}

.figma-tabbar ion-tab-button.tab-selected ion-label,
.figma-tabbar ion-tab-button[aria-selected="true"] ion-label {
  transform: translateY(-1px);
  animation: tabLabelSlide 0.3s ease-out;
}

/* Icon color states - Selected */
.figma-tabbar ion-tab-button.tab-selected .custom-tab-icon svg path,
.figma-tabbar ion-tab-button.tab-selected .custom-tab-icon svg rect,
.figma-tabbar ion-tab-button.tab-selected .custom-tab-icon svg circle {
  stroke: var(--ion-color-primary) !important;
  stroke-width: 1.5px;
}

/* Icon color states - Unselected */
.figma-tabbar ion-tab-button:not(.tab-selected) .custom-tab-icon svg path,
.figma-tabbar ion-tab-button:not(.tab-selected) .custom-tab-icon svg rect,
.figma-tabbar ion-tab-button:not(.tab-selected) .custom-tab-icon svg circle {
  stroke: var(--ion-color-medium) !important;
  stroke-width: 1.5px;
}

/* Additional fallback for Ionic's default selected state */
.figma-tabbar ion-tab-button[aria-selected="true"] .custom-tab-icon svg path,
.figma-tabbar ion-tab-button[aria-selected="true"] .custom-tab-icon svg rect,
.figma-tabbar ion-tab-button[aria-selected="true"] .custom-tab-icon svg circle {
  stroke: var(--ion-color-primary) !important;
  stroke-width: 1.5px;
}

/* Professional keyframe animations */
@keyframes tabIconPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes tabLabelSlide {
  0% {
    transform: translateY(0);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-1px);
    opacity: 1;
  }
}

/* Hover effects for better UX */
.figma-tabbar ion-tab-button:hover:not(.tab-selected):not([aria-selected="true"]) {
  transform: scale(1.02);
  opacity: 0.85;
  transition: all 0.2s ease-out;
}

.figma-tabbar ion-tab-button:hover:not(.tab-selected):not([aria-selected="true"]) .custom-tab-icon {
  transform: scale(1.05);
}

/* Active/pressed state */
.figma-tabbar ion-tab-button:active {
  transform: scale(0.98);
  transition: all 0.1s ease-out;
}

/* Smooth color transitions for SVG elements */
.figma-tabbar ion-tab-button .custom-tab-icon svg path,
.figma-tabbar ion-tab-button .custom-tab-icon svg rect,
.figma-tabbar ion-tab-button .custom-tab-icon svg circle {
  transition: stroke 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Enhanced focus states for accessibility */
.figma-tabbar ion-tab-button:focus-visible {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
  border-radius: 8px;
}

/* Professional sliding indicator */
.figma-tabbar::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: calc(100% / 6); /* 6 tabs */
  height: 3px;
  background: linear-gradient(90deg, var(--ion-color-primary), var(--ion-color-primary-shade));
  border-radius: 2px 2px 0 0;
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  transform: translateX(0);
  z-index: 1;
  box-shadow: 0 -1px 4px rgba(var(--ion-color-primary-rgb), 0.3);
}

/* Sliding indicator positions for each tab */
.figma-tabbar[data-active-tab="inicio"]::before {
  transform: translateX(0);
}

.figma-tabbar[data-active-tab="informes"]::before {
  transform: translateX(100%);
}

.figma-tabbar[data-active-tab="conexion"]::before {
  transform: translateX(200%);
}

.figma-tabbar[data-active-tab="portfolio"]::before {
  transform: translateX(300%);
}

.figma-tabbar[data-active-tab="horarios"]::before {
  transform: translateX(400%);
}

.figma-tabbar[data-active-tab="catalogo"]::before {
  transform: translateX(500%);
}

/* Active/pressed states */
.figma-tabbar ion-tab-button:active {
  transform: scale(1.25);
}

/* Essential Ionic variables for tab bar */
ion-tab-bar.figma-tabbar {
  --background: var(--ion-card-background);
  --border: none;
  --color: var(--ion-text-color);
}

/* Ensure ion-tabs uses the correct background */
ion-tabs {
  --background: var(--ion-background-color);
}

.transparent-tabbar-content {
  --padding-bottom: 0px;
}

.transparent-tabbar-content .scroll-content {
  padding-bottom: 0px !important;
}

/* Complex spacing calculation that cannot be replicated with standard Tailwind */
.transparent-tabbar-content .min-h-full {
  padding-bottom: calc(100px + env(safe-area-inset-bottom));
  min-height: 100vh;
}

/* Enhanced floating appearance for optimized Layout */
.figma-tabbar {
  /* Ensure proper z-index above content but below fixed header */
  z-index: 999;
}

/* Dark mode support is handled automatically through CSS variables */

