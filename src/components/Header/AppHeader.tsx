import React, { useState, useRef, useMemo, useCallback } from 'react';
import { IonH<PERSON>er, IonT<PERSON>bar, IonBackButton, IonButtons } from '@ionic/react';
import { useHistory, useLocation } from 'react-router-dom';
import { getSelectedStudent } from '../../data/studentMockData';
import { ROUTES } from '../../routes/routes';
import StudentDropdown from '../StudentDropdown/StudentDropdown';
import { useRestrictedNavigation } from '../../hooks/useRestrictedNavigation';
import './AppHeader.css';

/**
 * AppHeader - Main application header component
 * Based on Figma design node-id=5081-16479
 * Shows selected student info, notifications, and user profile access
 */

const AppHeader: React.FC = () => {
  const history = useHistory();
  const location = useLocation();

  // Memoize selectedStudent to prevent unnecessary re-renders
  const selectedStudent = useMemo(() => getSelectedStudent(), []);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const studentCardRef = useRef<HTMLDivElement>(null);

  // Use restricted navigation hook
  const { canGoBackInTabs, getRestrictedDefaultHref } = useRestrictedNavigation();

  // Check if we're on the account page
  const isAccountPage = location.pathname === ROUTES.ACCOUNT;

  // Memoize event handlers to prevent unnecessary re-renders
  const handleProfileClick = useCallback(() => {
    history.push(ROUTES.ACCOUNT);
  }, [history]);

  const handleNotificationsClick = useCallback(() => {
    history.push(ROUTES.NOTIFICATIONS);
  }, [history]);

  const handleStudentDropdownClick = useCallback(() => {
    setIsDropdownOpen(prev => !prev);
  }, []);

  const handleDropdownClose = useCallback(() => {
    setIsDropdownOpen(false);
  }, []);

  return (
    <>
      <IonHeader translucent={true} className="app-header-translucent">
        <IonToolbar className="app-toolbar-translucent">
          {/* Back Button - Only shows when can go back within tabs */}
          {/* {canGoBackInTabs() && (
            <IonButtons slot="start">
              <IonBackButton
                text={''}
                defaultHref={getRestrictedDefaultHref()}
              />
            </IonButtons>
          )} */}

          <div className="app-header-content flex justify-stretch items-stretch gap-6 p-2 px-4 w-full">
            {/* Student Card Dropdown */}
            <div
              ref={studentCardRef}
              className="student-card-dropdown rounded-md flex items-center gap-4 py-[7px] px-4 h-10 flex-1 cursor-pointer transition-all duration-200 ease-in-out hover:-translate-y-px"
              onClick={handleStudentDropdownClick}
            >
            <div className="student-info-section flex items-center gap-2 flex-1">
              <div className="student-avatar">
                {selectedStudent?.profileImage ? (
                  <img
                    src="/assets/images/avatar.png"
                    alt={selectedStudent.name}
                    className="w-full h-full object-cover rounded-full"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-teal-500 to-blue-400 flex items-center justify-center text-white font-semibold text-xs rounded-full">
                    {selectedStudent?.avatar || 'U'}
                  </div>
                )}
              </div>
              <div className="flex flex-col justify-center">
                <div className="text-[var(--ion-color-medium)] text-xs sm:text-[9px] leading-tight font-normal">Progreso de</div>
                <div className="text-[var(--ion-text-color)] text-xs sm:text-[11px] leading-tight font-bold max-w-[120px] sm:max-w-[100px] overflow-hidden text-ellipsis whitespace-nowrap">
                  {selectedStudent?.name || 'Estudiante'}
                </div>
              </div>
            </div>
            <div className="w-4 h-4 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 4L10 8L6 12" className="stroke-[var(--ion-color-primary)]" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="header-actions flex items-center gap-3">
            {/* Notifications Button */}
            <div className="action-button cursor-pointer transition-all duration-200 ease-in-out hover:-translate-y-px" onClick={handleNotificationsClick}>
              <div className="button-container rounded-md flex flex-col justify-center items-center gap-2.5 p-1 w-10 h-10 transition-all duration-200 ease-in-out relative">
                <div className="w-6 h-6 flex items-center justify-center">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className='text-[var(--ion-text-color)]'>
                    <path d="M18 8C18 6.4087 17.3679 4.88258 16.2426 3.75736C15.1174 2.63214 13.5913 2 12 2C10.4087 2 8.88258 2.63214 7.75736 3.75736C6.63214 4.88258 6 6.4087 6 8C6 15 3 17 3 17H21C21 17 18 15 18 8Z" stroke="CurrentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M13.73 21C13.5542 21.3031 13.3019 21.5547 12.9982 21.7295C12.6946 21.9044 12.3504 21.9965 12 21.9965C11.6496 21.9965 11.3054 21.9044 11.0018 21.7295C10.6981 21.5547 10.4458 21.3031 10.27 21" stroke="CurrentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                {/* Notification Badge */}
                <div className="notification-badge"></div>
              </div>
            </div>

            {/* Profile Button - Hidden when on Account page */}
            {!isAccountPage && (
              <div className="action-button cursor-pointer transition-all duration-200 ease-in-out hover:-translate-y-px" onClick={handleProfileClick}>
                <div className="button-container rounded-md flex flex-col justify-center items-center gap-2.5 p-1 w-10 h-10 transition-all duration-200 ease-in-out relative">
                  <div className="text-[var(--ion-text-color)] w-6 h-6 flex items-center justify-center">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="CurrentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                      <circle cx="12" cy="7" r="4" stroke="CurrentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        </IonToolbar>
      </IonHeader>

      {/* Student Dropdown - Outside header for better z-index control */}
      <StudentDropdown
        isOpen={isDropdownOpen}
        onClose={handleDropdownClose}
        anchorRef={studentCardRef}
      />
    </>
  );
};

export default AppHeader;
