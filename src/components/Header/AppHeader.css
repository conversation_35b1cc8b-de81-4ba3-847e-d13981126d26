/* App Header - Optimized Translucent with Blur Effect and Scroll-Behind Behavior */

/* CSS Custom Properties for Dynamic Header Height Calculation */
:root {
  --app-header-base-height: 60px;
  --app-header-total-height: calc(var(--app-header-base-height) + env(safe-area-inset-top, 0px));
  --app-header-content-offset: calc(var(--app-header-total-height) + 10px);
}

/* Translucent header with proper scroll-behind behavior */
.app-header-translucent {
  --background: var(--ion-toolbar-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--ion-color-step-150);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* Ensure proper backdrop-filter rendering */
  isolation: isolate;
  will-change: backdrop-filter;
  /* Dynamic height calculation */
  height: var(--app-header-total-height);
}

/* Essential Ionic variables */
.app-toolbar-translucent {
  --background: transparent;
  --border-color: transparent;
  --color: var(--ion-text-color);
  --min-height: var(--app-header-base-height);
}

/* Complex backdrop effects that cannot be replicated with standard Tailwind */
.student-card-dropdown {
  background: var(--ion-card-background);
  box-shadow: 0px -1px 10px 1px var(--ion-color-step-100);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--ion-color-step-150);
}

.student-card-dropdown:hover {
  box-shadow: 0px 2px 15px 2px var(--ion-color-step-200);
  background: var(--ion-card-background);
  transform: translateY(-1px);
}

.student-avatar {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--ion-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Complex backdrop effects that cannot be replicated with standard Tailwind */
.button-container {
  background: var(--ion-card-background);
  box-shadow: 0px -1px 10px 1px var(--ion-color-step-100);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.action-button:hover .button-container {
  box-shadow: 0px 2px 15px 2px var(--ion-color-step-200);
  background: var(--ion-card-background);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: 11px;
  right: 13px;
  width: 10px;
  height: 10px;
  background: var(--ion-color-primary);
  border: 1px solid var(--ion-background-color);
  border-radius: 50%;
  z-index: 1;
}

/* Dark mode support for fixed header handled automatically through CSS variables */

/* Dark mode styling handled automatically through CSS variables */



/* Dark mode support is handled automatically through CSS variables */

/* Dark mode text and icon colors handled automatically through CSS variables */

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .student-card-dropdown,
  .button-container {
    transition: none;
  }
  
  .student-card-dropdown:hover,
  .action-button:hover .button-container {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .student-card-dropdown,
  .button-container {
    border: 1px solid #4E4E4E;
  }
  
  .notification-badge {
    border-width: 2px;
  }
}
