/* Student Dropdown - Enhanced animations and native loading experience */

/* Custom shadow that adapts to light/dark mode - no white glow */
.shadow-dropdown {
  box-shadow: 0px 4px 20px 0px var(--ion-color-step-200);
  border: 1px solid var(--ion-color-step-150);
}

/* Dark mode shadow is handled automatically through CSS variables */

/* Enhanced dropdown animations following Ionic design patterns */
.student-dropdown-animate {
  animation: dropdownSlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  transform-origin: top center;
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

/* Smooth dropdown entrance animation */
@keyframes dropdownSlideIn {
  0% {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced hover effects for dropdown items */
.student-dropdown-animate .cursor-pointer {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.student-dropdown-animate .cursor-pointer:hover {
  transform: translateX(2px);
  background-color: rgba(var(--ion-color-primary-rgb), 0.1);
}

/* Loading state styling */
.student-loading {
  --backdrop-opacity: 0.6;
  --spinner-color: var(--ion-color-primary);
}

.student-loading .loading-wrapper {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  border-radius: 12px;
  box-shadow: 0px 8px 32px 0px var(--ion-color-step-200);
}

/* Dark mode loading adjustments are handled automatically through CSS variables */

/* Accessibility improvements - Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .student-dropdown-animate {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }

  .student-dropdown-animate .cursor-pointer {
    transition: none !important;
  }

  .student-dropdown-animate .cursor-pointer:hover {
    transform: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .shadow-dropdown {
    border: 2px solid var(--ion-text-color);
  }

  .student-dropdown-animate .cursor-pointer:hover {
    border: 1px solid var(--ion-text-color);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .w-\[264px\] {
    width: calc(100vw - 32px) !important;
    max-width: 264px !important;
  }

  /* Adjust animation for mobile */
  .student-dropdown-animate {
    animation-duration: 0.25s;
  }
}
