import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { IonText, IonLoading } from '@ionic/react';
import { useHistory } from 'react-router-dom';
import { getAllStudents, getSelectedStudent, setSelectedStudent } from '../../data/studentMockData';
import { ROUTES } from '../../routes/routes';
import './StudentDropdown.css';

interface StudentDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  anchorRef: React.RefObject<HTMLElement>;
}

interface LoadingState {
  isLoading: boolean;
  message: string;
}

/**
 * StudentDropdown - Optimized for performance
 *
 * Performance optimizations implemented:
 * - React.memo with custom comparison function
 * - Conditional data loading (only when isOpen)
 * - Memoized event handlers with useCallback
 * - Reduced debug logging
 * - Early return for closed state
 */

const StudentDropdown: React.FC<StudentDropdownProps> = ({ isOpen, onClose, anchorRef }) => {
  // Performance optimization: Only log when actually opening/closing
  if (process.env.NODE_ENV === 'development' && isOpen) {
    console.log('🔍 StudentDropdown: Rendering dropdown (optimized)');
  }

  const history = useHistory();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Loading state for student transitions
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    message: ''
  });

  // Memoize expensive data operations - only compute when needed
  const students = useMemo(() => {
    if (!isOpen) return [];
    return getAllStudents();
  }, [isOpen]);

  const selectedStudent = useMemo(() => {
    if (!isOpen) return null;
    return getSelectedStudent();
  }, [isOpen]);

  // Position dropdown relative to anchor - only when open
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });

  // Close dropdown when clicking outside - only when open
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, anchorRef]);

  // Position dropdown relative to anchor - only when open
  useEffect(() => {
    if (!isOpen || !anchorRef.current || !dropdownRef.current) return;

    const anchorRect = anchorRef.current.getBoundingClientRect();

    // Position below the anchor with some spacing
    const top = anchorRect.bottom + 8;
    const left = anchorRect.left;
    const width = anchorRect.width;

    // Ensure dropdown doesn't go off screen
    const maxLeft = window.innerWidth - width - 16;
    const finalLeft = Math.min(left, maxLeft);

    setPosition({ top, left: finalLeft, width });
  }, [isOpen, anchorRef]);

  const handleStudentSelect = useCallback(async (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    const studentName = student?.name || 'estudiante';

    try {
      // Show loading state
      setLoadingState({
        isLoading: true,
        message: `Cargando información de ${studentName}...`
      });

      // Close dropdown immediately
      onClose();

      // Set the selected student
      setSelectedStudent(studentId);

      // Simulate loading time for smooth transition (minimum 800ms for good UX)
      await new Promise(resolve => setTimeout(resolve, 800));

      // Navigate to refresh the page content smoothly
      window.location.reload();

    } catch (error) {
      console.error('Error selecting student:', error);
      setLoadingState({
        isLoading: false,
        message: ''
      });
    }
  }, [students, onClose]);

  const handleAddStudent = useCallback(() => {
    onClose();
    history.push(ROUTES.STUDENT_SELECTION);
  }, [onClose, history]);

  if (!isOpen) {
    return null;
  }

  return (
    <>
      {/* Native Loading Component */}
      <IonLoading
        isOpen={loadingState.isLoading}
        message={loadingState.message}
        duration={0}
        spinner="crescent"
        cssClass="student-loading"
      />

      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-transparent z-[9998]"
        onClick={onClose}
      />

      {/* Dropdown */}
      <div
        ref={dropdownRef}
        className="rounded-md overflow-hidden student-dropdown-animate z-[10000] bg-[var(--ion-card-background)] shadow-dropdown border border-[var(--ion-color-step-150)]"
        style={{
          position: 'fixed',
          top: `${position.top}px`,
          left: `${position.left}px`,
          width: `${position.width}px`
        }}
      >
        {/* Student List */}
        <div className="py-[7px] px-4">
          {students.map((student) => {
            if (selectedStudent?.id === student.id) return null; // 👈 OMIT selected student

            return (
              <div
                key={student.id}
                className="flex items-center gap-2 py-2 px-2 -mx-2 cursor-pointer transition-all duration-200 ease-in-out rounded hover:bg-[var(--ion-color-primary-tint)] hover:bg-opacity-25"
                onClick={() => handleStudentSelect(student.id)}
              >
                <div className="w-[25px] h-[25px] rounded-full overflow-hidden flex-shrink-0">
                  {student.profileImage ? (
                    <img
                      src={student.profileImage}
                      alt={student.name}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-teal-500 to-blue-400 flex items-center justify-center text-white font-semibold text-xs rounded-full">
                      {student.avatar}
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="leading-tight mb-0.5">
                    <IonText>
                      <span className="font-normal text-[9px] leading-tight text-[var(--ion-color-medium)] antialiased">
                        Progreso de
                      </span>
                    </IonText>
                  </div>
                  <div className="leading-tight">
                    <IonText>
                      <span className="font-bold text-[11px] leading-tight text-[var(--ion-text-color)] antialiased max-w-full overflow-hidden text-ellipsis whitespace-nowrap block">
                        {student.name}
                      </span>
                    </IonText>
                  </div>
                </div>
                <div className="w-4 h-4 flex-shrink-0 flex items-center justify-center">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M6 4L10 8L6 12"
                      className="stroke-[var(--ion-color-primary)]"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
            );
          })}

          {/* Add Student Option */}
          <div className="h-px -mx-2 my-2 bg-[var(--ion-color-step-200)]" />
          <div
            className="flex items-center gap-2 py-2 px-2 -mx-2 cursor-pointer transition-all duration-200 ease-in-out rounded mt-2 hover:bg-[var(--ion-color-secondary-tint)] hover:bg-opacity-5"
            onClick={handleAddStudent}
          >
            <div className="w-[25px] h-[25px] rounded-full overflow-hidden flex-shrink-0">
              <div className="w-full h-full flex items-center justify-center rounded-full bg-[var(--ion-color-light)]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8 3V13M3 8H13"
                    className="stroke-[var(--ion-color-medium)]"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="leading-tight">
                <IonText>
                  <span className="font-medium text-[12px] text-[var(--ion-color-medium)]">
                    Cambiar estudiante
                  </span>
                </IonText>
              </div>
            </div>
            <div className="w-4 h-4 flex-shrink-0 flex items-center justify-center">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6 4L10 8L6 12"
                  className="stroke-[var(--ion-color-primary)]"
                  strokeWidth="1"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default React.memo(StudentDropdown, (prevProps, nextProps) => {
  // Only re-render if isOpen state changes or anchorRef changes
  return (
    prevProps.isOpen === nextProps.isOpen &&
    prevProps.anchorRef === nextProps.anchorRef &&
    prevProps.onClose === nextProps.onClose
  );
});
