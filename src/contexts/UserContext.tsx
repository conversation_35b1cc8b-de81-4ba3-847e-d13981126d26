import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  ReactNode,
} from "react";
import { useAuth } from "react-oidc-context";
import { AuthStorageService } from "../services/auth-storage.service";
import { UserApiService } from "../services/user-api.service";

import { clearOIDCStorage } from "../utils/oidc-debug";
import { Capacitor } from "@capacitor/core";
import { CapacitorAuthService } from "../services/capacitor-auth.service";
import { environmentConfig } from "../config/environment.config";

/**
 * User Context for managing authentication state and user information
 * throughout the application. Integrates with OIDC authentication system.
 */

export interface Student {
  id: string;
  name: string;
  grade: string;
  school: string;
  avatar?: string;
  // Enhanced fields to match API response
  surnames?: string;
  login?: string;
  schoolLevelSessions?: Array<{
    id: string;
    schoolId: string;
    schoolName: string;
    levelId: string;
    levelName: string;
    grade: string;
    academicYear?: string;
    isActive: boolean;
  }>;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  avatar: string;
  memberSince: string;
  children: Student[];
}

interface UserContextType {
  user: User | null;
  isAuthenticated: boolean;
  selectedStudent: Student | null;
  isLoading: boolean;
  selectStudent: (student: Student) => void;
  updateUser: (userData: Partial<User>) => void;
  logout: () => Promise<void>;
  updateCapacitorAuthState?: (authResult?: any) => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

const SELECTED_STUDENT_KEY = "selected-student";

interface UserProviderProps {
  children: ReactNode;
}

// Helper function to create User object from OIDC profile using API service
const createUserFromOIDCProfile = async (oidcUser: any): Promise<User> => {
  const profile = oidcUser.profile || {};
  const accessToken = oidcUser.access_token;

  try {
    // Use UserApiService to fetch complete user profile from Santillana's backend
    const user = await UserApiService.fetchUserProfile(profile, accessToken);

    return user;
  } catch (error) {
    console.warn(
      "UserContext - Error fetching user profile from API, using fallback data:",
      error
    );

    // Fallback to basic profile data if API fails
    return {
      id: profile.sub || profile.id || "oidc-user",
      name:
        profile.name ||
        profile.preferred_username ||
        profile.given_name ||
        "Usuario OIDC",
      email: profile.email || "<EMAIL>",
      phone: profile.phone_number || profile.phone || "+34 123 456 789",
      role: profile.role || profile.groups?.[0] || "Padre/Madre",
      avatar:
        profile.picture ||
        "https://ionicframework.com/docs/img/demos/avatar.svg",
      memberSince: new Date().getFullYear().toString(),
      children: [], // Empty children array if API fails
    };
  }
};

// Helper function to load selected student from storage
const loadSelectedStudent = async (userData: User): Promise<Student | null> => {
  const savedStudent = localStorage.getItem(SELECTED_STUDENT_KEY);
  if (savedStudent) {
    try {
      const studentData = JSON.parse(savedStudent);
      // Verify the student still exists in the current user's children
      const studentExists = userData.children.find(
        (child) => child.id === studentData.id
      );
      if (studentExists) {
        return studentData;
      } else {
        return userData.children[0] || null;
      }
    } catch (error) {
      return userData.children[0] || null;
    }
  } else if (userData.children.length > 0) {
    return userData.children[0];
  }
  return null;
};

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const auth = useAuth();
  const [user, setUser] = useState<User | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isCapacitorAuthenticated, setIsCapacitorAuthenticated] =
    useState<boolean>(false);
  const [isLoggingOut, setIsLoggingOut] = useState<boolean>(false);

  // Initialize Capacitor auth service for native platforms
  useEffect(() => {
    if (Capacitor.isNativePlatform()) {
      CapacitorAuthService.initialize().catch((error) => {
        console.error(
          "UserContext - Error initializing Capacitor auth service:",
          error
        );
      });
    }
  }, []);

  // Initialize user data from OIDC auth state (web and native)
  useEffect(() => {
    const initializeUser = async () => {
      try {
        const isNative = Capacitor.isNativePlatform();

        // For native platforms, also check Capacitor auth service
        if (isNative) {
          const capacitorUser = await CapacitorAuthService.getCurrentUser();
          if (capacitorUser && capacitorUser.expiresIn > 0) {
            // Create user object from Capacitor user data
            const userData = await createUserFromOIDCProfile(capacitorUser);
            setUser(userData);
            setIsCapacitorAuthenticated(true);

            // Store tokens for native apps
            await AuthStorageService.storeTokens(capacitorUser as any);

            // Load selected student
            const student = await loadSelectedStudent(userData);
            setSelectedStudent(student);

            setIsInitialized(true);
            return;
          } else {
            setIsCapacitorAuthenticated(false);
          }
        }

        // Standard OIDC authenticated user (web or native fallback)
        if (auth.isAuthenticated && auth.user) {
          // Create user object from OIDC profile with real API data
          const userData = await createUserFromOIDCProfile(auth.user);
          setUser(userData);

          // Store tokens for native apps
          await AuthStorageService.storeTokens(auth.user);

          // Load selected student
          const student = await loadSelectedStudent(userData);
          setSelectedStudent(student);
        }
        // No authentication - clear everything
        else if (!auth.isLoading) {
          setUser(null);
          setSelectedStudent(null);
          setIsCapacitorAuthenticated(false);
          localStorage.removeItem(SELECTED_STUDENT_KEY);
        }
      } catch (error) {
        console.warn("UserContext - Error initializing user data:", error);
        setUser(null);
        setSelectedStudent(null);
        localStorage.removeItem("isTestUser");
      } finally {
        if (!auth.isLoading) {
          setIsInitialized(true);
        }
      }
    };

    initializeUser();
  }, [auth.isAuthenticated, auth.isLoading, auth.user]);

  // Save selected student to localStorage when it changes
  useEffect(() => {
    if (selectedStudent) {
      try {
        localStorage.setItem(
          SELECTED_STUDENT_KEY,
          JSON.stringify(selectedStudent)
        );
      } catch (error) {
        console.warn("UserContext - Error saving selected student:", error);
      }
    }
  }, [selectedStudent]);

  const logout = async () => {
    try {
      // Set logging out state to immediately disable authentication
      setIsLoggingOut(true);

      // Clear local React state immediately
      setUser(null);
      setSelectedStudent(null);
      setIsCapacitorAuthenticated(false);

      if (Capacitor.isNativePlatform()) {
        // Native logout with CapacitorAuthService
        await CapacitorAuthService.signOut();
      } else {
        // Web logout - clear local session and force fresh login
        try {
          // Clear API cache first
          await UserApiService.clearCache();

          // Clear local state
          await auth.removeUser();
          await AuthStorageService.clearTokens();
          clearOIDCStorage();

          // Clear specific student selection keys before clearing all storage
          localStorage.removeItem(SELECTED_STUDENT_KEY);
          localStorage.removeItem("selectedStudentId");

          // Clear all storage completely
          localStorage.clear();
          sessionStorage.clear();

          // Clear all cookies to ensure fresh authentication
          document.cookie.split(";").forEach(function (c) {
            document.cookie = c
              .replace(/^ +/, "")
              .replace(
                /=.*/,
                "=;expires=" + new Date().toUTCString() + ";path=/"
              );
          });

          // Small delay to allow loading to be hidden before redirect
          await new Promise((resolve) => setTimeout(resolve, 200));

          // Reset logging out state before redirect
          setIsLoggingOut(false);

          // Redirect to auth page - this should trigger fresh login
          window.location.href = "/auth";
        } catch (oidcError) {
          console.warn("Logout cleanup failed, forcing redirect:", oidcError);
          // Force cleanup and redirect
          await UserApiService.clearCache();
          localStorage.removeItem(SELECTED_STUDENT_KEY);
          localStorage.removeItem("selectedStudentId");
          localStorage.clear();
          sessionStorage.clear();

          // Small delay to allow loading to be hidden before redirect
          await new Promise((resolve) => setTimeout(resolve, 200));

          // Reset logging out state before redirect
          setIsLoggingOut(false);

          window.location.href = "/auth";
        }
      }
    } catch (error) {
      console.error("UserContext - Error during logout:", error);

      // Force clear everything even if there are errors
      setUser(null);
      setSelectedStudent(null);

      try {
        await UserApiService.clearCache();
        await AuthStorageService.clearTokens();
        clearOIDCStorage();
        if (Capacitor.isNativePlatform()) {
          await CapacitorAuthService.clearAllAuthData();
        }
      } catch (clearError) {
        console.error("Error during emergency cleanup:", clearError);
      }

      localStorage.removeItem(SELECTED_STUDENT_KEY);
      localStorage.removeItem("selectedStudentId");
      localStorage.clear();
      sessionStorage.clear();

      // Small delay to allow loading to be hidden before redirect
      await new Promise((resolve) => setTimeout(resolve, 200));

      // Reset logging out state before redirect
      setIsLoggingOut(false);

      // Always redirect to auth page
      window.location.href = "/auth";
    }
  };

  const selectStudent = (student: Student) => {
    setSelectedStudent(student);
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
    }
  };

  // Method to update Capacitor authentication state
  const updateCapacitorAuthState = useCallback(async (authResult?: any) => {
    if (Capacitor.isNativePlatform()) {
      if (authResult) {
        // Create user object from auth result
        const userData = await createUserFromOIDCProfile(authResult);
        setUser(userData);
        setIsCapacitorAuthenticated(true);

        // Load selected student
        const student = await loadSelectedStudent(userData);
        setSelectedStudent(student);
      } else {
        setUser(null);
        setSelectedStudent(null);
        setIsCapacitorAuthenticated(false);
      }
    }
  }, []);

  // Determine authentication status based on platform
  // If logging out, always return false to prevent redirects
  const isAuthenticated = isLoggingOut
    ? false
    : Capacitor.isNativePlatform()
    ? isCapacitorAuthenticated
    : auth.isAuthenticated;

  const value: UserContextType = {
    user,
    isAuthenticated,
    selectedStudent,
    isLoading: auth.isLoading || !isInitialized,
    logout,
    selectStudent,
    updateUser,
    updateCapacitorAuthState,
  };

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;
};

/**
 * Custom hook to use user context
 * Throws error if used outside of UserProvider
 */
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};

export default UserContext;
