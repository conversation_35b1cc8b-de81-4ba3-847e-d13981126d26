/**
 * LoadingContext - React Context for HTTP Loading State Management
 * Provides loading state for HTTP requests throughout the React component tree
 * Integrates with LoadingService for global HTTP loading state management
 * Note: Loading is automatically managed by EnhancedHttpService for all HTTP requests
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { IonLoading } from '@ionic/react';
import { LoadingContextType, LoadingOverlayConfig } from '../types/loading.types';
import { LoadingService, LoadingEvent } from '../services/loading.service';
import { debugLog } from '../config/environment.config';
import ModernLoading from '../components/ModernLoading/ModernLoading';

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: ReactNode;
  showGlobalLoading?: boolean;
  useModernLoading?: boolean;
  customLoadingComponent?: React.ComponentType<{ isOpen: boolean; message: string }>;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({
  children,
  showGlobalLoading = true,
  useModernLoading = true,
  customLoadingComponent: CustomLoading
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState('Cargando...');
  const [loadingConfig, setLoadingConfig] = useState<LoadingOverlayConfig>({});

  useEffect(() => {
    const listenerId = 'loading-context';

    // Subscribe to loading service events
    LoadingService.subscribe(listenerId, (event: LoadingEvent) => {
      debugLog('[LoadingContext] Loading event received', event);

      const globalState = LoadingService.getGlobalState();
      setIsLoading(globalState.isGlobalLoading);
      setLoadingMessage(LoadingService.getCurrentMessage());

      // Update loading configuration
      if (globalState.isGlobalLoading) {
        const config = LoadingService.createOverlayConfig(LoadingService.getCurrentMessage());
        setLoadingConfig(config);
      }
    });

    // Initialize with current state
    const globalState = LoadingService.getGlobalState();
    setIsLoading(globalState.isGlobalLoading);
    setLoadingMessage(LoadingService.getCurrentMessage());

    debugLog('[LoadingContext] Initialized and subscribed to LoadingService');

    // Cleanup subscription on unmount
    return () => {
      LoadingService.unsubscribe(listenerId);
      debugLog('[LoadingContext] Unsubscribed from LoadingService');
    };
  }, []);

  // Context value - Focused on HTTP loading state monitoring
  const contextValue: LoadingContextType = {
    isLoading,
    loadingMessage,
    // These methods are primarily for internal HTTP service use
    // Manual loading control should not be used for UI operations
    showLoading: (operation?: string, message?: string) => {
      debugLog('[LoadingContext] HTTP loading started', { operation, message });
      return LoadingService.showLoading(operation, message);
    },
    hideLoading: (operationId: string) => {
      debugLog('[LoadingContext] HTTP loading stopped', { operationId });
      LoadingService.hideLoading(operationId);
    },
    hideAllLoading: () => {
      debugLog('[LoadingContext] All HTTP loading stopped');
      LoadingService.hideAllLoading();
    },
    getActiveOperations: () => {
      return LoadingService.getActiveOperations();
    },
    isOperationLoading: (operation: string) => {
      return LoadingService.isOperationLoading(operation);
    }
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
      
      {/* Global Loading Overlay */}
      {showGlobalLoading && (
        <>
          {CustomLoading ? (
            <CustomLoading isOpen={isLoading} message={loadingMessage} />
          ) : useModernLoading ? (
            <ModernLoading
              isOpen={isLoading}
              message={loadingMessage}
              spinner={loadingConfig.spinner || 'crescent'}
              duration={loadingConfig.duration || 0}
              showBackdrop={loadingConfig.showBackdrop !== false}
              backdropDismiss={loadingConfig.backdropDismiss || false}
              className={loadingConfig.cssClass || ''}
            />
          ) : (
            <IonLoading
              isOpen={isLoading}
              message={loadingMessage}
              spinner={loadingConfig.spinner || 'crescent'}
              duration={loadingConfig.duration || 0}
              showBackdrop={loadingConfig.showBackdrop !== false}
              backdropDismiss={loadingConfig.backdropDismiss || false}
              cssClass={`loading-overlay ${loadingConfig.cssClass || ''}`}
              keyboardClose={loadingConfig.keyboardClose || false}
            />
          )}
        </>
      )}
    </LoadingContext.Provider>
  );
};

/**
 * Custom hook to use loading context
 * Throws error if used outside of LoadingProvider
 */
export const useLoading = (): LoadingContextType => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

/**
 * Higher-order component for automatic loading management
 */
export function withLoading<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  defaultOperation?: string
) {
  return function LoadingWrappedComponent(props: P) {
    const { showLoading, hideLoading } = useLoading();

    // Enhanced props with loading controls
    const enhancedProps = {
      ...props,
      showLoading: (operation?: string, message?: string) => 
        showLoading(operation || defaultOperation, message),
      hideLoading
    } as P & {
      showLoading: (operation?: string, message?: string) => string;
      hideLoading: (operationId: string) => void;
    };

    return <WrappedComponent {...enhancedProps} />;
  };
}

/**
 * Hook for monitoring HTTP loading state
 * Note: This should only be used to monitor loading state, not to manually control it
 * Loading is automatically managed by EnhancedHttpService for HTTP requests
 */
export const useHttpLoading = () => {
  const { isLoading, loadingMessage, getActiveOperations, isOperationLoading } = useLoading();

  return {
    isLoading,
    loadingMessage,
    getActiveOperations,
    isOperationLoading
  };
};

/**
 * Hook for HTTP operations with automatic loading
 * This hook should be used for operations that make HTTP requests
 * Loading will be automatically managed by the EnhancedHttpService
 */
export const useHttpOperation = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  operationName?: string
) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = async (...args: T): Promise<R> => {
    try {
      setIsLoading(true);
      setError(null);

      // Note: Loading overlay is automatically handled by EnhancedHttpService
      // when the operation makes HTTP requests
      const result = await operation(...args);

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    execute,
    isLoading,
    error,
    clearError: () => setError(null)
  };
};

export default LoadingContext;
