/**
 * ErrorContext - React Context for Error Handling
 * Provides error handling capabilities throughout the React component tree
 * Integrates with ErrorHandlerService for centralized error management
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { ErrorContextType, HttpError, ErrorHandlingConfig } from '../types/loading.types';
import { ErrorHandlerService, ErrorEvent } from '../services/error-handler.service';
import { debugLog } from '../config/environment.config';

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

interface ErrorProviderProps {
  children: ReactNode;
  enableGlobalErrorHandling?: boolean;
  maxErrorHistory?: number;
}

export const ErrorProvider: React.FC<ErrorProviderProps> = ({ 
  children, 
  enableGlobalErrorHandling = true,
  maxErrorHistory = 50
}) => {
  const [lastError, setLastError] = useState<HttpError | null>(null);
  const [errorHistory, setErrorHistory] = useState<HttpError[]>([]);

  useEffect(() => {
    const listenerId = 'error-context';

    // Subscribe to error service events
    ErrorHandlerService.subscribe(listenerId, (event: ErrorEvent) => {
      debugLog('[ErrorContext] Error event received', event);

      switch (event.type) {
        case 'error-occurred':
          setLastError(event.error);
          setErrorHistory(prev => {
            const newHistory = [event.error, ...prev];
            return newHistory.slice(0, maxErrorHistory);
          });
          break;
        case 'error-handled':
          debugLog('[ErrorContext] Error handled', event.error);
          break;
        case 'error-dismissed':
          debugLog('[ErrorContext] Error dismissed', event.error);
          break;
      }
    });

    // Initialize with current error history
    const currentHistory = ErrorHandlerService.getErrorHistory();
    setErrorHistory(currentHistory.slice(0, maxErrorHistory));
    if (currentHistory.length > 0) {
      setLastError(currentHistory[0]);
    }

    debugLog('[ErrorContext] Initialized and subscribed to ErrorHandlerService');

    // Cleanup subscription on unmount
    return () => {
      ErrorHandlerService.unsubscribe(listenerId);
      debugLog('[ErrorContext] Unsubscribed from ErrorHandlerService');
    };
  }, [maxErrorHistory]);

  // Context value
  const contextValue: ErrorContextType = {
    lastError,
    errorHistory,
    handleError: async (error: any, config?: Partial<ErrorHandlingConfig>) => {
      debugLog('[ErrorContext] handleError called', { error, config });
      
      if (enableGlobalErrorHandling) {
        await ErrorHandlerService.handleError(error, config);
      } else {
        // Just parse and store the error without showing UI
        const parsedError = ErrorHandlerService.parseError(error);
        setLastError(parsedError);
        setErrorHistory(prev => {
          const newHistory = [parsedError, ...prev];
          return newHistory.slice(0, maxErrorHistory);
        });
      }
    },
    clearError: () => {
      debugLog('[ErrorContext] clearError called');
      setLastError(null);
    },
    clearErrorHistory: () => {
      debugLog('[ErrorContext] clearErrorHistory called');
      setErrorHistory([]);
      setLastError(null);
      ErrorHandlerService.clearErrorHistory();
    },
    getErrorMessage: (error: any) => {
      const parsedError = ErrorHandlerService.parseError(error);
      return parsedError.message;
    },
    isRetryableError: (error: HttpError) => {
      // Network errors are retryable
      if (error.status === 0) return true;
      
      // Server errors (5xx) are retryable
      if (error.status >= 500) return true;
      
      // Rate limiting is retryable
      if (error.status === 429) return true;
      
      // Timeout errors are retryable
      if (error.status === 408) return true;
      
      return false;
    }
  };

  return (
    <ErrorContext.Provider value={contextValue}>
      {children}
    </ErrorContext.Provider>
  );
};

/**
 * Custom hook to use error context
 * Throws error if used outside of ErrorProvider
 */
export const useError = (): ErrorContextType => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
};

/**
 * Higher-order component for automatic error handling
 */
export function withErrorHandling<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  defaultErrorConfig?: Partial<ErrorHandlingConfig>
) {
  return function ErrorWrappedComponent(props: P) {
    const { handleError } = useError();

    // Enhanced props with error handling
    const enhancedProps = {
      ...props,
      handleError: (error: any, config?: Partial<ErrorHandlingConfig>) => 
        handleError(error, { ...defaultErrorConfig, ...config })
    } as P & {
      handleError: (error: any, config?: Partial<ErrorHandlingConfig>) => Promise<void>;
    };

    return <WrappedComponent {...enhancedProps} />;
  };
}

/**
 * Hook for managing component-specific error state
 */
export const useComponentError = (componentName: string) => {
  const { handleError, lastError, clearError } = useError();
  const [componentError, setComponentError] = useState<HttpError | null>(null);

  const handleComponentError = async (error: any, config?: Partial<ErrorHandlingConfig>) => {
    const parsedError = ErrorHandlerService.parseError(error);
    
    // Add component context to error
    const enhancedError = {
      ...parsedError,
      operation: parsedError.operation || componentName
    };

    setComponentError(enhancedError);
    await handleError(enhancedError, config);
  };

  const clearComponentError = () => {
    setComponentError(null);
    clearError();
  };

  return {
    error: componentError || lastError,
    handleError: handleComponentError,
    clearError: clearComponentError,
    hasError: !!(componentError || lastError)
  };
};

/**
 * Hook for async operations with automatic error handling
 */
export const useAsyncWithError = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  errorConfig?: Partial<ErrorHandlingConfig>
) => {
  const { handleError } = useError();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<HttpError | null>(null);
  const [data, setData] = useState<R | null>(null);

  const execute = async (...args: T): Promise<R> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await operation(...args);
      setData(result);
      
      return result;
    } catch (err) {
      const parsedError = ErrorHandlerService.parseError(err);
      setError(parsedError);
      
      // Handle error through context
      await handleError(err, errorConfig);
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const retry = async (...args: T): Promise<R> => {
    return execute(...args);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    execute,
    retry,
    isLoading,
    error,
    data,
    clearError,
    hasError: !!error
  };
};

/**
 * Hook for error boundary integration
 */
export const useErrorBoundary = () => {
  const { handleError } = useError();

  const captureError = (error: Error, errorInfo?: any) => {
    const enhancedError = {
      ...error,
      componentStack: errorInfo?.componentStack,
      errorBoundary: true
    };

    handleError(enhancedError, {
      severity: 'critical',
      showAlert: true,
      logError: true
    });
  };

  return { captureError };
};

export default ErrorContext;
