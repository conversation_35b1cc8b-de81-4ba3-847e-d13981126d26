/**
 * Mock student data for the student selection screen
 * Based on Figma design specifications
 */

export interface Student {
  id: string;
  name: string;
  age: number;
  grade: string;
  avatar: string;
  school: string;
  profileImage?: string;
}

export const mockStudents: Student[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 30,
    grade: 'Grado Once A',
    avatar: 'MC',
    school: 'Colegio Santillana',
    profileImage: '/assets/images/avatar.png'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    age: 10,
    grade: 'Grado Quinto B',
    avatar: 'JM',
    school: 'Colegio Santillana',
    profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: '3',
    name: '<PERSON>',
    age: 14,
    grade: 'Grado Noveno C',
    avatar: 'CA',
    school: 'Colegio Santillana',
    profileImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face'
  }
];

/**
 * Get student by ID
 */
export const getStudentById = (id: string): Student | undefined => {
  return mockStudents.find(student => student.id === id);
};

/**
 * Get all students
 */
export const getAllStudents = (): Student[] => {
  return mockStudents;
};

/**
 * Store selected student in localStorage
 */
export const setSelectedStudent = (studentId: string): void => {
  localStorage.setItem('selectedStudentId', studentId);
};

/**
 * Get selected student from localStorage
 */
export const getSelectedStudent = (): Student | null => {
  const studentId = localStorage.getItem('selectedStudentId');
  if (studentId) {
    return getStudentById(studentId) || null;
  }
  return null;
};

/**
 * Clear selected student
 */
export const clearSelectedStudent = (): void => {
  localStorage.removeItem('selectedStudentId');
};
