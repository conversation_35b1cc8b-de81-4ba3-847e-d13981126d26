/**
 * OIDC debugging utilities to help diagnose state management issues
 */



/**
 * Inspect OIDC storage for debugging purposes
 */
export const inspectOIDCStorage = () => {
  console.log("=== OIDC Storage Inspection ===");

  // Check localStorage for OIDC data
  const localStorageKeys = Object.keys(localStorage).filter(key => key.includes('oidc'));
  console.log("LocalStorage OIDC keys:", localStorageKeys);

  localStorageKeys.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      console.log(`LocalStorage[${key}]:`, value ? JSON.parse(value) : value);
    } catch (error) {
      console.log(`LocalStorage[${key}] (raw):`, localStorage.getItem(key));
    }
  });

  // Check sessionStorage for OIDC data
  const sessionStorageKeys = Object.keys(sessionStorage).filter(key => key.includes('oidc'));
  console.log("SessionStorage OIDC keys:", sessionStorageKeys);

  sessionStorageKeys.forEach(key => {
    try {
      const value = sessionStorage.getItem(key);
      console.log(`SessionStorage[${key}]:`, value ? JSON.parse(value) : value);
    } catch (error) {
      console.log(`SessionStorage[${key}] (raw):`, sessionStorage.getItem(key));
    }
  });

  console.log("=== End OIDC Storage Inspection ===");
};

/**
 * Clear all OIDC storage for debugging
 */
export const clearOIDCStorage = () => {
  console.log("Clearing all OIDC storage...");

  // Clear localStorage OIDC data
  Object.keys(localStorage).filter(key => key.includes('oidc')).forEach(key => {
    localStorage.removeItem(key);
    console.log(`Removed from localStorage: ${key}`);
  });

  // Clear sessionStorage OIDC data
  Object.keys(sessionStorage).filter(key => key.includes('oidc')).forEach(key => {
    sessionStorage.removeItem(key);
    console.log(`Removed from sessionStorage: ${key}`);
  });

  console.log("OIDC storage cleared");
};

/**
 * Log current URL parameters for debugging
 */
export const logUrlParameters = () => {
  const url = new URL(window.location.href);
  console.log("Current URL:", window.location.href);
  console.log("URL Parameters:", Object.fromEntries(url.searchParams.entries()));
  console.log("URL Hash:", url.hash);
};

/**
 * Validate OIDC callback URL
 */
export const validateCallbackUrl = () => {
  const url = new URL(window.location.href);
  const params = Object.fromEntries(url.searchParams.entries());
  
  console.log("=== OIDC Callback URL Validation ===");
  console.log("Full URL:", window.location.href);
  console.log("Parameters:", params);

  const validationResults = {
    hasCode: !!params.code,
    hasState: !!params.state,
    hasError: !!params.error,
    codeLength: params.code?.length || 0,
    stateLength: params.state?.length || 0,
    errorDescription: params.error_description
  };

  console.log("Validation Results:", validationResults);

  if (params.error) {
    console.log("❌ OIDC Error detected:", {
      error: params.error,
      error_description: params.error_description,
      error_uri: params.error_uri
    });
  }

  if (!params.code && !params.error) {
    console.log("⚠️ No authorization code or error found in callback URL");
  }

  if (!params.state) {
    console.log("⚠️ No state parameter found in callback URL");
  }

  console.log("=== End OIDC Callback URL Validation ===");
  
  return validationResults;
};

/**
 * Check if we're in a callback scenario
 */
export const isOIDCCallback = (): boolean => {
  const url = new URL(window.location.href);
  const hasCode = url.searchParams.has('code');
  const hasState = url.searchParams.has('state');
  const hasError = url.searchParams.has('error');
  const isCallbackPath = url.pathname === '/callback';
  
  return isCallbackPath && (hasCode || hasError) && hasState;
};

/**
 * Get OIDC configuration summary for debugging
 */
export const getOIDCConfigSummary = () => {
  return {
    currentUrl: window.location.href,
    isCallback: isOIDCCallback(),
    storagePrefix: {
      user: "oidc.santillana.user.",
      state: "oidc.santillana.state."
    },
    redirectUris: {
      callback: `${window.location.origin}/callback`,
      logout: `${window.location.origin}/logout`,
      silentRefresh: `${window.location.origin}/silent-refresh`
    }
  };
};
