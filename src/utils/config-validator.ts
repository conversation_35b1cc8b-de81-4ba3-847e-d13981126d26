/**
 * Configuration validator for OIDC authentication
 * Validates redirect URIs, client configuration, and environment settings
 */

import { Capacitor } from '@capacitor/core';
import { environmentConfig, redirectUris, platformInfo } from '../config/environment.config';
import { debugLog } from '../config/environment.config';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendations: string[];
}

export class ConfigValidator {
  /**
   * Validate complete OIDC configuration
   */
  static validateConfiguration(): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      recommendations: []
    };

    // Validate environment configuration
    this.validateEnvironmentConfig(result);
    
    // Validate redirect URIs
    this.validateRedirectUris(result);
    
    // Validate platform-specific settings
    this.validatePlatformConfig(result);
    
    // Validate Santillana Connect requirements
    this.validateSantillanaConnectRequirements(result);

    result.isValid = result.errors.length === 0;
    
    debugLog('ConfigValidator - Validation result:', result);
    return result;
  }

  /**
   * Validate environment configuration
   */
  private static validateEnvironmentConfig(result: ValidationResult) {
    // Check authority URL
    if (!environmentConfig.authority) {
      result.errors.push('OIDC authority URL is not configured');
    } else if (!environmentConfig.authority.startsWith('https://')) {
      result.errors.push('OIDC authority must use HTTPS');
    }

    // Check client ID
    if (!environmentConfig.clientId) {
      result.errors.push('OIDC client ID is not configured');
    }

    // Check scope
    if (!environmentConfig.scope) {
      result.errors.push('OIDC scope is not configured');
    } else {
      const requiredScopes = ['openid', 'profile', 'email'];
      const configuredScopes = environmentConfig.scope.split(' ');
      
      for (const requiredScope of requiredScopes) {
        if (!configuredScopes.includes(requiredScope)) {
          result.warnings.push(`Missing recommended scope: ${requiredScope}`);
        }
      }
    }

    // Check base URLs
    if (Capacitor.isNativePlatform()) {
      if (!environmentConfig.nativeBaseUrl) {
        result.errors.push('Native base URL is not configured');
      } else if (!environmentConfig.nativeBaseUrl.startsWith('capacitor://')) {
        result.errors.push('Native base URL must use capacitor:// scheme');
      }
    } else {
      if (!environmentConfig.devBaseUrl) {
        result.warnings.push('Development base URL is not configured');
      }
    }
  }

  /**
   * Validate redirect URIs
   */
  private static validateRedirectUris(result: ValidationResult) {
    const platform = Capacitor.getPlatform();
    
    // Check callback URI
    if (!redirectUris.callback) {
      result.errors.push('Callback redirect URI is not configured');
    } else {
      if (platform === 'web') {
        if (!redirectUris.callback.startsWith('http://localhost')) {
          result.warnings.push('Web callback URI should use http://localhost for development');
        }
      } else {
        if (!redirectUris.callback.startsWith('capacitor://localhost')) {
          result.errors.push('Native callback URI must use capacitor://localhost scheme');
        }
      }
    }

    // Check logout URI
    if (!redirectUris.postLogout) {
      result.warnings.push('Post-logout redirect URI is not configured');
    }

    // Check silent refresh URI
    if (!redirectUris.silentRefresh) {
      result.warnings.push('Silent refresh redirect URI is not configured');
    }

    // Validate URI format
    try {
      new URL(redirectUris.callback);
    } catch (error) {
      result.errors.push('Callback redirect URI is not a valid URL');
    }
  }

  /**
   * Validate platform-specific configuration
   */
  private static validatePlatformConfig(result: ValidationResult) {
    const platform = Capacitor.getPlatform();

    if (platform === 'android') {
      // Android-specific validations
      result.recommendations.push('Ensure AndroidManifest.xml includes deep link intent filter for capacitor://localhost');
      result.recommendations.push('Verify android:launchMode="singleTask" is set for MainActivity');
    } else if (platform === 'ios') {
      // iOS-specific validations
      result.recommendations.push('Ensure Info.plist includes URL scheme for capacitor://localhost');
    } else if (platform === 'web') {
      // Web-specific validations
      if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
        result.warnings.push('HTTPS is recommended for production web deployments');
      }
    }
  }

  /**
   * Validate Santillana Connect specific requirements
   */
  private static validateSantillanaConnectRequirements(result: ValidationResult) {
    // Check authority matches Santillana Connect
    if (!environmentConfig.authority.includes('santillanaconnect.com')) {
      result.warnings.push('Authority does not appear to be Santillana Connect');
    }

    // Check client ID format
    if (!environmentConfig.clientId.includes('sumun') && !environmentConfig.clientId.includes('santillana')) {
      result.warnings.push('Client ID does not appear to be for Santillana Connect');
    }

    // Check required scopes for Santillana Connect
    const santillanaScopes = ['neds/full_access', 'offline_access'];
    const configuredScopes = environmentConfig.scope.split(' ');
    
    for (const santillanaScope of santillanaScopes) {
      if (!configuredScopes.includes(santillanaScope)) {
        result.warnings.push(`Missing Santillana Connect scope: ${santillanaScope}`);
      }
    }

    // Validate redirect URIs match Santillana Connect requirements
    const expectedRedirectUris = [
      'http://localhost:5173/callback',
      'http://localhost:5173/silent-refresh',
      'capacitor://localhost/callback'
    ];

    const currentUri = redirectUris.callback;
    if (!expectedRedirectUris.some(uri => currentUri.includes(uri.split('/').pop() || ''))) {
      result.recommendations.push('Verify redirect URIs are registered in Santillana Connect');
    }
  }

  /**
   * Get configuration summary for debugging
   */
  static getConfigurationSummary(): any {
    return {
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      environment: {
        authority: environmentConfig.authority,
        clientId: environmentConfig.clientId,
        scope: environmentConfig.scope,
        debugAuth: environmentConfig.debugAuth
      },
      redirectUris: redirectUris,
      platformInfo: platformInfo,
      userAgent: navigator.userAgent,
      currentUrl: window.location.href,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Validate configuration and log results
   */
  static validateAndLog(): ValidationResult {
    const result = this.validateConfiguration();
    
    debugLog('ConfigValidator - Configuration Summary:', this.getConfigurationSummary());
    
    if (result.errors.length > 0) {
      console.error('❌ Configuration Errors:', result.errors);
    }
    
    if (result.warnings.length > 0) {
      console.warn('⚠️ Configuration Warnings:', result.warnings);
    }
    
    if (result.recommendations.length > 0) {
      console.info('💡 Configuration Recommendations:', result.recommendations);
    }
    
    if (result.isValid) {
      debugLog('✅ Configuration validation passed');
    } else {
      debugLog('❌ Configuration validation failed');
    }
    
    return result;
  }
}
