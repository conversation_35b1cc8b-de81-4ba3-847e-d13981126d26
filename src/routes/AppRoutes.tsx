import React, { useEffect, useState } from "react";
import { IonRouterOutlet, IonTabs, IonPage, IonContent } from '@ionic/react';

// Import custom TabBar components
import AnimatedTabBar from '../components/TabBar/AnimatedTabBar';
import { Route, Redirect, useHistory, useLocation } from "react-router-dom";
import RouteGuard from "./RouteGuard";
import { useAuth } from 'react-oidc-context';
import { AuthStorageService } from "../services/auth-storage.service";
import { debugLog, userManager } from "../config/user-manager.config";
import { environmentConfig } from "../config/environment.config";
import { inspectOIDCStorage, validateCallbackUrl, logUrlParameters, clearOIDCStorage } from "../utils/oidc-debug";
import { LoadingService } from '../services/loading.service';
import { ROUTES } from './routes';
import StudentSelectionPage from '../pages/StudentSelection/StudentSelectionPage';
import AuthPage from '../pages/Auth/AuthPage';
import Tab1 from "../pages/HomePage/Tab1";
import ReportsPage from "../pages/HomePage/ReportsPage";
import ConnectionPage from "../pages/ConnectionPage/ConnectionPage";
import PortfolioPage from "../pages/PortfolioPage/PortfolioPage";
import SchedulePage from "../pages/SchedulePage/SchedulePage";
import CatalogPage from "../pages/CatalogPage/CatalogPage";
import AccountPage from "../pages/AccountPage/AccountPage";
import NotificationsPage from "../pages/NotificationsPage/NotificationsPage";
import AuthErrorBoundary from '../components/ErrorBoundary/AuthErrorBoundary';

import '../components/TabBar/FigmaTabBar.css';

// OIDC Callback page - handles return from Santillana Connect
const CallbackPage: React.FC = () => {
  const history = useHistory();
  const location = useLocation();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      // Show loading immediately
      const loadingId = LoadingService.showLoading('callback', 'Completando autenticación...');

      try {
        debugLog("CallbackPage - Starting OIDC callback processing");

        // Debug current state
        logUrlParameters();
        inspectOIDCStorage();
        validateCallbackUrl();

        // Check if we have the authorization code in the URL
        const urlParams = new URLSearchParams(location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
          debugLog("CallbackPage - OIDC error in URL parameters", { error, error_description: urlParams.get('error_description') });
          LoadingService.hideLoading(loadingId);
          setError(`Error de autenticación: ${error}`);
          return;
        }

        if (!code) {
          debugLog("CallbackPage - No authorization code found in URL");
          LoadingService.hideLoading(loadingId);
          setError("No se encontró el código de autorización en la respuesta");
          return;
        }

        if (!state) {
          debugLog("CallbackPage - No state parameter found in URL");
          LoadingService.hideLoading(loadingId);
          setError("No se encontró el parámetro de estado en la respuesta");
          return;
        }

        debugLog("CallbackPage - Found authorization code and state, processing...", {
          hasCode: !!code,
          hasState: !!state,
          codeLength: code?.length,
          stateLength: state?.length
        });

        // Use UserManager directly to handle the callback
        // This should properly match the state and exchange the code for tokens
        const user = await userManager.signinRedirectCallback();

        debugLog("CallbackPage - OIDC callback processed successfully", {
          userId: user.profile?.sub,
          userName: user.profile?.name,
          userEmail: user.profile?.email,
          hasAccessToken: !!user.access_token,
          hasRefreshToken: !!user.refresh_token
        });

        // Store tokens for native apps
        await AuthStorageService.storeTokens(user);

        // Hide loading and redirect
        LoadingService.hideLoading(loadingId);
        debugLog("CallbackPage - Redirecting to student selection");
        history.replace(ROUTES.STUDENT_SELECTION);

      } catch (error: any) {
        console.error("CallbackPage - Error processing OIDC callback:", error);
        debugLog("CallbackPage - OIDC callback error", {
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack
        });

        // Hide loading on error
        LoadingService.hideLoading(loadingId);

        // Use enhanced error handling
        const { AuthErrorHandler } = await import('../utils/auth-error-handler');
        const authError = AuthErrorHandler.parseError(error);

        debugLog("CallbackPage - Parsed authentication error:", authError);

        // Provide specific error messages
        setError(authError.userMessage);

        // Log debug information for troubleshooting
        if (environmentConfig.debugAuth) {
          console.error("CallbackPage - Debug info:", AuthErrorHandler.getDebugInfo(authError));

          // For state mismatch errors, inspect storage
          if (authError.code === 'STATE_MISMATCH') {
            inspectOIDCStorage();
          }
        }
      }
    };

    // Only process if we don't have an error
    if (!error) {
      processCallback();
    }
  }, [location, history, error]);

  if (error) {
    return (
      <IonPage>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="text-[var(--ion-color-danger)] mb-4">
              <svg className="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2 text-[var(--ion-text-color)]">Error de Autenticación</h2>
            <p className="text-[var(--ion-color-medium)] mb-4">{error}</p>
            <div className="space-y-2">
              <button
                onClick={() => {
                  debugLog("CallbackPage - Retry authentication");
                  history.replace(ROUTES.AUTH);
                }}
                className="bg-[var(--ion-color-primary)] hover:bg-[var(--ion-color-primary-shade)] text-white px-6 py-2 rounded-lg transition-colors mr-2"
              >
                Intentar de nuevo
              </button>
              {error?.includes("estado") && (
                <button
                  onClick={() => {
                    debugLog("CallbackPage - Clearing OIDC storage and retrying");
                    clearOIDCStorage();
                    history.replace(ROUTES.AUTH);
                  }}
                  className="bg-[var(--ion-color-warning)] hover:bg-[var(--ion-color-warning-shade)] text-white px-6 py-2 rounded-lg transition-colors"
                >
                  Limpiar datos y reintentar
                </button>
              )}
            </div>
          </div>
        </IonContent>
      </IonPage>
    );
  }

  // LoadingService handles the loading display, so we just return an empty page
  // The loading overlay will be shown globally
  return (
    <IonPage>
      <IonContent>
        {/* Empty content - LoadingService overlay handles the loading display */}
      </IonContent>
    </IonPage>
  );
};

// Silent refresh page for token renewal
const SilentRefreshPage: React.FC = () => {
  useEffect(() => {
    debugLog("SilentRefreshPage - Processing silent refresh");
  }, []);

  return (
    <IonPage>
      <IonContent>
        <div style={{ display: 'none' }}>
          Renovando tokens de forma silenciosa...
        </div>
      </IonContent>
    </IonPage>
  );
};

// Logout page for handling post-logout redirect
const LogoutPage: React.FC = () => {
  const auth = useAuth();
  const [isProcessed, setIsProcessed] = useState(false);

  useEffect(() => {
    if (isProcessed) return;

    const handleLogout = async () => {
      try {
        setIsProcessed(true);
        debugLog("LogoutPage - Processing logout...");

        await auth.removeUser();
        await AuthStorageService.clearTokens();

        debugLog("LogoutPage - Logout completed, tokens cleared");

        setTimeout(() => {
          auth.signinRedirect().catch(console.error);
        }, 2000);

      } catch (error) {
        console.error("❌ LogoutPage - Error during logout:", error);
        setTimeout(() => {
          auth.signinRedirect().catch(console.error);
        }, 2000);
      }
    };

    handleLogout();
  }, [auth, isProcessed]);

  return (
    <IonPage>
      <IonContent className="ion-padding">
        <div className="flex flex-col items-center justify-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--ion-color-danger)] mb-4"></div>
          <p className="text-lg mb-2 text-[var(--ion-text-color)]">Cerrando sesión...</p>
          <p className="text-sm text-[var(--ion-color-medium)]">Serás redirigido al login automáticamente</p>
        </div>
      </IonContent>
    </IonPage>
  );
};

const AppRoutes: React.FC = () => {
  return (
    <IonRouterOutlet>
      {/* Student Selection - replaces intro slides */}
      <Route exact path={ROUTES.STUDENT_SELECTION}>
        <RouteGuard>
          <StudentSelectionPage />
        </RouteGuard>
      </Route>

      {/* Authentication routes wrapped with error boundary */}
      <Route exact path={ROUTES.AUTH}>
        <AuthErrorBoundary>
          <AuthPage />
        </AuthErrorBoundary>
      </Route>

      {/** OIDC callback route (after login) */}
      <Route exact path="/callback">
        <AuthErrorBoundary>
          <CallbackPage />
        </AuthErrorBoundary>
      </Route>

      {/** Silent refresh route (for token renewal) */}
      <Route exact path="/silent-refresh" component={SilentRefreshPage} />

      {/** Post-logout redirect route */}
      <Route exact path="/logout" component={LogoutPage} />

      {/* Account page - Protected route outside of tabs */}
      <Route exact path={ROUTES.ACCOUNT}>
        <RouteGuard>
          <AccountPage />
        </RouteGuard>
      </Route>

      {/* Notifications page - Protected route outside of tabs */}
      <Route exact path={ROUTES.NOTIFICATIONS}>
        <RouteGuard>
          <NotificationsPage />
        </RouteGuard>
      </Route>

      {/* Protected routes - all require OIDC authentication */}
      <Route path={ROUTES.HOME}>
        <RouteGuard>
          <IonTabs>
            <IonRouterOutlet>
              {/* Tab sub-routes */}
              <Route exact path="/tabs/inicio" component={Tab1} />
              <Route exact path="/tabs/informes" component={ReportsPage} />
              <Route exact path="/tabs/conexion" component={ConnectionPage} />
              <Route exact path="/tabs/portfolio" component={PortfolioPage} />
              <Route exact path="/tabs/horarios" component={SchedulePage} />
              <Route exact path="/tabs/catalogo" component={CatalogPage} />

              {/* Legacy routes for backward compatibility */}
              <Route exact path="/tabs/tab1"><Redirect to="/tabs/inicio" /></Route>
              <Route exact path="/tabs/tab2"><Redirect to="/tabs/informes" /></Route>
              <Route exact path="/tabs/tab3"><Redirect to="/tabs/conexion" /></Route>
              <Route exact path="/tabs/tab4"><Redirect to="/tabs/portfolio" /></Route>
              <Route exact path="/tabs/tab5"><Redirect to="/tabs/horarios" /></Route>
              <Route exact path="/tabs/tab6"><Redirect to="/tabs/catalogo" /></Route>

              {/* Default tab redirect */}
              <Route exact path={ROUTES.HOME}>
                <Redirect to="/tabs/inicio" />
              </Route>
            </IonRouterOutlet>

            {/* Animated Tab Bar */}
            <AnimatedTabBar />
          </IonTabs>
        </RouteGuard>
      </Route>

      {/* Ruta por defecto */}
      <Route exact path="/">
        <Redirect to={ROUTES.STUDENT_SELECTION} />
      </Route>
    </IonRouterOutlet>
  );
};

export default AppRoutes;
