import React, { useEffect } from 'react';
import { Redirect, useLocation, useHistory } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { useUser } from '../contexts/UserContext';
import { debugLog } from '../config/user-manager.config';
import { ROUTES } from './routes';
import { Capacitor } from '@capacitor/core';

interface RouteGuardProps {
  children: React.ReactNode;
}

const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const auth = useAuth();
  const { user, isAuthenticated, isLoading } = useUser();
  const location = useLocation();
  const history = useHistory();

  useEffect(() => {
    // Check if we're authenticated but on the auth page
    if (isAuthenticated && location.pathname === ROUTES.AUTH) {
      debugLog('RouteGuard - User already authenticated, redirecting to student selection');
      history.replace(ROUTES.STUDENT_SELECTION);
    }
    
    // Add debug logging to track authentication state and redirects
    debugLog('RouteGuard - Current state:', {
      isAuthenticated,
      pathname: location.pathname,
      isLoading,
      hasUser: !!user
    });
  }, [isAuthenticated, location.pathname, history, isLoading, user]);

  // Show loading while OIDC authentication is initializing
  if (isLoading) {
    debugLog('RouteGuard - Loading state, waiting for OIDC authentication');
    return null; // or a loading spinner
  }

  // If user is not authenticated, redirect to auth
  if (!isAuthenticated && location.pathname !== ROUTES.AUTH) {
    debugLog('RouteGuard - User not authenticated, redirecting to auth', {
      isOIDCAuth: auth.isAuthenticated,
      isUserContextAuth: isAuthenticated,
      hasUser: !!user,
      isNative: Capacitor.isNativePlatform()
    });
    return <Redirect to={ROUTES.AUTH} />;
  }

  // If user is authenticated but trying to access main tabs without selecting a student
  const selectedStudentId = localStorage.getItem('selectedStudentId');
  if (isAuthenticated && !selectedStudentId && location.pathname.startsWith('/tabs')) {
    debugLog('RouteGuard - User authenticated but no student selected, redirecting to student selection');
    return <Redirect to={ROUTES.STUDENT_SELECTION} />;
  }

  // Usuario autenticado - permitir acceso
  debugLog('RouteGuard - User authenticated, allowing access', {
    userName: user?.name,
    userEmail: user?.email,
    authMethod: Capacitor.isNativePlatform() ? 'Capacitor' : 'OIDC'
  });

  return <>{children}</>;
};

export default RouteGuard;
