/* For information on how to create your own theme, please see:
http://ionicframework.com/docs/theming/ */



@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme) important;
@import "tailwindcss/utilities.css" layer(utilities) important;

@utility container {
  margin-inline: auto;
}

/* Custom Tailwind Component Classes for Reusable Patterns */
@layer components {
  /* Unified Card Design System - Following AttendanceCard Reference */
  .card-base {
    @apply mb-6 rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl;
    --background: var(--ion-card-background);
    --color: var(--ion-text-color);
    border: none;
    margin: 0;
  }

  .card-header-unified {
    padding: 1rem 1rem 0 1rem;
    --background: transparent;
    --color: inherit;
  }

  .card-content-unified {
    padding: 0 1rem 1rem 1rem;
    --background: transparent;
    --color: inherit;
  }

  .card-title-unified {
    color: var(--ion-text-color);
    font-size: 0.875rem;
    font-weight: 700;
    line-height: 1.21;
    margin: 0;
  }

  .card-description-unified {
    color: var(--ion-color-medium);
    font-size: 0.625rem;
    line-height: 1.21;
    margin: 0;
  }

  .card-button-unified {
    --color: var(--ion-color-primary);
    --color-hover: var(--ion-color-primary-shade);
    margin: 0;
    padding: 0;
  }

  .card-header-gradient {
    @apply relative overflow-hidden rounded-t-2xl p-6;
  }

  /* Modular Card System - Consistent Margins */
  .modular-card {
    @apply rounded-2xl shadow-lg transition-all duration-300 ease-in-out hover:-translate-y-0.5 hover:shadow-xl;
    --background: var(--ion-card-background);
    --color: var(--ion-text-color);
    border: none;
    margin-bottom: 1.5rem; /* 24px consistent spacing */
  }

  .modular-card:last-child {
    margin-bottom: 0; /* Remove margin from last card */
  }

  /* Button Components */
  .btn-icon-round {
    @apply w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out;
  }

  .btn-icon-small {
    @apply w-7 h-7 rounded-full flex items-center justify-center transition-all duration-300 ease-in-out;
  }

  /* Typography Components */
  .text-figma-small {
    @apply text-[8px] leading-[1.2] text-[var(--ion-color-medium)] font-normal;
  }

  .text-figma-body {
    @apply text-sm leading-relaxed text-[var(--ion-text-color)] font-normal;
  }

  .text-figma-title {
    @apply text-lg font-semibold text-[var(--ion-text-color)] m-0;
  }

  /* Layout Components */
  .flex-center {
    @apply flex items-center justify-center;
  }



  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-start;
  }

  /* Loading Skeleton Components */
  .skeleton-title {
    @apply animate-pulse rounded h-6 mb-4;
  }

  .skeleton-subtitle {
    @apply animate-pulse rounded h-4 mb-6 w-3/4;
  }

  .skeleton-content {
    @apply animate-pulse rounded-xl h-20 mb-4;
  }

  /* Responsive Text Sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-[9px];
  }

  .text-responsive-sm {
    @apply text-sm sm:text-[13px];
  }

  .text-responsive-base {
    @apply text-base sm:text-sm;
  }

  /* Icon Sizes */
  .icon-xs {
    @apply w-4 h-4;
  }

  .icon-sm {
    @apply w-5 h-5;
  }

  .icon-md {
    @apply w-6 h-6;
  }

  .icon-lg {
    @apply w-8 h-8;
  }

  /* Spacing Utilities */
  .gap-figma {
    @apply gap-4;
  }

  .gap-figma-sm {
    @apply gap-2;
  }

  .gap-figma-lg {
    @apply gap-6;
  }

  /* Professional Transitions */
  .transition-smooth {
    @apply transition-all duration-300 ease-in-out;
  }

  .transition-fast {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Hover Effects */
  .hover-lift {
    @apply hover:-translate-y-0.5 hover:shadow-lg;
  }

  .hover-scale {
    @apply hover:scale-105;
  }
}

:root {
  /* Update Ionic colors to match Figma design system */
  --ion-color-primary: #35A192;
  --ion-color-primary-rgb: 53, 161, 146;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #2e8a7a;
  --ion-color-primary-tint: #49aa9d;

  --ion-color-secondary: #45B2FF;
  --ion-color-secondary-rgb: 69, 178, 255;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #2f8e80;
  --ion-color-secondary-tint: #49aa9d;

  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;
  --ion-text-color: #000000;
  --ion-text-color-rgb: 0, 0, 0;
  --ion-border-color: #d7d8da;

  /* Enhanced theme variables for better dark mode support */
  --ion-card-background: #ffffff;
  --ion-toolbar-background: #ffffff;
  --ion-item-background: #ffffff;

  /* Transition variables */
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: ease;

  /* Override Ionic text colors to match Figma design */
  --ion-text-color: #4E4E4E;
  --ion-text-color-rgb: 78, 78, 78;

  /* Background colors to match Figma */
  --ion-background-color: #FFFFFF;
  --ion-background-color-rgb: 255, 255, 255;
  --ion-card-background: #F9F9F9;
  --ion-toolbar-background: #FFFFFF;
  --ion-item-background: #FFFFFF;

  /* Border colors to match Figma */
  --ion-border-color: #DFDFDF;
}


.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary);
  --ion-color-base-rgb: var(--ion-color-secondary-rgb);
  --ion-color-contrast: var(--ion-color-secondary-contrast);
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb);
  --ion-color-shade: var(--ion-color-secondary-shade);
  --ion-color-tint: var(--ion-color-secondary-tint);
}

/* Dark mode variables are now handled automatically by Ionic's built-in dark mode system */
/* Using @ionic/react/css/palettes/dark.class.css in App.tsx */
/* All dark mode styling is handled through CSS variables that adapt automatically */

/* Global smooth transitions for theme changes */
* {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Disable transitions during theme initialization to prevent flash */
.theme-transitioning,
.theme-transitioning * {
  transition: none !important;
}

/* Enhanced component theming */
ion-card {
  transition:
    background var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-toolbar {
  --background: var(--ion-toolbar-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-item {
  --background: var(--ion-item-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Improved focus styles for accessibility */
ion-button:focus,
ion-toggle:focus,
ion-input:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Fix for iOS list background - make it transparent to inherit card background */
.list-ios {
  background: transparent !important;
}

ion-list {
  background: transparent !important;
}

/* Ensure items also inherit the card background properly */
ion-item {
  --background: transparent;
}

/* Ensure ion-tabs uses the correct background for dark mode */
ion-tabs {
  --background: var(--ion-background-color);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}