/* Loading System - Design System Integration */

/* Modern Loading Overlay Enhancements */
.loading-overlay {
  --background: rgba(255, 255, 255, 0.85);
  --backdrop-filter: blur(16px) saturate(180%);
  --border-radius: 16px;
  --box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  --border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode loading overlay */
.ion-palette-dark .loading-overlay {
  --background: rgba(28, 28, 30, 0.85);
  --backdrop-filter: blur(20px) saturate(160%);
  --border: 1px solid rgba(255, 255, 255, 0.1);
  --box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Enhanced IonLoading styling */
.loading-overlay .loading-wrapper {
  background: var(--background);
  backdrop-filter: var(--backdrop-filter);
  -webkit-backdrop-filter: var(--backdrop-filter);
  border-radius: var(--border-radius);
  border: var(--border);
  box-shadow: var(--box-shadow);
  padding: 2rem 1.5rem;
  min-width: 140px;
  max-width: 280px;
}

/* Loading spinner enhancements */
.loading-overlay .loading-spinner {
  --color: var(--ion-color-primary);
  width: 32px;
  height: 32px;
  margin-bottom: 1rem;
}

/* Loading message styling */
.loading-overlay .loading-content {
  color: var(--ion-text-color);
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.4;
  opacity: 0.9;
}

/* Gradient Background Integration */
.loading-with-gradient {
  background: linear-gradient(
    135deg,
    rgba(53, 161, 146, 0.1) 0%,
    rgba(69, 178, 255, 0.1) 100%
  );
}

.ion-palette-dark .loading-with-gradient {
  background: linear-gradient(
    135deg,
    rgba(53, 161, 146, 0.05) 0%,
    rgba(69, 178, 255, 0.05) 100%
  );
}

/* Progress Indicator Styles */
.loading-progress-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.loading-progress-ring {
  position: absolute;
  width: 48px;
  height: 48px;
  transform: rotate(-90deg);
}

.loading-progress-ring svg {
  width: 100%;
  height: 100%;
}

.loading-progress-ring .progress-bg {
  fill: none;
  stroke: var(--ion-color-light);
  stroke-width: 2;
  opacity: 0.3;
}

.loading-progress-ring .progress-fill {
  fill: none;
  stroke: var(--ion-color-primary);
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smart Loading States */
.loading-smart-fade {
  animation: smart-fade-in 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.loading-smart-fade.fade-out {
  animation: smart-fade-out 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes smart-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(8px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes smart-fade-out {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-8px);
  }
}

/* TabBar Integration */
.loading-overlay-with-tabbar {
  bottom: calc(var(--ion-tab-bar-height, 56px) + env(safe-area-inset-bottom));
}

/* Header Integration */
.loading-overlay-with-header {
  top: calc(var(--ion-header-height, 56px) + env(safe-area-inset-top));
}

/* Z-Index Management */
.loading-overlay {
  z-index: 20000;
}

.loading-overlay-critical {
  z-index: 25000;
}

.loading-overlay-modal {
  z-index: 30000;
}

/* Mobile Optimizations */
@media (max-width: 640px) {
  .loading-overlay .loading-wrapper {
    padding: 1.5rem 1.25rem;
    min-width: 120px;
    max-width: 240px;
  }
  
  .loading-overlay .loading-spinner {
    width: 28px;
    height: 28px;
  }
  
  .loading-overlay .loading-content {
    font-size: 0.8125rem;
  }
}

@media (max-width: 480px) {
  .loading-overlay .loading-wrapper {
    padding: 1.25rem 1rem;
    min-width: 100px;
    max-width: 200px;
  }
  
  .loading-overlay {
    --backdrop-filter: blur(12px) saturate(160%);
  }
}

/* Performance Optimizations */
.loading-overlay,
.loading-overlay .loading-wrapper {
  will-change: opacity, transform;
}

.loading-overlay .loading-spinner {
  will-change: transform;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .loading-smart-fade,
  .loading-smart-fade.fade-out {
    animation: none;
  }
  
  .loading-overlay .loading-spinner {
    animation-duration: 2s;
  }
  
  .loading-overlay {
    --backdrop-filter: none;
  }
}

@media (prefers-contrast: high) {
  .loading-overlay {
    --background: var(--ion-background-color);
    --border: 2px solid var(--ion-text-color);
    --box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    --backdrop-filter: none;
  }
}

/* Focus Management */
.loading-overlay:focus {
  outline: none;
}

.loading-overlay .loading-wrapper:focus-within {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Screen Reader Support */
.loading-overlay[aria-hidden="true"] {
  pointer-events: none;
}

/* Loading State Transitions */
.content-loading-enter {
  opacity: 0;
  transform: translateY(16px);
}

.content-loading-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-loading-exit {
  opacity: 1;
  transform: translateY(0);
}

.content-loading-exit-active {
  opacity: 0;
  transform: translateY(-16px);
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Micro-interactions */
.loading-pulse {
  animation: loading-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Loading Button States */
.loading-button {
  position: relative;
  overflow: hidden;
}

.loading-button.is-loading {
  color: transparent;
  pointer-events: none;
}

.loading-button.is-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: loading-button-spin 0.8s linear infinite;
}

@keyframes loading-button-spin {
  to {
    transform: rotate(360deg);
  }
}
