/* Mobile Loading Optimizations - 60fps Performance & Integration */

/* Hardware acceleration for smooth animations */
.modern-loading-overlay,
.skeleton-loader-wrapper,
.loading-button,
.progress-circular-svg,
.progress-minimal-fill {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* GPU-accelerated animations */
.modern-loading-spinner,
.skeleton-shimmer,
.loading-button-spinner {
  will-change: transform;
  transform: translateZ(0);
}

/* Optimized backdrop filters for mobile */
@supports (backdrop-filter: blur(16px)) {
  .modern-loading-backdrop {
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
  }
}

@supports not (backdrop-filter: blur(16px)) {
  .modern-loading-backdrop {
    background: rgba(0, 0, 0, 0.3);
  }
  
  .ion-palette-dark .modern-loading-backdrop {
    background: rgba(0, 0, 0, 0.5);
  }
}

/* TabBar Integration - Floating Appearance */
.loading-overlay-with-floating-tabbar {
  bottom: calc(var(--ion-tab-bar-height, 56px) + 16px + env(safe-area-inset-bottom));
}

.modern-loading-overlay.with-floating-tabbar .modern-loading-content {
  margin-bottom: calc(var(--ion-tab-bar-height, 56px) + 32px);
}

/* Header Glass Effects Integration */
.loading-overlay-with-glass-header {
  top: calc(var(--ion-header-height, 56px) + env(safe-area-inset-top));
}

.modern-loading-overlay.with-glass-header .modern-loading-backdrop {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Safe Area Handling */
.modern-loading-overlay {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Responsive Design - Mobile First */
@media (max-width: 767px) {
  /* Optimize for mobile screens */
  .modern-loading-content {
    padding: 1.5rem 1.25rem;
    min-width: 120px;
    max-width: min(280px, calc(100vw - 2rem));
  }
  
  .skeleton-card {
    margin: 0 0.5rem 1rem 0.5rem;
  }
  
  .loading-button {
    min-height: 44px; /* iOS touch target */
  }
  
  /* Reduce backdrop blur on lower-end devices */
  .modern-loading-backdrop {
    backdrop-filter: blur(12px) saturate(160%);
    -webkit-backdrop-filter: blur(12px) saturate(160%);
  }
}

@media (max-width: 480px) {
  /* Extra small screens */
  .modern-loading-content {
    padding: 1.25rem 1rem;
    min-width: 100px;
    max-width: calc(100vw - 1rem);
  }
  
  .skeleton-loader-wrapper {
    margin: 0 0.25rem;
  }
  
  /* Further reduce effects for performance */
  .modern-loading-backdrop {
    backdrop-filter: blur(8px) saturate(140%);
    -webkit-backdrop-filter: blur(8px) saturate(140%);
  }
}

/* Landscape Orientation Optimizations */
@media (orientation: landscape) and (max-height: 500px) {
  .modern-loading-content {
    padding: 1rem 1.25rem;
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .skeleton-card {
    margin-bottom: 0.75rem;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .modern-loading-spinner,
  .loading-button-spinner {
    /* Crisp rendering on retina displays */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Performance Optimizations for Older Devices */
@media (max-width: 480px) and (max-height: 800px) {
  /* Reduce animation complexity */
  .skeleton-shimmer::before {
    animation-duration: 3s;
  }
  
  .modern-loading-spinner {
    animation-duration: 1.5s;
  }
  
  /* Simplify shadows */
  .modern-loading-content {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
}

/* Touch Interaction Optimizations */
.loading-button {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.modern-loading-backdrop {
  touch-action: none;
}

/* iOS Specific Optimizations */
@supports (-webkit-touch-callout: none) {
  .modern-loading-overlay {
    /* iOS Safari optimizations */
    -webkit-overflow-scrolling: touch;
  }
  
  .modern-loading-content {
    /* Prevent iOS zoom on focus */
    -webkit-text-size-adjust: 100%;
  }
  
  .loading-button {
    /* iOS button styling */
    -webkit-appearance: none;
    border-radius: 8px;
  }
}

/* Android Specific Optimizations */
@media (pointer: coarse) {
  .loading-button {
    /* Android touch targets */
    min-height: 48px;
    min-width: 48px;
  }
  
  .modern-loading-content {
    /* Android material design shadows */
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.1);
  }
}

/* Battery Saving Mode Optimizations */
@media (prefers-reduced-motion: reduce) {
  .modern-loading-backdrop {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    background: rgba(0, 0, 0, 0.5);
  }
  
  .skeleton-shimmer::before {
    display: none;
  }
  
  .skeleton-title,
  .skeleton-content,
  .skeleton-image {
    background: var(--ion-color-light);
    animation: none;
  }
}

/* Network-aware Loading */
@media (prefers-reduced-data: reduce) {
  .modern-loading-backdrop {
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
  
  .skeleton-shimmer {
    background: var(--ion-color-light);
  }
  
  .skeleton-shimmer::before {
    display: none;
  }
}

/* Dark Mode Mobile Optimizations */
.ion-palette-dark .modern-loading-content {
  background: rgba(28, 28, 30, 0.9);
  backdrop-filter: blur(20px) saturate(160%);
  -webkit-backdrop-filter: blur(20px) saturate(160%);
}

@media (max-width: 480px) {
  .ion-palette-dark .modern-loading-content {
    background: rgba(28, 28, 30, 0.95);
    backdrop-filter: blur(12px) saturate(140%);
    -webkit-backdrop-filter: blur(12px) saturate(140%);
  }
}

/* Smooth Transitions for Content Loading */
.content-transition-enter {
  opacity: 0;
  transform: translateY(8px) scale(0.98);
}

.content-transition-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: 
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-transition-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.content-transition-exit-active {
  opacity: 0;
  transform: translateY(-8px) scale(0.98);
  transition: 
    opacity 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Z-Index Management for Mobile */
.modern-loading-overlay {
  z-index: 20000;
}

.modern-loading-overlay.critical {
  z-index: 25000;
}

.modern-loading-overlay.modal {
  z-index: 30000;
}

/* Ensure loading doesn't interfere with navigation */
.modern-loading-overlay.with-navigation {
  z-index: 19999;
}

/* Performance Monitoring Classes */
.loading-performance-monitor {
  /* Add this class to monitor performance */
  will-change: transform, opacity;
  contain: layout style paint;
}

/* Memory Optimization */
.skeleton-loader-wrapper.offscreen {
  /* Hide offscreen skeletons to save memory */
  visibility: hidden;
  pointer-events: none;
}

/* Intersection Observer Optimizations */
.skeleton-loader-wrapper[data-visible="false"] {
  opacity: 0;
  transform: translateY(20px);
}

.skeleton-loader-wrapper[data-visible="true"] {
  opacity: 1;
  transform: translateY(0);
  transition: 
    opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
