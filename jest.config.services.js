/**
 * Jest Configuration for Services Tests
 * Specialized configuration for testing the foundational services
 */

import baseConfig from './jest.config.js';

export default {
  ...baseConfig,

  // Test file patterns - only services
  testMatch: [
    '<rootDir>/src/services/**/__tests__/**/*.test.ts',
    '<rootDir>/src/services/**/*.test.ts'
  ],

  // Setup files - use both global and services-specific setup
  setupFilesAfterEnv: [
    '<rootDir>/src/setupTests.ts',
    '<rootDir>/src/services/__tests__/setup.ts'
  ],



  // Disable coverage for now to focus on getting tests running
  collectCoverage: false,

  // Verbose output for services testing
  verbose: true
};
