# 🔍 Chrome DevTools Remote Debugging Guide

## 🎯 **Overview**

This guide shows you how to debug the Capacitor authentication flow using Chrome DevTools remote debugging via `chrome://inspect`. All authentication logs will be visible directly in Chrome DevTools console without needing a separate debug page.

## 🛠️ **Setup Instructions**

### **1. Enable Developer Options on Android Device**
```bash
# Enable Developer Options
Settings > About Phone > Tap "Build Number" 7 times

# Enable USB Debugging
Settings > Developer Options > USB Debugging (ON)
```

### **2. Connect Device and Setup ADB**
```bash
# Connect device via USB
# Accept USB debugging prompt on device

# Verify device connection
adb devices

# Expected output:
# List of devices attached
# DEVICE_ID    device
```

### **3. Build and Deploy App**
```bash
# Build the app
npx cap build android

# Sync native files
npx cap sync android

# Deploy to device
npx cap run android --target DEVICE_ID
```

### **4. Open Chrome DevTools Remote Debugging**
1. Open Chrome browser on your computer
2. Navigate to: `chrome://inspect`
3. Ensure "Discover USB devices" is checked
4. Wait for your device to appear under "Remote Target"
5. Click "inspect" next to your app's WebView

## 🔍 **Authentication Flow Logs**

When you trigger authentication, you'll see these logs in Chrome DevTools console:

### **Initialization Logs**
```
🔧 [DEBUG] Debug configuration initialized
🔧 [DEBUG] Platform: android
🔧 [DEBUG] Is native platform: true
🔧 [DEBUG] Available plugins: ["App", "InAppBrowser", "Preferences", ...]
🧪 [TEST] Testing console.log - should appear in Chrome DevTools
🔐 [AUTH] Initializing modern WebView authentication service
🔐 [AUTH] Platform: android
🔐 [AUTH] InAppBrowser available: true
✅ [AUTH] Authentication service initialization complete
```

### **Authentication Start Logs**
```
🔐 [DEBUG] ==========================================
🔐 [DEBUG] AUTHENTICATION FLOW STARTING
🔐 [DEBUG] ==========================================
🔐 [DEBUG] Timestamp: 2025-06-26T17:30:00.000Z
🔐 [DEBUG] Platform: android
🔐 [DEBUG] ==========================================
📱 [UI] Using modern Capacitor WebView authentication
📱 [UI] Platform: android
📱 [UI] InAppBrowser available: true
🚀 [AUTH] Starting secure WebView authentication
```

### **Authentication Process Logs**
```
🔑 [AUTH] Generating authentication state and PKCE parameters
💾 [AUTH] Storing authentication state securely
🔗 [AUTH] Building authorization URL
🌐 [AUTH] Opening secure WebView
✅ [AUTH] WebView opened successfully, waiting for callback
```

### **Callback Processing Logs**
```
🔗 [AUTH] Deep link received: capacitor://localhost/callback?code=...
🔄 [AUTH] Processing authentication callback
🔒 [AUTH] Valid OIDC callback detected, processing...
🚪 [AUTH] Closing WebView
🔍 [AUTH] Parsing callback URL parameters
🔍 [AUTH] Callback parameters: { hasCode: true, hasState: true, ... }
🎫 [AUTH] Authorization code received, exchanging for tokens
```

### **Token Exchange Logs**
```
🔄 [AUTH] Preparing token exchange request
🌐 [AUTH] Sending token exchange request
📡 [AUTH] Token exchange response received
🎉 [AUTH] Token exchange successful
💾 [AUTH] Storing authentication result securely
✅ [AUTH] Resolving authentication promise
```

### **Success Logs**
```
🎉 [AUTH] Authentication completed successfully
🎉 [UI] Authentication successful: { userId: "...", userName: "...", ... }
🔐 [DEBUG] ==========================================
🔐 [DEBUG] AUTHENTICATION FLOW COMPLETED
🔐 [DEBUG] ==========================================
🏠 [UI] Redirecting to home page in 2 seconds
🏠 [UI] Navigating to home page
```

## 🔧 **Debugging Commands**

### **In Chrome DevTools Console:**
```javascript
// Test console logging
console.log('🧪 Test log from Chrome DevTools');

// Check authentication state
await CapacitorAuthService.getCurrentUser();

// Clear stored data
await Preferences.clear();

// Check stored keys
const keys = await Preferences.keys();
console.log('Stored keys:', keys.keys);

// Check platform info
console.log('Platform:', Capacitor.getPlatform());
console.log('InAppBrowser available:', Capacitor.isPluginAvailable('InAppBrowser'));
```

### **Filter Console Logs:**
In Chrome DevTools console, use these filters:
- `[AUTH]` - All authentication logs
- `[UI]` - UI-related logs
- `[DEBUG]` - Debug flow logs
- `🔐` - Authentication flow markers
- `❌` - Error logs
- `⚠️` - Warning logs

## 🚨 **Troubleshooting**

### **Issue: No logs appearing in Chrome DevTools**
```bash
# Check if device is properly connected
adb devices

# Check if app is running
adb shell am start -n com.santillana.agendafamiliar/.MainActivity

# Force refresh Chrome DevTools
# Close and reopen chrome://inspect
```

### **Issue: WebView not appearing in chrome://inspect**
1. Ensure USB debugging is enabled
2. Accept USB debugging prompt on device
3. Check "Discover USB devices" in chrome://inspect
4. Restart the app
5. Wait 30 seconds for WebView to appear

### **Issue: Console logs are truncated**
1. In Chrome DevTools, go to Settings (gear icon)
2. Increase "Console" > "Preserve log upon navigation"
3. Set "Console" > "Show timestamps"

### **Issue: Authentication logs not showing**
```javascript
// Test if logging is working
console.log('🧪 Manual test log');

// Check if debug config is initialized
DebugConfig.testConsoleLogging();
```

## 📱 **Expected Debugging Experience**

### **Successful Flow:**
1. Open Chrome DevTools via chrome://inspect
2. Trigger authentication in the app
3. See complete authentication flow logs in real-time
4. Monitor WebView opening/closing
5. Track token exchange process
6. Verify successful completion

### **Failed Flow:**
1. See error logs with detailed information
2. Identify exact failure point
3. Check network requests in Network tab
4. Examine stored data in Application tab

## 🎯 **Key Benefits**

✅ **Real-time debugging** - See logs as they happen
✅ **Complete visibility** - All authentication steps logged
✅ **Network inspection** - Monitor HTTP requests
✅ **Storage inspection** - Check stored tokens and state
✅ **Error tracking** - Detailed error information
✅ **Performance monitoring** - Track timing and performance

## 📋 **Quick Testing Checklist**

1. ✅ Device connected and visible in chrome://inspect
2. ✅ App deployed and running on device
3. ✅ Chrome DevTools opened and connected
4. ✅ Console shows initialization logs
5. ✅ Authentication flow logs appear in real-time
6. ✅ Success/error logs show complete information
7. ✅ Network tab shows token exchange requests
8. ✅ Application tab shows stored tokens

This setup provides a seamless debugging experience where you can monitor the complete authentication flow directly in Chrome DevTools without any additional debug pages or tools!
