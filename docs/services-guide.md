# Agenda Familiar - Services Guide

This guide provides comprehensive documentation for the foundational services in the Agenda Familiar project. These services provide the core infrastructure for HTTP requests, storage, loading states, error handling, and user notifications.

## Table of Contents

1. [EnhancedHttpService](#enhancedhttpservice)
2. [StorageService](#storageservice)
3. [LoadingService](#loadingservice)
4. [ErrorHandlerService](#errorhandlerservice)
5. [AlertService](#alertservice)
6. [Integration Examples](#integration-examples)
7. [Best Practices](#best-practices)

## EnhancedHttpService

The `EnhancedHttpService` provides HTTP functionality with automatic authentication, loading states, error handling, and retry logic.

### Features

- **Automatic Authentication**: Adds Bearer tokens to requests automatically
- **Loading Integration**: Shows loading indicators for all HTTP requests
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Retry Logic**: Automatic retry with exponential backoff for retryable errors
- **Platform Support**: Works on both native (Capacitor) and web platforms

### Basic Usage

```typescript
import { EnhancedHttpService } from '../services';

// GET request
const response = await EnhancedHttpService.get<UserData>('/api/user/profile');
if (response.ok) {
  console.log('User data:', response.data);
}

// POST request with data
const createResponse = await EnhancedHttpService.post('/api/students', {
  name: 'Juan Pérez',
  grade: '5to Grado'
});

// Custom options
const customResponse = await EnhancedHttpService.get('/api/data', {
  headers: { 'Custom-Header': 'value' },
  timeout: 10000,
  loadingMessage: 'Cargando datos...',
  operation: 'fetch-user-data'
});
```

### Configuration

```typescript
EnhancedHttpService.configure({
  enableLoading: true,
  enableErrorHandling: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableDebugLogging: true
});
```

## StorageService

The `StorageService` provides unified storage management with type safety, quota management, and automatic cleanup.

### Features

- **Type Safety**: Full TypeScript support with generic types
- **Platform Agnostic**: Uses Capacitor Preferences on native, localStorage on web
- **Quota Management**: Automatic cleanup when storage limits are reached
- **Metadata Tracking**: Tracks storage usage and statistics
- **Error Handling**: Graceful error handling with detailed results

### Basic Usage

```typescript
import { StorageService } from '../services';

// Store typed data
interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'es' | 'en';
  notifications: boolean;
}

const preferences: UserPreferences = {
  theme: 'dark',
  language: 'es',
  notifications: true
};

const result = await StorageService.set('user-preferences', preferences);
if (result.success) {
  console.log('Preferences saved');
}

// Retrieve typed data
const retrieved = await StorageService.get<UserPreferences>('user-preferences');
if (retrieved.success) {
  console.log('Theme:', retrieved.data?.theme);
}

// Check if key exists
const exists = await StorageService.has('user-preferences');

// Get storage statistics
const stats = await StorageService.getStats();
console.log(`Storage: ${stats.totalKeys} keys, ${stats.estimatedSize} bytes`);
```

### Configuration

```typescript
StorageService.configure({
  enableDebugLogging: true,
  maxStorageSize: 10 * 1024 * 1024, // 10MB
  enableCompression: false,
  enableEncryption: false,
  cleanupThreshold: 0.8 // Cleanup at 80% capacity
});
```

## LoadingService

The `LoadingService` manages global loading states with support for multiple concurrent operations.

### Features

- **Multiple Operations**: Track multiple loading operations simultaneously
- **Minimum Display Time**: Prevents loading flicker
- **Maximum Display Time**: Auto-hide after timeout
- **Event System**: Subscribe to loading state changes
- **Ionic Integration**: Creates proper IonLoading configurations

### Basic Usage

```typescript
import { LoadingService } from '../services';

// Show loading
const operationId = LoadingService.showLoading('data-fetch', 'Cargando datos...');

try {
  // Perform operation
  await fetchData();
} finally {
  // Hide loading
  LoadingService.hideLoading(operationId);
}

// Check loading state
if (LoadingService.isGlobalLoading()) {
  console.log('Something is loading...');
}

// Subscribe to changes
LoadingService.subscribe('my-component', (event) => {
  console.log('Loading event:', event.type, event.operationId);
});
```

## ErrorHandlerService

The `ErrorHandlerService` provides centralized error handling with user-friendly notifications.

### Features

- **Error Classification**: Automatic severity classification
- **User Notifications**: Toast and alert notifications
- **Error History**: Track and analyze error patterns
- **Retry Support**: Identify retryable errors
- **Platform Integration**: Uses Ionic controllers

### Basic Usage

```typescript
import { ErrorHandlerService } from '../services';

try {
  await riskyOperation();
} catch (error) {
  // Handle error with default configuration
  await ErrorHandlerService.handleError(error);
  
  // Handle error with custom configuration
  await ErrorHandlerService.handleError(error, {
    showAlert: true,
    showToast: false,
    customMessage: 'Error personalizado',
    severity: 'high'
  });
}

// Get error history
const history = ErrorHandlerService.getErrorHistory();
console.log(`${history.length} errors recorded`);
```

## AlertService

The `AlertService` provides user notifications including toasts, alerts, and confirmation dialogs.

### Features

- **Multiple Types**: Success, error, warning, info notifications
- **Toast Notifications**: Non-blocking notifications
- **Alert Dialogs**: Blocking dialogs for important messages
- **Confirmation Dialogs**: Yes/No confirmation prompts
- **Haptic Feedback**: Tactile feedback on supported devices

### Basic Usage

```typescript
import { AlertService } from '../services';

// Show success toast
await AlertService.showSuccess('¡Datos guardados correctamente!');

// Show error toast
await AlertService.showError('Error al cargar los datos');

// Show custom toast
await AlertService.showToast({
  message: 'Mensaje personalizado',
  duration: 5000,
  color: 'primary',
  position: 'top'
});

// Show alert dialog
await AlertService.showAlert({
  header: 'Información Importante',
  message: 'Los datos se han actualizado',
  buttons: [
    {
      text: 'Entendido',
      role: 'confirm',
      handler: () => console.log('User acknowledged')
    }
  ]
});

// Show confirmation dialog
const confirmed = await AlertService.showConfirmation({
  header: 'Confirmar Eliminación',
  message: '¿Estás seguro de que quieres eliminar este elemento?',
  confirmText: 'Eliminar',
  cancelText: 'Cancelar'
});

if (confirmed) {
  console.log('User confirmed deletion');
}
```

## Integration Examples

### Complete Data Fetching Example

```typescript
import { 
  EnhancedHttpService, 
  StorageService, 
  AlertService, 
  ErrorHandlerService 
} from '../services';

interface StudentData {
  id: string;
  name: string;
  grade: string;
  lastUpdated: number;
}

class StudentService {
  private static readonly CACHE_KEY = 'students-cache';
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  static async getStudents(forceRefresh = false): Promise<StudentData[]> {
    try {
      // Check cache first
      if (!forceRefresh) {
        const cached = await StorageService.get<{
          data: StudentData[];
          timestamp: number;
        }>(this.CACHE_KEY);

        if (cached.success && cached.data) {
          const isValid = Date.now() - cached.data.timestamp < this.CACHE_DURATION;
          if (isValid) {
            return cached.data.data;
          }
        }
      }

      // Fetch from API
      const response = await EnhancedHttpService.get<StudentData[]>('/api/students', {
        operation: 'fetch-students',
        loadingMessage: 'Cargando estudiantes...'
      });

      if (response.ok) {
        // Cache the results
        await StorageService.set(this.CACHE_KEY, {
          data: response.data,
          timestamp: Date.now()
        });

        await AlertService.showSuccess('Estudiantes cargados correctamente');
        return response.data;
      } else {
        throw new Error('Failed to fetch students');
      }

    } catch (error) {
      await ErrorHandlerService.handleError(error, {
        customMessage: 'Error al cargar los estudiantes'
      });
      throw error;
    }
  }
}
```

### Form Submission Example

```typescript
import { EnhancedHttpService, AlertService } from '../services';

class FormService {
  static async submitStudentForm(formData: any): Promise<boolean> {
    try {
      const confirmed = await AlertService.showConfirmation({
        header: 'Confirmar Envío',
        message: '¿Estás seguro de que quieres enviar este formulario?'
      });

      if (!confirmed) {
        return false;
      }

      const response = await EnhancedHttpService.post('/api/students', formData, {
        operation: 'submit-student-form',
        loadingMessage: 'Enviando formulario...'
      });

      if (response.ok) {
        await AlertService.showSuccess('Formulario enviado correctamente');
        return true;
      } else {
        throw new Error('Form submission failed');
      }

    } catch (error) {
      await AlertService.showError('Error al enviar el formulario');
      return false;
    }
  }
}
```

## Best Practices

### 1. Error Handling
- Always handle errors gracefully
- Use appropriate error severity levels
- Provide meaningful error messages to users
- Log errors for debugging purposes

### 2. Loading States
- Show loading indicators for operations > 300ms
- Use descriptive loading messages
- Always hide loading states in finally blocks

### 3. Storage Management
- Use typed interfaces for stored data
- Implement cache invalidation strategies
- Monitor storage usage and implement cleanup

### 4. HTTP Requests
- Use appropriate HTTP methods
- Implement proper timeout values
- Handle authentication errors appropriately
- Use retry logic for transient failures

### 5. User Notifications
- Use appropriate notification types
- Keep messages concise and actionable
- Provide confirmation for destructive actions
- Consider accessibility in notification design

### 6. Service Configuration
- Configure services at application startup
- Use environment-specific configurations
- Enable debug logging in development
- Disable unnecessary features in production

### 7. Memory Management
- Clean up services on application shutdown
- Unsubscribe from event listeners
- Clear caches when appropriate
- Monitor memory usage in long-running applications

## Testing Services

### Unit Testing Example

```typescript
import { StorageService } from '../services/storage.service';

describe('StorageService', () => {
  beforeEach(() => {
    // Reset service state
    StorageService.configure({
      enableDebugLogging: false,
      maxStorageSize: 1024 * 1024 // 1MB for testing
    });
  });

  afterEach(async () => {
    // Clean up after each test
    await StorageService.clear();
  });

  it('should store and retrieve data correctly', async () => {
    const testData = { name: 'Test User', age: 25 };

    const setResult = await StorageService.set('test-key', testData);
    expect(setResult.success).toBe(true);

    const getResult = await StorageService.get('test-key');
    expect(getResult.success).toBe(true);
    expect(getResult.data).toEqual(testData);
  });

  it('should handle storage quota exceeded', async () => {
    // Configure small storage limit
    StorageService.configure({ maxStorageSize: 100 });

    const largeData = 'x'.repeat(200);
    const result = await StorageService.set('large-key', largeData);

    expect(result.success).toBe(false);
    expect(result.error).toContain('quota exceeded');
  });
});
```

### Integration Testing Example

```typescript
import { EnhancedHttpService, LoadingService, ErrorHandlerService } from '../services';

describe('HTTP Service Integration', () => {
  beforeEach(() => {
    // Mock HTTP responses
    jest.spyOn(global, 'fetch').mockImplementation();

    // Reset services
    LoadingService.hideAllLoading();
    ErrorHandlerService.clearErrorHistory();
  });

  it('should show loading during HTTP request', async () => {
    const mockResponse = { ok: true, json: () => Promise.resolve({ data: 'test' }) };
    (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

    const requestPromise = EnhancedHttpService.get('/test');

    // Check that loading is active
    expect(LoadingService.isGlobalLoading()).toBe(true);

    await requestPromise;

    // Check that loading is hidden after request
    expect(LoadingService.isGlobalLoading()).toBe(false);
  });

  it('should handle errors correctly', async () => {
    const mockError = new Error('Network error');
    (global.fetch as jest.Mock).mockRejectedValue(mockError);

    try {
      await EnhancedHttpService.get('/test');
    } catch (error) {
      // Error should be handled by ErrorHandlerService
      const history = ErrorHandlerService.getErrorHistory();
      expect(history.length).toBeGreaterThan(0);
    }
  });
});
```

## Troubleshooting

### Common Issues

1. **Loading indicators not showing**
   - Ensure LoadingService is properly initialized
   - Check that Ionic components are available
   - Verify loading operations are properly paired (show/hide)

2. **Authentication headers not added**
   - Verify tokens are stored correctly
   - Check platform detection (native vs web)
   - Ensure authentication services are initialized

3. **Storage quota exceeded**
   - Implement proper cleanup strategies
   - Monitor storage usage regularly
   - Configure appropriate storage limits

4. **Toast/Alert not displaying**
   - Ensure Ionic controllers are initialized
   - Check for JavaScript errors in console
   - Verify proper component lifecycle

### Debug Mode

Enable debug logging for all services:

```typescript
import {
  EnhancedHttpService,
  StorageService,
  LoadingService,
  ErrorHandlerService,
  AlertService
} from '../services';

// Enable debug logging for all services
EnhancedHttpService.configure({ enableDebugLogging: true });
StorageService.configure({ enableDebugLogging: true });
LoadingService.configure({ enableDebugLogging: true });
AlertService.configure({ enableDebugLogging: true });
```

### Performance Monitoring

```typescript
// Monitor service performance
const stats = {
  http: EnhancedHttpService.getStats(),
  storage: await StorageService.getStats(),
  loading: LoadingService.getGlobalState(),
  alerts: AlertService.getActiveCount()
};

console.log('Service Statistics:', stats);
```
