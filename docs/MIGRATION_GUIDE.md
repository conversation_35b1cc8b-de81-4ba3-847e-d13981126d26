# 🔄 Dark Mode Migration Guide

## 📋 Overview

This guide helps developers understand the changes made during the dark mode refactoring and how to work with the new implementation.

## 🚨 Breaking Changes

### **Removed Props**
All components no longer accept theme-related props:

```typescript
// ❌ OLD - Props drilling approach
interface ComponentProps {
  isDarkMode: boolean;
  toggleDarkMode: (checked: boolean) => void;
}

// ✅ NEW - No theme props needed
interface ComponentProps {
  // Other props only
}
```

### **Removed Components**
- `DarkModeToggle` component (replaced with `ThemeToggle`)
- `AuthDemo` component (was using old implementation)

### **Changed Component Signatures**

```typescript
// ❌ OLD
const AuthPage: React.FC<AuthPageProps> = ({ isDarkMode, toggleDarkMode }) => {
  // Component logic
};

// ✅ NEW
const AuthPage: React.FC = () => {
  // Component logic - use useTheme() hook if needed
};
```

## 🔧 How to Use New Implementation

### **1. Using Theme in Components**

```typescript
import { useTheme } from '../contexts/ThemeContext';

const MyComponent: React.FC = () => {
  const { isDarkMode, toggleTheme, setTheme } = useTheme();

  return (
    <div>
      <p>Current theme: {isDarkMode ? 'Dark' : 'Light'}</p>
      {/* Use theme state as needed */}
    </div>
  );
};
```

### **2. Theme Toggle Location**

```typescript
// ✅ ONLY use ThemeToggle in AccountPage
import ThemeToggle from '../../components/ThemeToggle/ThemeToggle';

const AccountPage: React.FC = () => {
  return (
    <IonContent>
      <IonItemDivider>Preferencias</IonItemDivider>
      <ThemeToggle />
    </IonContent>
  );
};
```

### **3. Conditional Styling Based on Theme**

```typescript
const MyComponent: React.FC = () => {
  const { isDarkMode } = useTheme();

  return (
    <div className={`my-component ${isDarkMode ? 'dark' : 'light'}`}>
      {/* Content */}
    </div>
  );
};
```

## 🎨 CSS Changes

### **Theme Variables**
Continue using CSS custom properties as before:

```css
.my-component {
  background: var(--ion-background-color);
  color: var(--ion-text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.ion-palette-dark .my-component {
  /* Dark mode specific styles if needed */
}
```

### **Transition Support**
The new implementation includes smooth transitions:

```css
/* Automatic transitions for theme changes */
* {
  transition: 
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing);
}
```

## 🧪 Testing Changes

### **Component Tests**
Update component tests to use ThemeProvider:

```typescript
import { render } from '@testing-library/react';
import { ThemeProvider } from '../contexts/ThemeContext';
import MyComponent from './MyComponent';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

test('component renders correctly', () => {
  renderWithTheme(<MyComponent />);
  // Test assertions
});
```

### **Theme Context Tests**
Test theme functionality using the context:

```typescript
import { useTheme } from '../contexts/ThemeContext';

const TestComponent = () => {
  const { isDarkMode, toggleTheme } = useTheme();
  return (
    <div>
      <span data-testid="theme">{isDarkMode ? 'dark' : 'light'}</span>
      <button onClick={toggleTheme}>Toggle</button>
    </div>
  );
};
```

## 📱 User Experience Changes

### **For Users**
- **Single Location**: Theme toggle now only in Account page (/tabs/cuenta)
- **Modern Design**: New, contemporary toggle design
- **Instant Effect**: Theme changes apply immediately
- **Persistent**: Settings saved across sessions

### **For Developers**
- **Cleaner Code**: No more props drilling
- **Easy Integration**: Simple useTheme() hook
- **Type Safety**: Full TypeScript support
- **Better Performance**: Efficient context-based updates

## 🔍 Troubleshooting

### **Common Issues**

1. **"useTheme must be used within a ThemeProvider" Error**
   ```typescript
   // ❌ Component not wrapped in ThemeProvider
   const App = () => <MyComponent />;

   // ✅ Wrap with ThemeProvider
   const App = () => (
     <ThemeProvider>
       <MyComponent />
     </ThemeProvider>
   );
   ```

2. **Theme Not Persisting**
   - Check localStorage permissions
   - Verify ThemeProvider is at app root level
   - Check browser console for errors

3. **Transitions Not Working**
   - Ensure CSS custom properties are properly defined
   - Check for conflicting CSS transitions
   - Verify theme-transitioning class is working

### **Debug Theme State**

```typescript
const DebugTheme: React.FC = () => {
  const { isDarkMode } = useTheme();
  
  useEffect(() => {
    console.log('Current theme:', isDarkMode ? 'dark' : 'light');
    console.log('DOM class:', document.documentElement.classList.contains('ion-palette-dark'));
    console.log('localStorage:', localStorage.getItem('agenda-familiar-theme'));
  }, [isDarkMode]);

  return null;
};
```

## 📚 Best Practices

### **Do's**
- ✅ Use `useTheme()` hook for theme access
- ✅ Place theme toggle only in Account page
- ✅ Use CSS custom properties for theming
- ✅ Test theme functionality thoroughly
- ✅ Follow accessibility guidelines

### **Don'ts**
- ❌ Don't create multiple theme toggles
- ❌ Don't pass theme props manually
- ❌ Don't bypass the ThemeProvider
- ❌ Don't hardcode theme-specific styles
- ❌ Don't forget to test both themes

## 🚀 Next Steps

1. **Update existing components** to remove theme props
2. **Test all pages** in both light and dark themes
3. **Verify accessibility** with screen readers
4. **Check responsive design** on different devices
5. **Update documentation** for your team

---

## 📞 Support

If you encounter issues during migration:

1. Check the console for error messages
2. Verify ThemeProvider is properly configured
3. Test theme persistence in browser
4. Review component implementations
5. Check CSS custom properties

The new implementation provides a more maintainable, accessible, and user-friendly dark mode experience following modern React and Ionic best practices.
