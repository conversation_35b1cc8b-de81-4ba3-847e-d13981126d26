<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redirecting...</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      text-align: center;
      padding: 20px;
    }
    .loader {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <h2>Authentication Successful</h2>
  <p>Redirecting back to the app...</p>
  <div class="loader"></div>
  
  <script>
    // This script helps redirect back to the app
    document.addEventListener('DOMContentLoaded', function() {
      try {
        console.log('Callback page loaded');
        
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        
        if (code && state) {
          console.log('Auth code received, redirecting to app');
          
          // Construct deep link URL
          const deepLink = `capacitor://localhost/callback?${window.location.search.substring(1)}`;
          console.log('Redirecting to:', deepLink);
          
          // Redirect to app
          window.location.href = deepLink;
          
          // Fallback for iOS
          setTimeout(function() {
            // If we're still here after 2 seconds, show a manual button
            const redirectButton = document.createElement('button');
            redirectButton.innerText = 'Click here to return to app';
            redirectButton.style.padding = '10px 20px';
            redirectButton.style.backgroundColor = '#4CAF50';
            redirectButton.style.color = 'white';
            redirectButton.style.border = 'none';
            redirectButton.style.borderRadius = '4px';
            redirectButton.style.cursor = 'pointer';
            redirectButton.style.marginTop = '20px';
            
            redirectButton.onclick = function() {
              window.location.href = deepLink;
            };
            
            document.body.appendChild(redirectButton);
          }, 2000);
        } else {
          console.error('Missing code or state parameters');
          document.body.innerHTML += '<p style="color: red;">Error: Missing authentication parameters</p>';
        }
      } catch (error) {
        console.error('Error in callback page:', error);
        document.body.innerHTML += `<p style="color: red;">Error: ${error.message}</p>`;
      }
    });
  </script>
</body>
</html>