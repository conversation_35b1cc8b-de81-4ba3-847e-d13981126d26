<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autenticación - Agenda Familiar</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Completando autenticación...</h2>
        <p>Redirigiendo a la aplicación...</p>
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('🔐 [CALLBACK] OAuth callback page loaded');
                
                // Get URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const state = urlParams.get('state');
                const error = urlParams.get('error');
                
                if (error) {
                    console.error('🔐 [CALLBACK] Error in callback:', error);
                    showError('Error de autenticación: ' + error);
                    return;
                }
                
                if (!code) {
                    console.error('🔐 [CALLBACK] No authorization code in callback');
                    showError('No se recibió código de autorización');
                    return;
                }
                
                console.log('🔐 [CALLBACK] Authorization code received:', code.substring(0, 10) + '...');
                
                // Construir URL de deep link para la app con todos los parámetros originales
                const appUrl = `capacitor://localhost/callback${window.location.search}`;
                console.log('🔐 [CALLBACK] Redirigiendo a:', appUrl);
                
                // Try multiple redirection methods to ensure it works
                // Method 1: Standard location change
                window.location.href = appUrl;
                
                // Method 2: Try with timeout as fallback
                setTimeout(() => {
                    window.location.replace(appUrl);
                }, 500);
                
                // Method 3: Try with iframe as another fallback
                setTimeout(() => {
                    const iframe = document.createElement('iframe');
                    iframe.style.display = 'none';
                    iframe.src = appUrl;
                    document.body.appendChild(iframe);
                }, 1000);
                
                // Fallback: mostrar instrucciones después de 3 segundos
                setTimeout(() => {
                    document.querySelector('.container').innerHTML = `
                        <h2>¡Autenticación exitosa!</h2>
                        <p>Si la aplicación no se abrió automáticamente, por favor haz clic en el botón de abajo:</p>
                        <p><small>Código: ${code.substring(0, 10)}...</small></p>
                        <button onclick="window.location.href='${appUrl}'" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Abrir aplicación
                        </button>
                    `;
                }, 2000);
                
            } catch (err) {
                console.error('🔐 [CALLBACK] Error:', err);
                showError('Error procesando la respuesta de autenticación');
            }
        });
        
        function showError(message) {
            document.querySelector('.container').innerHTML = `
                <div style="color: #721c24; background-color: #f8d7da; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
                <button onclick="window.location.href='capacitor://localhost/auth'" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Volver a intentar
                </button>
            `;
        }
    </script>
</body>
</html>
