# Development SSL Certificates

This directory contains SSL certificates for local HTTPS development.

## Files

- `localhost.pem` - SSL certificate for localhost
- `localhost-key.pem` - Private key for localhost certificate

## Usage

These certificates are used when running the development server with HTTPS:

```bash
npm run dev
# Server runs at https://localhost:8100
```

## Generating New Certificates

If you need to generate new certificates:

```bash
# Using mkcert (recommended)
mkcert localhost

# Using openssl
openssl req -x509 -newkey rsa:4096 -keyout localhost-key.pem -out localhost.pem -days 365 -nodes -subj '/CN=localhost'
```

## Security Note

These certificates are for development only and should never be used in production. They are self-signed and not trusted by browsers by default.
