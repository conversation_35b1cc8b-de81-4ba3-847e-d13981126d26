![Logo](./public/img/logo.png)

# Agenda Familiar App

## Index

- [Getting Started](#getting-started)
- [Project Structure](#project-structure)

-- -

## Getting Started

### Using docker

```bash

# DEV local hot-reload
docker compose -f docker-compose.dev.yml up --build

# PRE the optimised bundle as CI
docker compose -f docker-compose.pre.yml up --build

# PROD artefact pipeline will push to a registry
docker compose -f docker-compose.prod.yml build
docker compose -f docker-compose.prod.yml up
```

## Project Structure

```
├── android/                        # Android platform files
├── ios/                            # iOS platform files
├── docs/                           # Documentation files
│   ├── AUTHENTICATION.md           # Authentication documentation
│   ├── DEVELOPMENT.md              # Development guide
│   └── *.md                        # Other documentation files
├── public/                         # Static assets
│   ├── assets/                     # Images and icons
│   ├── img/                        # Application images
│   └── manifest.json               # PWA manifest
├── src/                            # Source code
│   ├── assets/                     # Application assets
│   │   └── icons/                  # Icon components
│   ├── components/                 # Reusable UI components
│   │   ├── AttendanceCard/         # Attendance display component
│   │   ├── CurricularProgressCard/ # Progress tracking component
│   │   ├── ErrorBoundary/          # Error handling component
│   │   ├── Header/                 # Application header
│   │   ├── ReportsProgressCard/    # Reports display component
│   │   ├── TabBar/                 # Navigation tabs
│   │   ├── ThemeToggle/            # Dark/light mode toggle
│   │   ├── WelcomeCard/            # Welcome screen component
│   │   ├── layout/                 # Layout components
│   │   └── index.ts                # Component exports
│   ├── config/                     # Configuration files
│   │   ├── auth.config.ts          # Authentication configuration
│   │   ├── debug.config.ts         # Debug settings
│   │   ├── environment.config.ts   # Environment variables
│   │   ├── user-manager.config.ts  # OIDC user manager setup
│   │   └── index.ts                # Config exports
│   ├── contexts/                   # React contexts
│   │   ├── ThemeContext.tsx        # Theme management
│   │   └── UserContext.tsx         # User state management
│   ├── data/                       # Mock data and constants
│   │   └── studentMockData.ts      # Student data for development
│   ├── hooks/                      # Custom React hooks
│   │   └── useAuth.ts              # Authentication hook
│   ├── pages/                      # Application pages
│   │   ├── AccountPage/            # User account management
│   │   ├── Auth/                   # Authentication pages
│   │   ├── HomePage/               # Main dashboard
│   │   └── StudentSelection/       # Student selection page
│   ├── routes/                     # Routing configuration
│   │   ├── AppRoutes.tsx           # Main routes definition
│   │   ├── RouteGuard.tsx          # Route protection component
│   │   └── routes.ts               # Routes constants
│   ├── services/                   # Business logic services
│   │   ├── auth-storage.service.ts # Token storage service
│   │   ├── authService.ts          # Authentication service
│   │   ├── capacitor-auth.service.ts # Native auth service
│   │   ├── user-api.service.ts     # User API service
│   │   └── index.ts                # Service exports
│   ├── theme/                      # Styling and themes
│   │   └── variables.css           # CSS custom properties
│   ├── types/                      # TypeScript type definitions
│   │   └── svg.d.ts                # SVG module declarations
│   ├── utils/                      # Utility functions
│   │   ├── auth-error-handler.ts   # Authentication error handling
│   │   ├── config-validator.ts     # Configuration validation
│   │   ├── crypto-fallback.ts      # Crypto polyfills
│   │   ├── crypto.ts               # Cryptographic utilities
│   │   ├── http.ts                 # HTTP utilities
│   │   ├── native-http.ts          # Native HTTP handling
│   │   ├── navigation-debug.ts     # Navigation debugging
│   │   ├── oidc-debug.ts           # OIDC debugging utilities
│   │   ├── storage.ts              # Storage utilities
│   │   ├── validation.ts           # Form validation
│   │   └── index.ts                # Utility exports
│   ├── App.tsx                     # Root component
│   ├── main.tsx                    # Application entry point
│   └── vite-env.d.ts               # Vite environment types
├── capacitor.config.ts             # Capacitor configuration
├── package.json                    # Dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
├── vite.config.ts                  # Vite build configuration
└── README.md                       # This file
```

## Documentation

All project documentation has been moved to the `docs/` folder:

- **[Authentication Guide](docs/AUTHENTICATION.md)** - OIDC authentication setup and troubleshooting
- **[Development Guide](docs/DEVELOPMENT.md)** - Development environment setup
- **[Architecture Overview](docs/ARCHITECTURE.md)** - Application architecture and design decisions
- **[Migration Guides](docs/)** - Various migration and upgrade guides

For a complete list of available documentation, see the [docs/](docs/) directory.

