# Modern HTTP Loading System - Integration Guide

## 🚀 Overview

This comprehensive, modern loading system provides professional, native-feeling loading states **exclusively for HTTP requests** throughout your Ionic React application. The system features glass-like translucent overlays, shimmer animations, and seamless integration with your established design patterns.

## ⚠️ **IMPORTANT: HTTP-Only Loading**

**El sistema de loading debe usarse ÚNICAMENTE para peticiones HTTP.** No debe activarse para operaciones locales de UI, navegación, o procesamiento de datos local.

### ✅ **Cuándo usar loading:**
- Peticiones HTTP/API calls
- Operaciones de red
- Llamadas a servicios externos

### ❌ **Cuándo NO usar loading:**
- Navegación entre páginas
- Operaciones de UI locales
- Procesamiento de datos en memoria
- Cambios de estado local

## ✨ Modern Design Features

- **Glass-like Loading Overlays** with backdrop-filter blur effects and rgba transparency
- **Shimmer Skeleton Animations** for professional, subtle loading states
- **Automatic HTTP Integration** - loading activates automatically during HTTP requests
- **60fps Mobile Performance** optimized for iOS and Android devices
- **Design System Integration** with 16px border radius, proper shadows, and gradient effects
- **Smart Loading States** that feel responsive and never jarring
- **Perfect TabBar & Header Integration** with floating appearance and glass effects

## 📦 Components

### 1. **LoadingService** - Global Loading State Management
- Tracks multiple concurrent loading operations
- Prevents loading flashing with minimum display times
- Automatic timeout handling
- Event-driven architecture for React integration

### 2. **ErrorHandlerService** - Comprehensive Error Handling
- Parses and categorizes all error types
- User-friendly error messages in Spanish
- Native Ionic toast and alert integration
- Automatic retry logic for retryable errors

### 3. **EnhancedHttpService** - HTTP with Loading Integration
- Extends existing HttpService patterns
- Automatic loading state management
- Built-in error handling and retry logic
- Compatible with Capacitor HTTP and authentication

### 4. **ModernLoading** - Glass-like Loading Overlay
- Glass-like translucent appearance with backdrop-filter blur
- 16px border radius and proper shadow effects
- Progress ring indicators with smooth animations
- Perfect integration with TabBar and Header glass effects

### 5. **SkeletonLoader** - Modern Shimmer Loading
- Shimmer effects instead of basic pulse animations
- Multiple layout variants (text, card, list, grid, custom)
- Follows AttendanceCard design standards perfectly
- Smooth fade-in/fade-out transitions

### 6. **LoadingButton** - Professional Loading Buttons
- Automatic loading state detection for async operations
- Progress ring indicators with percentage display
- Smooth transitions and micro-interactions
- Accessibility-first design

### 7. **ProgressIndicator** - Subtle Progress Components
- Linear, circular, and minimal variants
- Smart responsive design
- Color-coded progress states
- Professional animations

### 8. **React Contexts** - Easy Integration
- LoadingContext for loading state management
- ErrorContext for error handling
- Custom hooks for common patterns
- Higher-order components for automatic integration

## 🛠 Installation & Setup

### 1. Provider Setup (Already Done)
The providers are already integrated in `src/App.tsx`:

```tsx
<ThemeProvider>
  <ErrorProvider enableGlobalErrorHandling={true}>
    <LoadingProvider showGlobalLoading={true}>
      <UserProvider>
        <IonReactRouter>
          <AppRoutes />
        </IonReactRouter>
      </UserProvider>
    </LoadingProvider>
  </ErrorProvider>
</ThemeProvider>
```

### 2. Import Types and Services
```tsx
// Services
import { 
  LoadingService, 
  ErrorHandlerService, 
  EnhancedHttpService 
} from '../services';

// Components
import { SkeletonLoader } from '../components';

// Contexts and Hooks
import { 
  useLoading, 
  useError, 
  useComponentLoading, 
  useAsyncOperation 
} from '../contexts/LoadingContext';
```

## 📋 Usage Examples

### ✅ **Correcto: HTTP Requests con Loading Automático**
```tsx
const MyComponent = () => {
  const [data, setData] = useState(null);

  const loadData = async () => {
    try {
      // Loading se muestra automáticamente durante la petición HTTP
      const response = await EnhancedHttpService.get('/api/data', {
        loadingMessage: 'Cargando datos...',
        operation: 'load-user-data'
      });

      setData(response.data);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  return (
    <button onClick={loadData}>Load Data</button>
  );
};
```

### ✅ **Correcto: Monitoreo del Estado HTTP**
```tsx
const MyComponent = () => {
  const { isLoading, loadingMessage } = useHttpLoading();

  return (
    <div>
      {isLoading && <p>Estado HTTP: {loadingMessage}</p>}
      {/* Tu contenido */}
    </div>
  );
};
```

### ❌ **Incorrecto: Loading Manual para UI**
```tsx
// NO HACER ESTO - No usar loading para operaciones locales
const MyComponent = () => {
  const { showLoading, hideLoading } = useLoading();

  const handleLocalOperation = () => {
    const operationId = showLoading(); // ❌ INCORRECTO
    // Operación local sin HTTP
    processLocalData();
    hideLoading(operationId);
  };
};
```

### Automatic Async Operations
```tsx
const MyComponent = () => {
  const { execute, isLoading, error } = useAsyncOperation(
    async (id: string) => {
      const response = await fetch(`/api/data/${id}`);
      return response.json();
    },
    'fetch-user-data'
  );
  
  return (
    <div>
      <button onClick={() => execute('123')} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Load Data'}
      </button>
      {error && <p>Error: {error.message}</p>}
    </div>
  );
};
```

### ✅ **HTTP Requests con Loading Automático**
```tsx
const MyComponent = () => {
  const [data, setData] = useState(null);

  const loadData = async () => {
    try {
      // Loading se muestra automáticamente - no necesitas showLoading: true
      const response = await EnhancedHttpService.get('/api/data', {
        loadingMessage: 'Cargando datos...',
        operation: 'load-user-data',
        errorHandling: {
          severity: 'medium',
          showToast: true,
          retryable: true
        }
      });

      setData(response.data);
    } catch (error) {
      // Error is automatically handled by the service
      console.error('Failed to load data:', error);
    }
  };

  return (
    <button onClick={loadData}>Load Data</button>
  );
};
```

### Modern Loading Button
```tsx
const MyComponent = () => {
  const [progress, setProgress] = useState(0);

  const handleUpload = async () => {
    // LoadingButton automatically handles loading state
    await uploadFile();
  };

  const handleProgressOperation = async () => {
    // Button with progress indicator
    for (let i = 0; i <= 100; i += 10) {
      setProgress(i);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  };

  return (
    <div>
      <LoadingButton
        onClick={handleUpload}
        loadingText="Subiendo archivo..."
        color="primary"
      >
        Upload File
      </LoadingButton>

      <LoadingButton
        onClick={handleProgressOperation}
        showProgress={true}
        progress={progress}
        loadingText="Procesando..."
        color="secondary"
      >
        Process with Progress
      </LoadingButton>
    </div>
  );
};
```

### Progress Indicators
```tsx
const MyComponent = () => {
  const [uploadProgress, setUploadProgress] = useState(0);

  return (
    <div>
      {/* Linear progress */}
      <ProgressIndicator
        value={uploadProgress}
        variant="linear"
        showPercentage={true}
        showLabel={true}
        label="Subiendo archivo"
        color="primary"
      />

      {/* Circular progress */}
      <ProgressIndicator
        value={uploadProgress}
        variant="circular"
        showPercentage={true}
        size="large"
        color="secondary"
      />

      {/* Minimal progress */}
      <ProgressIndicator
        value={uploadProgress}
        variant="minimal"
        showLabel={true}
        label="Progreso"
        color="tertiary"
      />
    </div>
  );
};
```

### Modern Skeleton Loading with Shimmer
```tsx
const MyComponent = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  if (loading) {
    return (
      <SkeletonLoader
        variant="card"
        cardConfig={{
          hasHeader: true,
          hasContent: true,
          hasActions: true
        }}
        className="shimmer-effect" // Modern shimmer animation
      />
    );
  }

  return (
    <IonCard className="card-base">
      {/* Your content with smooth fade-in */}
      <div className="content-transition-enter-active">
        {/* Your content */}
      </div>
    </IonCard>
  );
};
```

### Advanced Skeleton Patterns
```tsx
const AdvancedSkeletonExample = () => {
  return (
    <div>
      {/* AttendanceCard-style skeleton */}
      <SkeletonLoader
        variant="card"
        cardConfig={{
          hasHeader: true,
          hasImage: false,
          hasContent: true,
          hasActions: false
        }}
        animated={true}
      />

      {/* List with avatars */}
      <SkeletonLoader
        variant="list"
        listConfig={{
          items: 5,
          hasAvatar: true,
          hasSecondaryText: true
        }}
      />

      {/* Grid Layout */}
      <SkeletonLoader
        variant="grid"
        gridConfig={{
          columns: 2,
          rows: 3,
          gap: '1rem'
        }}
      />

      {/* Custom pattern */}
      <SkeletonLoader
        variant="custom"
        customPattern={[
          { type: 'rect', width: '100%', height: '200px' },
          { type: 'text', width: '80%', height: '20px' },
          { type: 'text', width: '60%', height: '16px' },
          { type: 'circle', width: '40px', height: '40px' }
        ]}
      />
    </div>
  );
};
```

### Error Handling
```tsx
const MyComponent = () => {
  const { handleError, clearError, lastError } = useError();
  
  const riskyOperation = async () => {
    try {
      await someRiskyOperation();
    } catch (error) {
      await handleError(error, {
        severity: 'high',
        showAlert: true,
        customMessage: 'No se pudo completar la operación. Inténtalo de nuevo.',
        retryable: true
      });
    }
  };
  
  return (
    <div>
      <button onClick={riskyOperation}>Risky Operation</button>
      {lastError && (
        <div>
          <p>Error: {lastError.message}</p>
          <button onClick={clearError}>Clear Error</button>
        </div>
      )}
    </div>
  );
};
```

## 🎨 Skeleton Variants

### Text Skeleton
```tsx
<SkeletonLoader variant="text" lines={3} />
```

### Card Skeleton (Following AttendanceCard Standards)
```tsx
<SkeletonLoader 
  variant="card"
  cardConfig={{
    hasHeader: true,
    hasImage: false,
    hasContent: true,
    hasActions: true
  }}
/>
```

### List Skeleton
```tsx
<SkeletonLoader 
  variant="list"
  listConfig={{
    items: 5,
    hasAvatar: true,
    hasSecondaryText: true
  }}
/>
```

### Grid Skeleton
```tsx
<SkeletonLoader 
  variant="grid"
  gridConfig={{
    columns: 2,
    rows: 3,
    gap: '1rem'
  }}
/>
```

### Custom Skeleton
```tsx
<SkeletonLoader 
  variant="custom"
  customPattern={[
    { type: 'rect', width: '100%', height: '200px' },
    { type: 'text', width: '80%', height: '20px' },
    { type: 'text', width: '60%', height: '16px' }
  ]}
/>
```

## 🔧 Configuration

### Loading Service Configuration
```tsx
import { LoadingService } from '../services';

LoadingService.configure({
  defaultMessage: 'Cargando...',
  minDisplayTime: 300,
  maxDisplayTime: 30000,
  enableDebugLogging: true
});
```

### Enhanced HTTP Service Configuration
```tsx
import { EnhancedHttpService } from '../services';

EnhancedHttpService.configure({
  enableLoading: true,
  enableErrorHandling: true,
  enableRetry: true,
  maxRetries: 3,
  retryDelay: 1000,
  enableDebugLogging: true
});
```

## 🎯 Best Practices

### 1. **Solo usar Loading para HTTP**
```tsx
// ✅ Correcto - Loading automático para HTTP
const response = await EnhancedHttpService.get('/api/data');

// ❌ Incorrecto - Loading manual para operaciones locales
const operationId = showLoading();
processLocalData();
hideLoading(operationId);
```

### 2. **Monitorear Estado HTTP**
```tsx
// ✅ Correcto - Monitorear estado HTTP
const { isLoading, loadingMessage } = useHttpLoading();

// ❌ Incorrecto - Control manual de loading
const { showLoading, hideLoading } = useLoading();
```

### 3. **Usar Skeleton para Estados de Carga**
```tsx
// ✅ Correcto - Skeleton mientras se cargan datos HTTP reales
const { execute: loadData, isLoading } = useHttpOperation(async () => {
  return await EnhancedHttpService.get('/api/data');
});

{isLoading ? (
  <SkeletonLoader variant="card" cardConfig={{ hasHeader: true }} />
) : (
  <DataComponent data={data} />
)}

// ❌ Incorrecto - Skeleton para datos mock/estáticos
const mockData = [/* datos estáticos */];
{showFakeLoading && <SkeletonLoader />} // No hacer esto

// ❌ Incorrecto - Skeleton con delays artificiales
await new Promise(resolve => setTimeout(resolve, 1500)); // No simular carga
{artificialLoading && <SkeletonLoader />} // No hacer esto

// ❌ Incorrecto - Skeleton para operaciones instantáneas
{processingLocalData && <SkeletonLoader />}
```

### 4. **HTTP Operations Hook**
```tsx
// ✅ Correcto - Hook para operaciones HTTP
const { execute: loadData, isLoading } = useHttpOperation(async () => {
  return await EnhancedHttpService.get('/api/data');
});

// ❌ Incorrecto - Hook para operaciones locales
const { execute: processLocal } = useHttpOperation(() => {
  return processLocalData(); // No es HTTP
});
```

### 5. **Cuándo NO usar Skeleton Loading**

**❌ NO usar skeleton loading para:**

- **Datos mock/estáticos**: Si los datos están hardcodeados en el componente
- **Operaciones instantáneas**: Filtrado, búsqueda local, ordenamiento
- **Delays artificiales**: `setTimeout()` para simular carga
- **Datos en localStorage/sessionStorage**: Acceso instantáneo
- **Cálculos locales**: Procesamiento de datos ya cargados
- **Navegación entre páginas**: Cambios de ruta
- **Estados de UI**: Mostrar/ocultar elementos

**✅ SÍ usar skeleton loading para:**

- **Llamadas HTTP reales**: APIs, servicios web
- **Carga de archivos**: Imágenes, documentos
- **Operaciones de base de datos**: Consultas que toman tiempo
- **Servicios externos**: Autenticación, pagos
- **Operaciones asíncronas reales**: Que requieren tiempo de espera

```tsx
// ✅ Ejemplo correcto - HTTP real
const { execute: fetchUsers, isLoading } = useHttpOperation(async () => {
  return await EnhancedHttpService.get('/api/users');
});

// ❌ Ejemplo incorrecto - datos estáticos
const staticUsers = [{ id: 1, name: 'Juan' }];
const [fakeLoading, setFakeLoading] = useState(true);
// No hacer esto - los datos están disponibles inmediatamente
```

## 🧪 Testing

The system includes a comprehensive demo component at `src/components/LoadingDemo/LoadingDemo.tsx` that demonstrates all features:

- Global and component-specific loading
- HTTP service integration
- Async operation handling
- Error simulation and handling
- Skeleton loading patterns

## 🌙 Dark Mode Support

All components automatically support dark mode through Ionic's CSS variables:
- Skeleton elements adapt to dark theme colors with enhanced shimmer effects
- Glass-like loading overlays adjust transparency and blur for dark themes
- Progress indicators use theme-appropriate colors
- Error messages maintain proper contrast

## 📱 Mobile-First Optimization

The system is optimized for 60fps performance on mobile devices:

### Performance Features:
- **Hardware Acceleration**: GPU-accelerated animations with `transform: translateZ(0)`
- **Optimized Backdrop Filters**: Adaptive blur effects based on device capabilities
- **Smart Animation Reduction**: Respects `prefers-reduced-motion` for battery saving
- **Memory Management**: Efficient skeleton rendering with intersection observers

### Mobile Integration:
- **TabBar Compatibility**: Perfect integration with floating TabBar appearance
- **Header Glass Effects**: Seamless integration with translucent headers
- **Safe Area Handling**: Proper support for notches and home indicators
- **Touch Optimization**: 44px minimum touch targets for iOS, 48px for Android

### Responsive Design:
```css
/* Automatic mobile optimizations */
@media (max-width: 767px) {
  .modern-loading-content {
    padding: 1.5rem 1.25rem;
    max-width: min(280px, calc(100vw - 2rem));
  }
}

@media (max-width: 480px) {
  .modern-loading-backdrop {
    backdrop-filter: blur(8px) saturate(140%);
  }
}
```

### Platform-Specific Optimizations:
```tsx
// iOS-specific glass effects
const LoadingProvider = () => (
  <LoadingProvider
    useModernLoading={true}
    showGlobalLoading={true}
  >
    {children}
  </LoadingProvider>
);

// Android Material Design integration
const AndroidOptimized = () => (
  <LoadingButton
    className="android-material-button"
    rippleEffect={true}
  >
    Material Button
  </LoadingButton>
);
```

## 🔍 Debugging

Enable debug logging to monitor the system:
```tsx
LoadingService.configure({ enableDebugLogging: true });
EnhancedHttpService.configure({ enableDebugLogging: true });
```

Debug logs will appear in the console with prefixes:
- `[LoadingService]` - Loading state changes
- `[ErrorHandler]` - Error handling events
- `[EnhancedHttpService]` - HTTP request lifecycle
