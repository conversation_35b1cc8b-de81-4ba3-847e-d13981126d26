{"name": "agenda-familiar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest --passWithNoTests", "test:services": "jest --config jest.config.services.js --passWithNoTests", "test:watch": "jest --watch --passWithNoTests", "test:coverage": "jest --coverage --passWithNoTests", "test:services:coverage": "jest --config jest.config.services.js"}, "dependencies": {"@capacitor/android": "7.2.0", "@capacitor/app": "7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/core": "7.2.0", "@capacitor/haptics": "7.0.1", "@capacitor/inappbrowser": "^2.1.1", "@capacitor/ios": "7.2.0", "@capacitor/keyboard": "7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@tailwindcss/vite": "^4.1.8", "crypto-js": "^4.2.0", "ionicons": "^7.2.2", "oidc-client-ts": "^3.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-oidc-context": "^3.3.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4"}, "devDependencies": {"@capacitor/cli": "7.2.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jest": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-watch-typeahead": "^1.0.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "ts-jest": "^27.1.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}