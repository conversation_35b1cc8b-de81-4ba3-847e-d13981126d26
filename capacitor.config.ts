import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.santillana.agendafamiliar',
  appName: 'Agenda Familiar',
  webDir: 'dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    Browser: {
      // Configure Browser plugin for OIDC authentication
      presentationStyle: 'popover'
    },
    App: {
      // Handle deep links for OIDC callbacks
      deepLinkingEnabled: true,
      // Add explicit URL scheme for deep links
      url: 'capacitor://localhost'
    },
    CapacitorCookies: {
      // Enable native cookie management for session isolation
      enabled: true
    }
  }
};

export default config;
